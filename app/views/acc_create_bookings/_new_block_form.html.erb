<%= simple_form_for @acc_booking_header, :html => {:class => "form-horizontal"}, :url => add_block_acc_create_bookings_path, :remote => true, :method => :patch do |f|%>
    <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
      <h3>Add Additional Block</h3>
    </div>
    <div class="modal-body">
      <div class="well">
        <div class="row-fluid">
          <%= f.hidden_field :rfq_location_id, value: @acc_booking_header.rfq_location_id %>
          <%= f.simple_fields_for "acc_booking_blocks" do |block| %>
            <%= block.input :start_date, :as => :string, :input_html => {:class => "date_picker", value: nil} %>
            <%= block.input :end_date, :as => :string, :input_html => {:class => "date_picker acc_booking_block_start_date_slave", value: nil}%>
            <%= block.input :override_past_date, :as => :boolean %>
            <div class="control-group">
              <div class="controls">
                <%= f.submit "Generate Week blocks", :class => "btn btn-primary" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" data-dismiss="modal" aria-hidden="true" class="btn">Close</button>
    </div>
<% end %>

<script>
$(document).ready(function(){
  var startDatePicker = $(".date_picker");
  var endDatePicker = $(".acc_booking_block_start_date_slave");

  // Set end date dynamically based on start date
  startDatePicker.change(function () {
    var startDate = $(this).val();
    if (startDate) {
      try {
        var dateParts = startDate.split('-');
        if (dateParts.length === 3) {
          var day = parseInt(dateParts[0]);
          var monthName = dateParts[1];
          var year = parseInt(dateParts[2]);

          var monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                           'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          var month = monthNames.indexOf(monthName);

          if (month !== -1) {
            var parsedDate = new Date(year, month, day);
            var nextDay = new Date(parsedDate);
            nextDay.setDate(nextDay.getDate() + 1);

            var formattedDate = nextDay.getDate().toString().padStart(2, '0') + '-' +
                                monthNames[nextDay.getMonth()] + '-' +
                                nextDay.getFullYear();

            endDatePicker.val(formattedDate);

            // Refresh the end date picker to reflect the updated value
            endDatePicker.datepicker("update");
          }
        }
      } catch (e) {
        console.log("Error parsing date: " + e);
      }
    }
  });
});
</script>
