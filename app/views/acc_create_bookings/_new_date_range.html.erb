<div class="well" style="min-height:300px;">

  <%= simple_form_for @acc_booking_header,:html =>{:class=> "form-horizontal"}, :url => week_breakdown_acc_create_bookings_path do |f| %>
      <%= f.error_messages %>

      <div class="row-fluid">
        <div class="span6 offset2">
          <%= f.input :rfq_location_id, :as => :hidden %>
          <% if @rfq_location.rfq_training_centres.any? %>
            <p>As this Programme has more than one training centre please select from this list:</p>
            <%= f.input :rfq_training_centre_id, :label => "Training Centre", :as => :select, :collection => @rfq_location.rfq_training_centres.collect{|x| [x.centre_name, x.id]} %>
          <% end %>
          <%= f.simple_fields_for :acc_booking_blocks do |builder| %>
              <p>Please enter the dates of the first block. You will be able to add more later.</p>
              <%= builder.input :start_date, :as => :string, :input_html => {:class => "date_picker_btm input-block-level"}, :label => "Block start date" %>
              <%= builder.input :end_date, :as => :string, :input_html => {:class => "date_picker_btm input-block-level acc_booking_header_acc_booking_blocks_attributes_0_start_date_slave"}, :label => "Block end date" %>
              <%= builder.input :override_past_date, :as => :boolean %>
          <% end %>
          <div class="control-group">
            <div class="controls">
                <%= f.submit "Start Booking", :class => "btn btn-primary"%>
                <%= link_to "Cancel", acc_bookings_path(), :class => "btn" %>
             </div>
          </div>
        </div>
      </div>

  <% end %>
</div>

<script>
$(document).ready(function(){
  // Initialize the datepickers
  $(".date_picker_btm").datepicker({
    format: 'dd-M-yyyy', 
    autoclose: true, 
    orientation: 'bottom'
  });
  
  // Set initial defaults - show current month if no date is set
  var startDatePicker = $("#acc_booking_header_acc_booking_blocks_attributes_0_start_date");
  var endDatePicker = $(".acc_booking_header_acc_booking_blocks_attributes_0_start_date_slave");
  
  // If start date is empty, set datepicker to show current month
  if (!startDatePicker.val()) {
    var today = new Date();
    startDatePicker.datepicker("setViewDate", today);
  }
  
  // If end date is empty, set it to show current month too
  if (!endDatePicker.val()) {
    var today = new Date();
    endDatePicker.datepicker("setViewDate", today);
  }
  
  // Handle start date changes
  startDatePicker.on('changeDate', function (ev) {
    var this_picker = $(this);
    var this_picker_id = this_picker.attr("id");
    var slave_picker = $("." + this_picker_id + "_slave");
    
    if (ev.date) {
      // Set the minimum date for the end date picker
      slave_picker.datepicker("setStartDate", this_picker.val());
      
      // Navigate the end date picker to the same month as the start date
      slave_picker.datepicker("setViewDate", ev.date);
      
      // If end date is empty, set it to the next day to make it easier
      if (!slave_picker.val()) {
        var nextDay = new Date(ev.date);
        nextDay.setDate(nextDay.getDate() + 1);
        slave_picker.datepicker("setDate", nextDay);
      }
    }
  });
  
  // Also handle when the start date field is manually filled/changed
  startDatePicker.on('change', function() {
    var dateValue = $(this).val();
    if (dateValue) {
      try {
        // Parse the date in the format dd-M-yyyy
        var dateParts = dateValue.split('-');
        if (dateParts.length === 3) {
          var day = parseInt(dateParts[0]);
          var monthName = dateParts[1];
          var year = parseInt(dateParts[2]);
          
          // Convert month name to number
          var monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                           'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          var month = monthNames.indexOf(monthName);
          
          if (month !== -1) {
            var parsedDate = new Date(year, month, day);
            var slave_picker = $(".acc_booking_header_acc_booking_blocks_attributes_0_start_date_slave");
            
            // Set minimum date and navigate to same month
            slave_picker.datepicker("setStartDate", dateValue);
            slave_picker.datepicker("setViewDate", parsedDate);
            
            // If end date is empty, set it to the next day
            if (!slave_picker.val()) {
              var nextDay = new Date(parsedDate);
              nextDay.setDate(nextDay.getDate() + 1);
              slave_picker.datepicker("setDate", nextDay);
            }
          }
        }
      } catch (e) {
        console.log("Error parsing date: " + e);
      }
    }
  });
});

</script>
