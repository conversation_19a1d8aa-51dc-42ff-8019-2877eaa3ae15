<% if current_user.is_an_administrator? %>
  <%= link_to "RFQ: #{@rfq_location.rfq_request_id}", admin_rfq_request_path(@rfq_location.rfq_request), class: "btn pull-right", style:"margin-right: 30px", data: { cy: "rfq-link" } %>
  <%= link_to "Audits", audits_acc_booking_path(@booking), class: "btn pull-right", data: { cy: "audit-link" } %>
<% end %>

<h1 data-cy="booking-header"> <%= @booking.acc_booking_person.person_type %> <%= @booking.epa_booking ? "EPA" : "" %> Booking for
  <%= link_to @booking.acc_booking_person.name_for_table, acc_group_booking_acc_booking_person_path(@booking.acc_booking_person.acc_booking_header, @booking.acc_booking_person), data: { cy: "booking-person-link" } %>
  on Group Booking: <%= link_to @booking.acc_booking_person.acc_booking_header.reference, acc_group_booking_path(@booking.acc_booking_person.acc_booking_header), data: { cy: "group-booking-link" } %>
</h1>

<div class="row-fluid">
  <div class="span6">
    <div class="well">
      <% if @booking.virtual_flag? %>
        <div id='booking-info' data-cy="virtual-booking-info">
          <%= render :partial => "acc_create_bookings/virtual_details", :locals => { :abh => @booking.acc_booking_header } %>
          <h3>Booking Special
            Requirements <%= link_to "edit", spec_req_acc_booking_path(@booking), remote: true, class: "btn btn-small pull-right", data: { cy: "edit-special-requirements" } %></h3>
          <div id="spec-req" class="well">
            <%= simple_format(@booking.special_requirements) %>
          </div>
        </div>
      <% else %>
        <h3>Booking Details</h3>
        <% if !@booking.has_hotel_confirmed_if_required %>
          <h4 style="color:red;">Hotel must confirm!</h4>
          <% if @booking.hotel_confirmed_at.present? %>
            <div class="alert alert-success">
              <p>This bookings room availability has been confirmed by the hotel</p>
            </div>
          <% elsif @booking.hotel_declined_at.present? %>
            <div class="alert alert-danger">
              <p>This bookings room availability has been declined by the hotel</p>
            </div>
          <% else %>
            <div class="alert alert-warning">
              <p>This bookings room availability has not yet been processed by the hotel</p>
            </div>
          <% end %>
        <% end %>
        
        <div id='booking-info' data-cy="booking-info">
          <%= render :partial => "info" %>
        </div>
      <% end %>
    </div>

    <div class="well">
      <div class="alert alert-error" id="error-message" style="display:none" data-cy="error-message"></div>
      <% if @problem.present? %>
        <div class="alert alert-error">
          Sorry the additional payment needed could not be taken because: <%= @problem %> . Apprentice team are being
          sent an email in order that this can be resolved
        </div>
      <% end %>

      <% if @booking.virtual_flag? %>
        <% if @booking.cancelled? %>
          <%= button_to("Reinstate Booking", undo_booking_cancellation_acc_booking_path(@booking), :class => "btn", :method => :patch, :id => "undo-cancellation-button", data: { cy: "reinstate-booking-button" }) %>
          <br/>
        <% else %>
          <%= link_to("Cancel Booking", cancel_booking_form_acc_booking_path(@booking), :class => "btn btn-danger", :remote => true, :id => "cancel-button", data: { cy: "cancel-booking-button" }) %>
          <br/><br/>
        <% end %>
        <%= link_to "Return to Group", acc_group_booking_path(@booking.acc_booking_person.acc_booking_header), :class => "btn", data: { cy: "return-to-group-button" } %>
      <% else %>
        <div style="display:inline-block">
          <%# The empty {} at the end of these is to stop the text showing if the condition is false %>
          <%= link_to_if(!@booking.cancelled?, "Edit Booking", edit_acc_booking_path(@booking), :class => "btn btn-primary", data: { cy: "edit-booking-button" }){} %>
          <%= link_to_if(!@booking.cancelled?,"Cancel Booking", cancel_booking_form_acc_booking_path(@booking), :class => "btn btn-danger", :remote => true, :id => "cancel-button", data: { cy: "cancel-booking-button" }){} %>
        </div>
        <%= link_to("Confirm Booking", confirm_booking_acc_booking_path(@booking), :class => "btn btn-info", data: { cy: "confirm-booking-button" }) if @booking.confirmable? && !@booking.card_pay? %>
        <%= link_to("Confirm Booking Via Credit Account", confirm_booking_acc_booking_path(@booking, :pay_via_credit => true), :class => "btn btn-info", data: { cy: "confirm-booking-credit-button" }) if @booking.confirmable? && @has_credit_account %>
        <br/><br/>
        <%= link_to "Return to Group", acc_group_booking_path(@booking.acc_booking_person.acc_booking_header), :class => "btn", data: { cy: "return-to-group-button" } %>
        <%= link_to "Raise Issue", new_acc_issue_path(:"rfq_location[id]" => @booking.acc_booking_header.rfq_location.id, :acc_booking_id => @booking.id), :class => 'btn btn-inverse', :remote => true, :data => { :confirm => 'Are you sure?', cy: "raise-issue-button" } if @booking.acc_booking_person.rfq_learner.present? && @booking.acc_booking_header.rfq_location.issue_viewable?(current_user) %>
      <% end %>

      <%= link_to "Undo Confirmation", undo_booking_confirmation_acc_booking_path(@booking), class: 'btn btn-primary', :method => :patch, :data => { :confirm => "Are you sure?", cy: "undo-confirmation-button" } if @current_user.is_an_administrator? && @booking.confirmed? && !@booking.booking_payments.charged.any? && (@current_user.email == '<EMAIL>' || @current_user.email == '<EMAIL>') %>

      <% if !Rails.env.production? && @current_user.is_an_administrator? %>
        <h2> Secret Options (Should not appear on live!) </h2>
        <% if !@booking.hotel_confirmed_at.present? && !@booking.subsistence_only?%>
          <%= link_to "Hotel Confirm Booking",  confirm_booking_for_hotel_acc_booking_path(@booking), :class => 'btn btn-primary ', :method => :patch, :data => { :confirm => "Are you sure?", cy: "hotel-confirm-booking-button" } %>
          <%#= link_to "Hotel Decline Booking", decline_supplier_acc_booking_path(@booking), :class => 'btn btn-primary ', :method => :patch, :data => { :confirm => "Are you sure?" } %>
        <% end %>
        <% if @booking.cancelled_at %>
          <%= link_to "Undo Cancellation", undo_booking_cancellation_acc_booking_path(@booking), class: 'btn btn-primary', :method => :patch, data: { cy: "undo-cancellation-button" } %>
        <% else %>
          <%= link_to "Cancel Booking no validation + refund", cancel_booking_acc_booking_path(@booking, acc_booking: {cancellation_wish: "1", cancellation_reason: 'System override cancel', refund_override: true}), class: 'btn btn-primary', :method => :patch, data: { cy: "cancel-booking-no-validation-button" } %>
        <% end %>
        <%= link_to "Send VAT Summary", resend_stripe_receipt_acc_booking_path(@booking, vat: true, bypass_date: true), class: 'btn btn-primary', remote: true, data: { cy: "send-vat-summary-button" } %>
        <br/>

        <% client_contact = @rfq_location.rfq_request.contact%>
        <% hotel_contact = @booking.try(:hotel).try(:reservations_contact) %>

        <% if client_contact && client_contact.user.present? && session[:masquerader_id].blank? %>
          <%= form_for :masquerade_as, :url => admin_masquerades_path, :class => 'form form-search' do |f| %>
            <%= f.hidden_field :id, :value => client_contact.user.id %>
            <%= f.submit "Act As #{client_contact.full_name} (RFQ Contact)", :class => 'btn btn-mini btn-danger', data: { cy: "act-as-rfq-contact-button" } %>
          <% end %>
        <% elsif session[:masquerader_id].present? %>
          <p>Already Acting As</p>
        <% end %>

        <% if hotel_contact && hotel_contact.user.present? && session[:masquerader_id].blank? %>
          <%= form_for :masquerade_as, :url => admin_masquerades_path, :class => 'form form-search' do |f| %>
            <%= f.hidden_field :id, :value => hotel_contact.user.id %>
            <%= f.submit "Act As #{hotel_contact.full_name} (Hotel Reservations)", :class => 'btn btn-mini btn-danger', data: { cy: "act-as-hotel-reservations-button" } %>
          <% end %>
        <% elsif session[:masquerader_id].present? %>
          <p>Already Acting As</p>
        <% end %>

      <% end %>

    </div>
  </div>

  <div class="span6">
    <div id="cancellation-details" data-cy="cancellation-details">
      <%= render :partial => "cancellation_details" if @booking.cancelled? %>
    </div>
    <div id="confirmation-details" data-cy="confirmation-details">
      <%= render :partial => "confirmation_details" %>
    </div>
    <div>
      <% if @booking.epa_training_datetimes.present? %>
        <div class="well">
          <table class="table table-bordered">
            <tr>
              <th>End Point Assessment Date</th>
              <th>End Point Assessment Time</th>
            </tr>
            <% @booking.epa_training_datetimes.each do |training| %>
              <tr>
                <td> <%= training["date"] %> </td>
                <td> <%= training["time"] %> </td>
              </tr>
            <% end %>
          </table>
        </div>
      <% end %>
      <div class="well">
        <h3>Notes</h3>
        <table class="table table-bordered">
          <% if current_user.is_an_administrator? %>
            <tr>
              <th>
                ServAce Note
              </th>
              <td>
                <%= @booking.hg_note ? (simple_format @booking.hg_note) : 'No Note' %>
              </td>
            </tr>
          <% elsif current_user.is_a_client? %>
            <tr>
              <th>
                Client Note
              </th>
              <td>
                <%= simple_format @booking.client_note %>
              </td>
            </tr>
          <% end %>
        </table>
      </div>
      <% if !@booking.virtual_flag? %>
        <% if !@booking.subsistence_only? %>
          <div class="well">
            <h3>Stays</h3>
            <p>If you wish to change the days you are staying please edit the booking.</p>
            <%= render :partial => "stays" %>
          </div>

          <% if @booking.acc_booking_adjusts.any? %>
            <div class="well">
              <h3>Adjustments</h3>
              <%= render :partial => "adjustments" %>
            </div>
          <% end %>
        <% else%>
          <div class="well">
            <table class="table table-bordered">
              <thead>
                  <tr>
                    <th>Training Start Date</th>
                    <th>Training End Date</th>
                  </tr>
              </thead>
              <tbody>
                  <% @booking.training_dates.order(:start_date).each do |week| %>
                  <tr>
                    <td><%= week.get_earliest_date_in_week&.to_fs(:uk) %></td>
                    <td><%= week.get_latest_date_in_week&.to_fs(:uk) %></td>
                  </tr>
                  <% end %>
              </tbody>
            </table>
          </div>
        <% end %>
      <% end %>
      <% if @booking.acc_booking_person.rfq_learner.present? && @booking.acc_booking_header.rfq_location.issue_viewable?(current_user) %>
        <div class="well">
          <h3>Issues for this learner</h3>
          <div id='booking-issues-table'>
            <%= render :partial => "acc_issues/booking_table", :locals => { :issues => @booking.acc_booking_person.rfq_learner.acc_issues } %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<div id="acc-modal" class="modal hide fade" data-cy="acc-modal">

</div>

<script type="text/javascript">
    $(document).ready(function () {
        $(".tooltippable span").tooltip({placement: 'right', html: 'true'});
        $(".poppable span").popover({trigger: 'click', placement: 'top', html: 'true'});
    });
</script>
<%= render :partial => 'acc_issues/remove_learners_script' %>

<%= vite_javascript_tag "payment-management", "info-display" %>