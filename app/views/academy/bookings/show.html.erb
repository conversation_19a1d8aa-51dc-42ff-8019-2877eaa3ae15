<div id="academy-bookings">
    <academy-booking
        :booking="<%=@booking.to_json(:include => [:hotel_confirmed_by, :hotel_declined_by, :cancelled_by]) %>"
        :payments="<%= @booking_payments.to_json %>"
        :attendees="<%= @booking_attendees.to_json %>"
        :hotel="<%= @hotel.to_json %>"
        :rfq-request="<%= @rfq_request.to_json %>"
        :user-type="<%= @current_user.user_type.to_json %>"
    />
</div>

<%= vite_javascript_tag 'academy-bookings' %>