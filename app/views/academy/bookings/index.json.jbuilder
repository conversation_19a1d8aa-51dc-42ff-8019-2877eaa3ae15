
json.bookings @bookings.each do |booking|
  confirmation_info = []
  if booking.hotel_confirmed_at.present? 
    confirmation_info << 'Confirmed at: ' + booking.try(:hotel_confirmed_at)&.to_fs(:uk) + ' by: ' + booking.hotel_confirmed_by_name.to_s
  end
  if booking&.hotel_declined_at.present?
    confirmation_info << 'Declined at: ' + booking.try(:hotel_declined_at)&.to_fs(:uk) + ' by: ' + booking.hotel_declined_by_name.to_s + ' due to ' + booking.hotel_decline_reason.to_s
  end
  if booking.cancelled_at.present?
    confirmation_info << 'Cancelled at: ' + booking.try(:cancelled_at)&.to_fs(:uk) + ' by: ' + booking.cancelled_by_name.to_s + ' due to ' + booking.cancellation_reason.to_s
  end
  row_class = if booking&.hotel_declined_at.present?
    'declined'
  elsif booking.cancelled_at.present?
    'cancelled'
  elsif booking&.hotel_confirmed_at.present?
    'confirmed'
  else #unprocessed
    'unconfirmed'
  end
  json.id booking.id
  json.booker_name "#{booking.booker_forename} #{booking.booker_surname}".strip
  json.booker_email booking.booker_email
  json.booker_telephone booking.booker_telephone
  json.attendees booking.booking_attendees.not_cancelled
  json.created_at booking.created_at
  json.reservation_number booking.reservation_number
  json.special_requirements booking.special_requirements
  json.programme_name booking.rfq_location.rfq_request.rfq_programme.name
  json.hotel_name booking.hotel.name
  json.check_in booking.check_in
  json.check_out booking.check_out
  json.stay_cost booking.total_cost
  json.payment_method booking.payment_method
  # json.name booking&.booker_forename + " " + booking&.booker_surname
  json.email booking.booker_email
  # json.telephone booking.booker_telephone
  json.hotel_confirmed_at booking.hotel_confirmed_at
  json.hotel_confirmed_by booking.hotel_confirmed_by_name
  json.hotel_declined_at booking.hotel_declined_at
  json.hotel_declined_by booking.hotel_confirmed_by_name
  json.cancelled_at booking.cancelled_at
  json.cancelled_by booking.cancelled_by_name
  json.total_cost booking.total_cost
  json.confirmation_info confirmation_info
  json.row_class row_class
end
# TODO obviously the long chain of calls to get the business_unit, programme_name etc is not ideal and will be refactored

json.pagy @pagy
