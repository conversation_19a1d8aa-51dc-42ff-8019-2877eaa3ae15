<% completed = af.completed_task_check%>
<% if completed %>
  <% row_class = "success" %>
<% elsif completed == false %>
  <% row_class = "error" %>
<% else %>
  <% row_class = "" %>
<% end %>
<tr id="row-<%= af.id %>" class="<%= row_class %>">
  <td><%= link_to af.id, admin_organisation_admin_fee_path(af.organisation_id, af) %></td>
  <td>
    <% if af.respond_to? :org_name %>
      <%= link_to af.org_name, admin_organisation_path(af.organisation_id) %>
    <% else %>
      <%= link_to af.organisation.name, admin_organisation_path(af.organisation_id) %>
    <% end %>
    <%= link_to "Task", edit_admin_task_path(af.task), :class => "btn btn-inverse btn-small pull-right", :target => "_BLANK" if af.task.present? %>
  </td>
  <td><%= af.created_at.to_date.to_fs %></td>
  <td><%= af.expires_on %></td>
  <td><%= af.chase_date %></td>
  <td>
    <div class="pull-right">

      <%= link_to "Show", admin_organisation_admin_fee_path(af.organisation_id, af), :class => "btn btn-success btn-small " %>
      <%= link_to "Edit", edit_admin_organisation_admin_fee_path(af.organisation_id, af), :class => "btn btn-warning btn-small" %>
      <%= link_to "Clone", clone_admin_organisation_admin_fee_path(af.organisation_id, af), :class => "btn btn-primary btn-small" %>
      <%= link_to "Delete", admin_organisation_admin_fee_path(af.organisation_id, af), :remote => true, :method => :delete, :data => {:confirm => 'Are you sure'}, :class => "btn btn-danger btn-small" %>

    </div>
  </td>
</tr>
