<h1>Booking Request</h1>
<p>
  <font face="Helvetica" color="#4a4a4c">
    Thank you for your Academy Hub accommodation enquiry. This email is to confirm your booking request. 
    Confirmation of your stay will be sent to you within 1 hour. (Please note: This is not a confirmed 
    booking at this stage and confirmation will follow.)
  </font>
</p>
<table style="font-family:'Helvetica'!important;font-size: 13px!important; line-height: 1.5!important;">
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Booking Number</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @booking.id %> </font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Hotel Name</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @hotel.name %></font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Hotel Address</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @hotel.primary_location.full_address %></font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Hotel Contact Number</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @hotel.reservations_contact.telephone %></font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Reservation Number</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @booking.reservation_number %></font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Guest Name(s)</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @booking.attendees_name_list %></font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Check-in</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @booking.check_in&.to_fs(:uk) %></font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Check-out</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= @booking.check_out&.to_fs(:uk) %></font></td>
  </tr>

  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Payment Terms</font></th>
    <% payment_message = (@booking.payment_method != "Hotel invoice to client" && @booking.payment_method != "Invoice to ServAce") ? @booking.payment_method : "Pre-paid by Organisation"%>
    <td><font face="Helvetica" color="#4a4a4c"><%= payment_message %></font></td>
  </tr>

  <% if @booking.payment_method == 'Business Account' %>
      <tr>
        <th><font face="Helvetica" color="#4a4a4c">Account Number</font></th>
        <td><font face="Helvetica" color="#4a4a4c"><%= @booking.account_number %></font></td>
      </tr>
  <% end %>
    <th><font face="Helvetica" color="#4a4a4c">Total Cost</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= number_to_currency(@booking.total_cost / 100, :unit => "&pound;".html_safe)%> inc vat</font></td>
  </tr>
  <tr>
    <th><font face="Helvetica" color="#4a4a4c">Special requests</font></th>
    <td><font face="Helvetica" color="#4a4a4c"><%= simple_format(@booking.special_requirements) %></font></td>
  </tr>
</table>
<br>
<font face="Helvetica" color="#4a4a4c">
</font>
