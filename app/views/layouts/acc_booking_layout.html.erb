<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <link rel="icon" type="image/png" href="/servace-favicon.png">
  <link rel="manifest" href="/manifest.json"><meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">

  <%= csrf_meta_tags %>

  <!-- Le styles -->
  <%= stylesheet_link_tag "/bootstrap2/css/bootstrap.min.css" %>
  <%= stylesheet_link_tag    "application" %>
  <%= javascript_include_tag "application" %>
  <%= javascript_include_tag "/bootstrap2/js/bootstrap.min.js" %>






  <%= yield :head %>
  <script type="text/javascript" src="/javascripts/wymeditor/jquery.wymeditor.min.js"></script>
</head>

<body class="<%= "admin_interface" if @admin_mode %> <%= @body_class %> <%= (params[:action]).parameterize %> <%= (params[:controller]).parameterize %>">
<div id="top-nav">
  <div class="container">
    <ul>
    </ul>
    <div id="header-right">
    </div>
  </div>
</div>

<% if flash.present?  %>
<div class="row-fluid">
  <div class="span12">



    <%- flash.each do |name, msg| -%>

        <% class_name = case name
                        when :notice
                          'alert alert-block alert-info'
                        when  :warning
                           'alert alert-block'
                        when :error
                          'alert alert-block alert-error'
                        else
                          'alert alert-block alert-info'
                        end

         %>
         <% if msg.is_a?(String) %>
          <div class="<%= class_name %>" id=<%=  "flash_#{name}" %>  >
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <%= msg %>
          </div>
        <% end %>
    <% end %>


  </div>
  </div>
<% end %>


<div id="bg-acc-booking">

        <%= yield %>

</div>
<!-- Le javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<%= render "layouts/google_analytics" %>
<% if Rails.env=="development" %>
    <h3>Session</h3>
    <%= session.inspect  %>
    <h3>Params</h3>
    <%= params.inspect  %>
<% end %>


</body>
</html>
