<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <link rel="icon" type="image/png" href="/servace-favicon.png">
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#ffffff">


  <%= csrf_meta_tags %>
  <%= yield :geo %>
  <!-- Le styles -->
  <%= stylesheet_link_tag "/bootstrap2/css/bootstrap.min.css" %>
  <%= stylesheet_link_tag "application" %>

  <%= stylesheet_link_tag "print", :media => "print" %>

  <%= javascript_include_tag "application" %>
  <%= vite_javascript_tag "stimulus" %>
  <%= javascript_include_tag "/bootstrap2/js/bootstrap.min.js" %>

  <script type="text/javascript" src="/javascripts/wymeditor/jquery.wymeditor.min.js"></script>
  <link href='https://fonts.googleapis.com/css?family=Rokkitt' rel='stylesheet' type='text/css'>

  <%= yield :head %>
  
</head>

<body class="<%= "admin_interface" if @admin_mode %> <%= @body_class %> <%= (params[:action]).parameterize %> <%= (params[:controller]).parameterize %>">

<script type="text/javascript" src="https://secure.leadforensics.com/js/113837.js" data-turbolinks-permanent></script>
<noscript><img alt="" src="https://secure.leadforensics.com/113837.png" style="display:none;"/></noscript>
<div id="bg">
  <% if current_user %>
    <%= render 'layouts/main_top_nav' %>
  <% else %>
    <div id="servace-nav">
      <servace-nav />
    </div>
    <%= vite_javascript_tag "servace-nav" %>
    <%= vite_javascript_tag "servace-footer" %>
  <% end %>

  <div class="container">

    <div id="agree-cookies" class="alert alert-info" style="display:none">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      This website uses cookies. Cookies remember you so we can give you a better service online. By using this website
      or closing this message you are agreeing to our <a href="/cookies.html">cookies policy</a> .
    </div>
    <% if @acc_admin_mode %>
      <%= render 'layouts/admin_acc_nav' %>
    <% elsif @acc_client_mode %>
      <%= render 'layouts/client_acc_nav' %>
    <% elsif @acc_supplier_mode %>
      <%= render 'layouts/supplier_acc_nav' %>
    <% end %>
    <% if current_user && session[:masquerader_id].present? %>
      <%= development_ribbon position: :right, color: :orange, text: "Acting as: #{current_user.full_name_or_email}" %>
    <% end %>

    <% next_reboot = Rails.cache.fetch('next_reboot') %>
    <% time = Time.zone.now %>
    <% if next_reboot.try(:>, time) %>
      <div id="reboot-timer" class="alert alert-everyone tooltippable">
        <button type="button" style="margin-right:29px;" class="close" title="click to dismiss" data-dismiss="alert">&times;</button>
        <h3 style="text-align:center;">Maintenance Due</h3>

        <% the_diff = (next_reboot.to_i - time.to_i) %>
        Please note: the system is going to be updated
        <% t_array = the_diff.divmod(60) %>
        <span id='reboot-clock'><%= sprintf("in %d minutes and %d seconds", t_array[0], t_array[1]) %></span>,
        and will be in maintenance mode for a few minutes. We'll be as quick as we can.
        <%= hidden_field_tag :next_reboot, Rails.cache.fetch('next_reboot').strftime("%Y-%m-%dT%H:%M:%S%z") %>
      </div>
      <%= render :partial => 'layouts/header_row' unless @admin_mode %>
    <% end %>
    
    <%= render :partial => "layouts/flash_and_dropfile" if @acc_admin_mode || @acc_client_mode || @acc_supplier_mode %>

    <% if user_signed_in? && @current_chain_or_hotel.present? && @current_chain_or_hotel.is_a?(Hotel) && @current_chain_or_hotel.terms_and_conditions_accepted.present? && @current_chain_or_hotel.quotations.outstanding.not_past.any? %>
      <div class="row-fluid">
        <div class="alert span12">

          <h1><span class="glyphicon glyphicon-warning-sign" style='font-size:35px;color:#B7355F;padding:10px'></span>You
            have quotations outstanding, please follow
            the <%= "link".pluralize(@current_chain_or_hotel.quotations.not_past.outstanding.count) %> and complete
            the <%= "form".pluralize(@current_chain_or_hotel.quotations.not_past.outstanding.count) %>: </h1>
          <ul class="supplier-quotation-alerts">

            <% @current_chain_or_hotel.quotations.not_past.outstanding.order_by_quote_deadline.each do |q| %>
              <li><%= link_to_quotation_with_deadline(q) %></li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <% if user_signed_in? && @current_chain_or_hotel.present? && @current_chain_or_hotel.is_a?(Hotel) && (@current_chain_or_hotel.responses.outstanding.any? || @current_chain_or_hotel.responses.in_progress.any?) %>
      <div class="row-fluid">
        <div class="alert span12">

          <h1><span class="glyphicon glyphicon-warning-sign" style='font-size:35px;color:#B7355F;padding:10px'></span>You
            have tasks outstanding, please see below: </h1>
          <ul class="supplier-quotation-alerts">
            <% if @current_chain_or_hotel.apprentice_responses.outstanding.any? %>
              <li> You have <%= @current_chain_or_hotel.apprentice_responses.outstanding.count %> Apprentice RFQ
                response(s) outstanding and awaiting your quote. Please
                visit <%= link_to "Dashboard", rfq_supplier_dashboard_path %> to view and complete.
              </li>
            <% end %>
            <% if @current_chain_or_hotel.adult_responses.outstanding.any? %>
              <li> You have <%= @current_chain_or_hotel.adult_responses.outstanding.count %> Adult RFQ response(s)
                outstanding and awaiting your quote. Please
                visit <%= link_to "Dashboard", acc_stop_supplier_dashboard_path %> to view and complete.
              </li>
            <% end %>
            <% if @current_chain_or_hotel.apprentice_responses.in_progress.any? %>
              <li> You have <%= @current_chain_or_hotel.responses.in_progress.count %> Confirmed Apprentice Programme(s)
                with outstanding tasks. Please visit <%= link_to "Dashboard", rfq_supplier_dashboard_path %> to view and
                complete.
              </li>
            <% end %>
            <% if @current_chain_or_hotel.adult_responses.in_progress.any? %>
              <li> You have <%= @current_chain_or_hotel.responses.in_progress.count %> Confirmed Adult Programme(s) with
                outstanding tasks. Please visit <%= link_to "Dashboard", acc_stop_supplier_dashboard_path %> to view and
                complete.
              </li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <%= yield :main_navigation %>

    <%= yield %>
  </div>
  <!-- /container -->

  <% if !user_signed_in? %> 
  
    <div id="servace-footer">
      <servace-footer />
    </div>
  <% end %>

</div>
<!-- Le javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->

<%= render "layouts/google_analytics" %>
<% if Rails.env == "development" %>
  <div style="margin:10px;clear:both;">
    <h3>Session</h3>
    <%= ap session %>
    <h3>Params</h3>
    <%= ap params.permit! %>
  </div>
  <hr/>
<% end %>
</body>
</html>


