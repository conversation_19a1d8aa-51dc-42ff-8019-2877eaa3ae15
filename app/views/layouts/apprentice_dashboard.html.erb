<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <link rel="icon" type="image/png" href="/servace-favicon.png">
  <link rel="manifest" href="/manifest.json">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">


  <%= stylesheet_link_tag "application_new" %>
  <%= stylesheet_link_tag "bootstrap_and_overrides"%>

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-KyZXEAg3QhqLMpG8r+8fhAXLRk2vvoC2f3B09zVXn8CA5QIVfZOJ3BCsw2P0p/We" crossorigin="anonymous">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-U1DAWAznBHeqEIlVSCgzq+c9gqGAJn5c/t99JyeKa9xxaYpSvHU5awsuZVVFIhvj" crossorigin="anonymous"></script>

  <%= csrf_meta_tags %>

  <%= yield :head %>
  <link href='https://fonts.googleapis.com/css?family=Rokkitt' rel='stylesheet' type='text/css'>

</head>

<body class="<%= "admin_interface" if @admin_mode %> <%= @body_class %> <%= (params[:action]).parameterize %> <%= (params[:controller]).parameterize %>">


<%= render 'layouts/navigation/newui/main_top_nav' %>

<%# TODO add back others once a new version created %>
<% if @acc_admin_mode %>
  <%= render 'layouts/navigation/newui/app_admin_nav_new' %>
<% elsif @acc_client_mode %>
  <%= render :partial => "layouts/navigation/newui/app_client_nav_new", locals: {current_tab: "My Apprentice Hub", selected_sub_tab: "Reports Dashboard", sub_sub_menu: "", selected_sub_sub_tab: ""} %>
<% elsif @acc_supplier_mode %>
  <%= render :partial => "layouts/navigation/newui/app_supplier_nav_new" %>
<% end %>

<% if current_user && session[:masquerader_id].present? %>
  <%= development_ribbon position: :right, color: :orange, text: "Acting as: #{current_user.full_name_or_email}" %>
<% end %>

<script type="text/javascript" src="https://secure.leadforensics.com/js/113837.js" data-turbolinks-permanent></script>
<noscript><img alt="" src="https://secure.leadforensics.com/113837.png" style="display:none;"/></noscript>

<div style="background: #fff;">
  <div class="container-fluid">

<!--    <div id="apprentice-test">-->
<!--      <apprentice-test>Hello, replace me</apprentice-test>-->
<!--    </div>-->


    <div id="agree-cookies" class="alert alert-info" style="display:none">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      This website uses cookies. Cookies remember you so we can give you a better service online. By using this website
      or closing this message you are agreeing to our <a href="/cookies.html">cookies policy</a> .
    </div>

    <%# <%= render :partial => "layouts/flash_and_dropfile" if @acc_admin_mode || @acc_client_mode || @acc_supplier_mode %>

    <%# TODO as the pin tabs, move to partial %>

    <%= yield %>

    <%# <%= render :partial => 'layouts/footer' unless @admin_mode %>

  </div>
</div>


</body>
</html>

<style>
    .navbar-custom {
        background-color: #004F59;
    }
</style>

