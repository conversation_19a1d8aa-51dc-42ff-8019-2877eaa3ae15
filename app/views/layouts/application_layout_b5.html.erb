<!DOCTYPE html>
<html lang="en">
<head>
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">

  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <link rel="icon" type="image/png" href="/servace-favicon.png">
  <link rel="manifest" href="/manifest.json">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">

<!--  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" crossorigin="anonymous">-->

  <%#= javascript_include_tag 'application_new' %>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
  <%# <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script> %>
<!--  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>-->
<!--  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>-->
<!--  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.8.0/js/bootstrap-datepicker.min.js" integrity="sha256-tW5LzEC7QjhG0CiAvxlseMTs2qJS7u3DRPauDjFJ3zo=" crossorigin="anonymous"></script>-->



  <%= csrf_meta_tags %>

  <!-- Le styles -->
  <%#= stylesheet_link_tag "application_new" %>

  <%= yield :head %>
<!--  <link href='https://fonts.googleapis.com/css?family=Rokkitt' rel='stylesheet' type='text/css'>-->

</head>

<%# TODO add back once a new version created %>
<%= render 'layouts/navigation/newui/main_top_nav' %>

<% if current_user && session[:masquerader_id].present? %>
  <%= development_ribbon position: :right, color: :orange, text: "Acting as: #{current_user.full_name_or_email}" %>
<% end %>

<div class="row justify-content-center">
  <%- flash.each do |name, msg| -%>
    <% class_name = case name
                    when 'notice'
                      'alert alert-block alert-info'
                    when 'warning'
                      'alert alert-block'
                    when 'error'
                      'alert alert-block alert-error'
                    else
                      'alert alert-block alert-info'
                    end

    %>
    <% if msg.is_a?(String) %>
      <div class="<%= class_name %>" id=<%= "flash_#{name}" %>>
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <br/>
        <%= msg %>
      </div>
    <% end %>
  <% end %>
</div>

<script type="text/javascript" src="https://secure.leadforensics.com/js/113837.js" data-turbolinks-permanent></script>
<noscript><img alt="" src="https://secure.leadforensics.com/113837.png" style="display:none;"/></noscript>
<body id="bg-acc-booking">
<div class="container">

  <div id="agree-cookies" class="alert alert-info" style="display:none">
    <button type="button" class="close" data-dismiss="alert">&times;</button>
    This website uses cookies. Cookies remember you so we can give you a better service online. By using this website
    or closing this message you are agreeing to our <a href="/cookies.html">cookies policy</a> .
  </div>
  <% if current_user && current_user.is_a_supplier? %>
    <%= render :partial => 'layouts/navigation/newui/app_supplier_nav_new' %>
  <% end %>
  <%= yield %>

  <div class="row">
    <div class="col-md-3">
      <ul class="logos">
        <li><a href="https://www.servace.co.uk"><%= image_tag("/assets/imgphase3/servace-logo.png") %></a></li>
      </ul>
    </div>

    <div class="col-md-3 offset-6">
      <ul class="logos">
        <li><a href="https://www.servace.co.uk"><%= image_tag("/assets/imgphase3/servace-logo.png") %></a></li>
      </ul>
    </div>
  </div>
  <div class="row mb-4">
    <div class="col col-md-4 offset-4 text-center">
      Powered by <a href="https://www.servace.co.uk/" target="_blank">Servace</a>
    </div>
  </div>
</div>

</body>
</html>
