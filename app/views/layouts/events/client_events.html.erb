<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <meta name="robots" content="noindex, nofollow">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <%= csrf_meta_tags %>

  <!-- Le styles -->
  <%= stylesheet_link_tag "/bootstrap2/css/bootstrap.min.css" %>
  <%= stylesheet_link_tag "application" %>
  <%= stylesheet_link_tag "print", media: 'print' %>
  <%= javascript_include_tag "application" %>
  <%= javascript_include_tag "/bootstrap2/js/bootstrap.min.js" %>
  <script src="/jquery.payment.js"></script>





  <%= yield :head %>
</head>

<body class="<%= @event.event_code rescue "" %> events-ext_bookings <%= "admin_interface" if @admin_mode %> <%= @body_class %> <%= (params[:action]).parameterize %> <%= (params[:controller]).parameterize %>">
<div id="top-nav">
  <div class="container">
    <% unless @current_step.blank? || @current_step == 'step1' || @current_step == 'step6'%>
        <%= link_to '&laquo; Back'.html_safe, booking_back_step_path(@current_step, @event), :class=>"btn btn-mini btn-success btn-back", :style=>"float:left;margin: 5px 5px 0 0" %>
    <% end %>
    <% if @body_class == "as"%>
        <div id="header-right">
          <ul>
            <li>&nbsp;</li>
          </ul>
        </div>
    <% else %>

        <% unless @hide_header %>
            <ul>
                <li><%#= link_to "EventStop: Register another delegate. Call #{MAIN_PHONE} for assistance quoting referece 'harrogate2015'.", "/events/harrogate2015" %></li>
            </ul>
            <div id="header-right">
              <%= link_to "Find existing booking", search_bookings_path, :class => "btn btn-mini find-existing-booking" %>
            </div>


        <% end %>
    <% end %>
  </div>
</div>
<div id="bg">
  <div class="container">

    <br/>

    <div id="agree-cookies" class="alert alert-info" style="display:none">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      This website uses cookies. Cookies remember you so we can give you a better service online. By using this website or closing this message you are agreeing to our <a href="/cookies.html">cookies policy</a> .
    </div>


    <%#= yield :main_navigation %>
    <% flash.each do |k,v| %>
        <% k = k.to_s %>
        <% case k
             when 'alert'
               'error'
             when 'notice'
               'info'
             else
               k
           end %>
        <div class="alert alert-<%= k %> fade in">
          <button type="button" class="close" data-dismiss="alert">&times;</button>
          <%= v %>
        </div>
    <% end %>

    <%= render 'events/event_booking_steps' if @current_step.present? %>
    <br>
    <%= yield %>

    <hr/>
    <div class="row-fluid">
      <div class="span12">
        <% if @body_class == "as"%>
            <ul class="logos">
              <li><a href="https://www.servace.co.uk"><%= image_tag("/assets/events/hg-logo-long.png") %></a></li>
              <br/><br/>
              <li>Powered by <a href="https://www.servace.co.uk/" target="_blank">Servace</a></li>
            </ul>
            <div class="pull-right">
              <%= image_tag "imgphase3/Accommodation-hub.jpeg", :style => "max-height: 70px;" %>
            </div>
        <% else %>
            <ul class="logos">
              <li><a href="https://www.servace.co.uk"><%= image_tag("/assets/events/hg-logo-long.png") %></a></li>
            </ul>
            <div class="pull-right">
              Powered by <a href="https://www.servace.co.uk/" target="_blank">Servace</a>
            </div>
        <% end %>
      </div>
    </div>

    <br/>


  </div>
  <!-- /container -->
</div>
<!-- Le javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<%= render "layouts/google_analytics" %>
<% if Rails.env=="development" %>
    <h3>Session</h3>
    <%= session.inspect  %>
    <h3>Params</h3>
    <%= params.inspect  %>
<% end %>


</body>
</html>
