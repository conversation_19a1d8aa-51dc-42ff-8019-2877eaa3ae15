<!doctype html>

<head>
  <meta charset="UTF-8">
  <title>ConferenceStop by Servace</title>
  <%= stylesheet_link_tag(*style_sheets) %>
  <link type="text/css" rel="stylesheet" media="screen" href="/assets/hg-theme/jquery-ui-1.8.13.custom.css">
  <%= stylesheet_link_tag 'tip-yellow' %>
  <link type="text/css" rel="stylesheet" media="screen" href="/assets/jquery.multiselect.css"  >
  <%= javascript_include_tag "application" %>
  <script src="/javascripts/wymeditor/jquery.wymeditor.js"></script>
  <%= csrf_meta_tags %>
  <%= yield :head %>
  <% if Rails.env == "production" %>
    <script type="text/javascript">

      var _gaq = _gaq || [];
      _gaq.push(['_setAccount', 'UA-********-1']);
      _gaq.push(['_setDomainName', 'conferencestop.co.uk']);
      _gaq.push(['_trackPageview']);

      (function() {
        var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
      })();

    </script>
  <% end %>
</head>
