<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <link rel="icon" type="image/png" href="/servace-favicon.png">
  <link rel="manifest" href="/manifest.json">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">


  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" crossorigin="anonymous">

  <%= javascript_include_tag 'application_new' %>

  <%# <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script> %>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>


  <%= csrf_meta_tags %>

  <!-- Le styles -->
  <%= stylesheet_link_tag "application_new" %>

  <%= yield :head %>
  <link href='https://fonts.googleapis.com/css?family=Rokkitt' rel='stylesheet' type='text/css'>

</head>

<body class="<%= "admin_interface" if @admin_mode %> <%= @body_class %> <%= (params[:action]).parameterize %> <%= (params[:controller]).parameterize %>">

<%# TODO add back once a new version created %>
<%= render 'layouts/navigation/newui/main_top_nav' %>

<% if current_user && session[:masquerader_id].present? %>
  <%= development_ribbon position: :right, color: :orange, text: "Acting as: #{current_user.full_name_or_email}" %>
<% end %>
<script type="text/javascript" src="https://secure.leadforensics.com/js/113837.js" data-turbolinks-permanent></script>
<noscript><img alt="" src="https://secure.leadforensics.com/113837.png" style="display:none;"/></noscript>

<div style="background: #fff;">
  <div class="container-fluid">

    <div id="agree-cookies" class="alert alert-info" style="display:none">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      This website uses cookies. Cookies remember you so we can give you a better service online. By using this website
      or closing this message you are agreeing to our <a href="/cookies.html">cookies policy</a> .
    </div>

    <%#= render :partial => 'layouts/navigation/newui/header_row' unless @admin_mode %>
    <%# <%= render :partial => "layouts/flash_and_dropfile" if @acc_admin_mode || @acc_client_mode || @acc_supplier_mode %>

    <%#= render :partial => "layouts/navigation/newui/pin_tabs_new", locals: {current_tab: "My Conference Stop", selected_sub_tab: "Reports Dashboard", sub_sub_menu: "", selected_sub_sub_tab: ""} %>
    <%#= render :partial => "layouts/navigation/pin_tabs", locals: {current_pin: "My Conference Stop", selected_sub_tab: "Reports Dashboard", sub_sub_menu: "", selected_sub_sub_tab: ""} %>
    
    <%= render :partial => "layouts/navigation/newui/cs_main_nav_new", locals: {current_tab: "My Conference Hub", selected_sub_tab: "Reports Dashboard", sub_sub_menu: "", selected_sub_sub_tab: ""} %>


    <%= yield %>

    <%# <%= render :partial => 'layouts/footer' unless @admin_mode %>

  </div>
</div>

</body>
</html>

