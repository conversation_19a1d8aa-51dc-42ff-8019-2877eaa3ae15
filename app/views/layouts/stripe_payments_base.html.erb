<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title><%= @page_title || "Servace" %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= @meta_desc || "Your One Stop for your conference needs, booking conference venues" %>">
  <meta name="keywords" content="<%= @meta_keyw || "Servace, conferences, venues, hotels" %>">
  <meta name="author" content="Servace">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <link rel="icon" type="image/png" href="/servace-favicon.png">
  <link rel="manifest" href="/manifest.json">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
  <meta name="theme-color" content="#ffffff">

  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" crossorigin="anonymous">

  <%= javascript_include_tag 'application_new' %>

  <script src="https://js.stripe.com/v3/"></script>

<!--  <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>-->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.8.0/js/bootstrap-datepicker.min.js" integrity="sha256-tW5LzEC7QjhG0CiAvxlseMTs2qJS7u3DRPauDjFJ3zo=" crossorigin="anonymous"></script>

  <%= csrf_meta_tags %>

  <!-- Le styles -->
  <%= stylesheet_link_tag "application_new" %>

  <%= yield :head %>
  <link href='https://fonts.googleapis.com/css?family=Rokkitt' rel='stylesheet' type='text/css'>

  <style media="screen">
    .alert.alert-error{
      border: 1px solid red;
      color:darkred;
      background-color:pink;
    }

    body{
      font-size:14px;
      font-color:grey;

    }
    .container{
      padding-bottom:50px;
    }

    .table td,th {
      background:#ffffff;
    }

    td.acc-weekend{
      background:#efefef;
    }
    td.acc-weekday{
      background:#f5f5f5;
    }
    td.acc-bankhol{
      background:#aaa;
      color:#fff;
    }
     .table.table-sm td, th {
      background-color:#ffffff;
    }
    .card {
      border: 2px solid #eeeeee;
      margin-bottom:2px;
      border-radius: 1rem;

    }
    a {
      color:#5D3357!important;
    }
    a:hover{
      color:#84C020!important;
    }
    .nav .nav-item a {
      color:#ffffff!important;
    }
    div#keys ul {
      list-style: none;
    }
    div#keys ul li{
      margin: 0 0 5px 15px;
      float: left;
      list-style:none;
    }
    div#keys li span{
      width:10px;
      height:10px;
      border:1px solid #ccc;
      margin:3px 4px 0 0;
      float:left;
    }
    div#keys span.blank{
      background: #fff;
    }
    div#keys span.info{
      background: #d9edf7;
    }
    div#keys span.warning{
      background: #fcf8e3;
    }
    div#keys span.success{
      background: #dff0d8;
    }
    div#keys span.error{
      background: #f2dede;
    }
    div#keys span.weekend{
      background:#efefef
    }
    div#keys span.bh{
      background:#aaa;
    }
     input[type=checkbox].form-control {
          width:auto!important;
    }

    #bg-acc-booking{

      border-left: 10px solid #fff;
      border-right: 10px solid #fff;
      -moz-box-shadow: 0 0 0 1px #fff, 0px 0px 20px rgba(0, 0, 0, .05);
      -webkit-box-shadow: 0 0 0 1px #fff, 0px 0px 20px rgba(0, 0, 0, .05);
      box-shadow: 0 0 0 1px #fff, 0px 0px 20px rgba(0, 0, 0, .05);
      margin: 0 auto 0 auto;
      padding: 0 0 0 0;
    }

    .form-rounded {
       border-radius: 1rem;
      }

    .form-control:disabled{
      background-color:white;
    }

    .form-group label {
      text-align:right;
    }

    .well-lozenge{
      background: #f5f5f5;
      border: none;
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none;
      -webkit-border-radius: 20px 0 20px 0;
      -moz-border-radius: 20px 0 20px 0;
      border-radius: 20px 0 20px 0;
      padding: 30px 30px 10px 30px;
      margin-bottom:30px;

    }

    ul.logos{
      margin:0;
      padding:0;
      list-style:none;
      display:inline;
      width:auto;
      float:left;

      li{
        display:inline;
        margin: 0 20px 0 0;

        img{
          max-height:35px;
        }
      }
    }
    .btn-dark{
      color:lightgray!important;
    }
    .btn-dark:hover{
      color:#ffffff!important;
    }
    .btn-danger{
      color: white!important;
    }
    .btn-success{
      color:white!important;
    }
    .help-block{
      display:block;
      padding:10px;
      float:right;
      color:darkred;
      border: 1px solid red;
      background-color:#FFC0CB;
      border-radius: 4px;
    }
    #card-errors{
      color:red;
      border: 1px solid red;
      background-color:#ffc0cb;
      margin-bottom:10px;
      padding:4px;
      display:none;
      border-radius:4px;

    }
    #my-card-errors{
      color:red;
      border: 1px solid red;
      background-color:#ffc0cb;
      margin-bottom:10px;
      padding:4px;
      display:none;
      border-radius:4px;

    }
    </style>

</head>

<%# TODO add back once a new version created %>
<%#= render 'layouts/navigation/newui/main_top_nav' %>

<% if current_user && session[:masquerader_id].present? %>
  <%= development_ribbon position: :right, color: :orange, text: "Acting as: #{current_user.full_name_or_email}" %>
<% end %>

<div class="row justify-content-center">
  <%- flash.each do |name, msg| -%>
      <% class_name = case name
                      when 'notice'
                        'alert alert-block alert-info'
                      when  'warning'
                         'alert alert-block'
                      when 'error'
                        'alert alert-block alert-error'
                      else
                        'alert alert-block alert-info'
                      end

       %>
       <% if msg.is_a?(String) %>
        <div class="<%= class_name %>" id=<%=  "flash_#{name}" %>  >
          <button type="button" class="close" data-dismiss="alert">&times;</button><br/>
          <%= msg %>
        </div>
      <% end %>
  <% end %>
</div>

<script type="text/javascript" src="https://secure.leadforensics.com/js/113837.js" data-turbolinks-permanent></script>
<noscript><img alt="" src="https://secure.leadforensics.com/113837.png" style="display:none;"/></noscript>
<body id="bg-acc-booking">
  <div class="container">

      <div id="agree-cookies" class="alert alert-info" style="display:none">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        This website uses cookies. Cookies remember you so we can give you a better service online. By using this website
        or closing this message you are agreeing to our <a href="/cookies.html">cookies policy</a> .
      </div>
       <% if current_user && current_user.is_a_supplier? %>
           <%= render :partial => 'layouts/navigation/newui/app_supplier_nav_new' %>
       <% end %>
      <%= yield %>

    </div>

</body>
</html>
