<%= content_for :main_navigation do %>
  <ul class="nav nav-tabs maintabs">
    <%# Conference Hub %>
    <% if user_signed_in? %>
      <% if current_user.is_a_client? && current_user.con_enabled? %>
        <%= nav_pin "cs", "Conference <strong>Hub<span class='dot'>.</span></strong>".html_safe, current_user.is_advanced_client? ? client_dashboard_path : client_conferences_path, :class => "cs", :current => current_pin %>
      <% elsif current_user.is_a_supplier? %>
        <%= nav_pin "cs", "Conference <strong>Hub<span class='dot'>.</span></strong>".html_safe, current_user.has_active_package? ? supplier_dashboard_path : supplier_quotations_path, :class => "cs", :current => current_pin %>
      <% else %>
        <%= nav_pin "cs", "Conference <strong>Hub<span class='dot'>.</span></strong>".html_safe, cs_home_path, :class => "cs", :current => current_pin %>
      <% end %>
      <%= nav_pin "es", "Event <strong>Hub<span class='dot'>.</span></strong>".html_safe, (current_user ? EVENTSTOP_DASH : LEGACY_EVENTSTOP_HOME), :class => "es", :current => current_pin %>

    <%# Accommodation Hub %>
      <% if current_user.is_a_supplier? && current_user.con_enabled? %>
        <%= nav_pin "as", "Accommodation <strong>Hub<span class='dot'>.</span></strong>".html_safe,acc_stop_supplier_dashboard_path, :class => "as", :current => current_pin %>
      <% elsif current_user.is_a_client? && current_user.con_enabled? %>
        <%= nav_pin "as", "Accommodation <strong>Hub<span class='dot'>.</span></strong>".html_safe, adult_rfq_client_dashboard_path, :class => "as", :current => current_pin %>
      <% else %>
        <%= nav_pin "as", "Accommodation <strong>Hub<span class='dot'>.</span></strong>".html_safe,as_home_path, :class => "as", :current => current_pin %>
      <% end %>

      <%# if current_user.is_a_client? %>
        <%= nav_pin "ac", "Academy <strong>Hub<span class='dot'>.</span></strong>".html_safe, academy_bookings_path, :class => "ac", :current => current_pin %>
      <%# end %>

      <% if current_user.is_a_supplier? %>
        <%= nav_pin "ap", "Apprentice <strong>Hub<span class='dot'>.</span></strong>".html_safe, rfq_supplier_dashboard_path, :class => "ap", :current => current_pin %>
      <% elsif current_user.is_a_client? && current_user.app_enabled? %>
        <%= nav_pin "ap", "Apprentice <strong>Hub<span class='dot'>.</span></strong>".html_safe, reports_dashboard_apprentice_summary_report_index_path, :class => "ap", :current => current_pin %>
      <% else %>
        <%= nav_pin "ap", "Apprentice <strong>Hub<span class='dot'>.</span></strong>".html_safe, ap_home_path, :class => "ap", :current => current_pin %>
      <% end %>
    <% end %>

  </ul>
<% end %>