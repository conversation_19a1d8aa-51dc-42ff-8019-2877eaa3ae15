<ul class="nav nav-tabs maintabs" style="">
  <% if user_signed_in? %>
    <%# Conference Hub %>
    <% if current_user.is_a_client? %>
      <% if current_user.con_enabled?%>
        <%= new_nav_pin "cs", "Conference <strong>Hub<span class='dot'>.</span></strong>".html_safe, client_conferences_path, :class => "cs", :current => current_pin %>
      <% end %>

      <%= new_nav_pin "es", "Events <strong>Hub<span class='dot'>.</span></strong>".html_safe, current_user ? EVENTSTOP_DASH : LEGACY_EVENTSTOP_HOME, :class => "es", :current => current_pin %>

      <% if current_user.con_enabled?%>
        <%= new_nav_pin "as", "Accommodation <strong>Hub<span class='dot'>.</span></strong>".html_safe, adult_rfq_client_dashboard_path, :class => "as", :current => current_pin %>
      <% end%>
      <% if current_user.con_enabled?%>
        <%= new_nav_pin "ac", "Academy <strong>Hub<span class='dot'>.</span></strong>".html_safe, academy_bookings_path, :class => "ac", :current => current_pin %>
      <% end%>
      <% if current_user.app_enabled?%>
        <%= new_nav_pin "ap", "Apprentice <strong>Hub<span class='dot'>.</span></strong>".html_safe, reports_dashboard_apprentice_summary_report_index_path, :class => "ap", :current => current_pin %>
      <% end%>
      
    <% elsif current_user.is_a_supplier? %>
      <li class="nav-item cs">
        <a class="nav-link active" href="<%=current_user.has_active_package? ? supplier_dashboard_path : supplier_quotations_path%>"><%="Conference <strong>Hub<span class='dot'>.</span></strong>".html_safe %></a>
      </li>
    <% else %>
        <li class="nav-item cs">
          <a class="nav-link active" href="<%=cs_home_path%>"><%="Conference <strong>Hub<span class='dot'>.</span></strong>".html_safe %></a>
        </li>
    <% end %>
  <% end %>
</ul>