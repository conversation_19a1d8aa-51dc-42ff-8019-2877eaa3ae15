<div class="mainnavtop">
  <div class="navbar">
    <div class="navbar-inner">
      <div class="container">
        <div class="nav-collapse collapse">
          <ul class="nav">
            <%= nav_tab "Dashboard", admin_dashboard_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Tasks", admin_tasks_dashboard_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Conferences", admin_conferences_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Events", EVENTSTOP_ADMIN_VIEW, target: "_blank", current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Programmes", admin_rfq_programmes_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Bookings", apprentice_bookings_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Organisations", admin_organisations_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Chains", admin_chains_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Hotels", admin_hotels_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Users", admin_users_path, current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "Loyalty", admin_loyalty_points_path(:to_be_validated => true), current: current_tab %>
            <li class="divider-vertical"></li>

            <%= nav_tab "System Tables", admin_system_tables_path, current: current_tab %>
            <li class="divider-vertical"></li>
          </ul>
          <div class="pull-right jumpto ">
            <%= form_tag jump_to_opportunities_path(), :remote => true  do %>
                Go to:
                <%= text_field_tag :reference, nil, :onclick => "chooseBtn()", :style => "margin-right:32px;width:60px;" %>

                <div id="btn-chooser" style="display:none" class="btn-group btn-group-vertical">
                  <%= submit_tag "Ref", :class=>"btn btn-mini btn-danger" %>
                  <%= submit_tag "Inv", :class=>"btn btn-mini btn-info" %>
                </div>

            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="jump-to-list" style="display:none;" >

  </div>
  <%= render(:partial => "layouts/navigation/admin_#{sub_menu.downcase}_nav", :locals => {:current_tab => selected_sub_tab}) if sub_menu.present? %>
  <%= render(:partial => "layouts/navigation/admin_#{sub_sub_menu.downcase}_nav", :locals => {:parent => parent, :current_tab => selected_sub_sub_tab}) if sub_sub_menu.present?  %>
</div>
<script>
  function chooseBtn(){
    var panel = $("#btn-chooser");
    if (panel.is(":visible")){
      panel.hide();
    }
    else{
      panel.show();
    }
  }
  function closeJumpList(){
    $("#jump-to-list").hide();
  }

</script>
