<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>OneStop by Servace</title>

    <%= stylesheet_link_tag    "application" %>
    <%= javascript_include_tag "application" %>

    <script src="/javascripts/wymeditor/jquery.wymeditor.js"></script>
    <link href='https://fonts.googleapis.com/css?family=Rokkitt' rel='stylesheet' type='text/css'>
    <%= csrf_meta_tags %>
    <%= yield :head  %>

    <%= vite_client_tag %>
    <%= vite_javascript_tag 'application' %>
    <!--
      If using a TypeScript entrypoint file:
        vite_typescript_tag 'application'

      If using a .jsx or .tsx entrypoint, add the extension:
        vite_javascript_tag 'application.jsx'

      Visit the guide for more information: https://vite-ruby.netlify.app/guide/rails
    -->

  </head>
  <body class="hgone">
    <div id="wrapper">
      <div class="row">
        <header>
          <div class="col_5 col">
            <%= image_tag "HG-onestop.gif" %>
          </div>
          <div class="col_6 col">
            <div class="headercall">
                The one stop for conference bookings<br>
                from the Conference Experts<br>
                <strong>Call <%= MAIN_PHONE %></strong>
            </div>
          </div>
          <div class="col_5 col align_right">
            <% if user_signed_in? %>
              <% unless current_user.is_a_supplier? %>
                <div id="opp_finder">
                <%= form_tag jump_to_opportunities_path() do %>
                  Go to ref:
                  <%= text_field_tag :opportunity_id %>
                  <%= submit_tag "Go" %>
                <% end %>
                </div>
              <% end %>
              <ul>
              <li>Logged in as <%= current_user.contact.full_name_or_email %></li>
              <li><%= link_to "Update My Profile", edit_my_profile_path() %></li>
              <% if current_user.is_a_client?  %>
                <li><%= link_to "#{@current_org.name} profile", client_organisation_path() %></li>
                <li><a href='https://www.servace.co.uk/Conference-Manual.pdf'>Help Document</a></li>
              <% end %>
            </ul>
            <% end %>
          </div>
          <nav id="primary" class="col_16 col">
            <ul>
              <%  if user_signed_in? %>
                <%= render 'shared/menu' %>
                <%  if current_user.is_an_administrator? %>
                  <%= render 'shared/admin_menu' %>
                <% end %>
                <%  if current_user.is_a_client? %>
                  <%= render 'shared/client_menu' %>
                <% end %>
                <%  if current_user.is_a_supplier? %>
                  <%= render 'shared/supplier_menu' %>
                <% end %>
              <% else %>
                <%= render 'devise/menu/login_items' %>
              <% end %>
            </ul>

          </nav>
        <%= (yield :submenu).yielding do |submenu| %>
          <% unless submenu.empty? %>
            <nav id="secondary" class="col_16 col">
              <ul>
                <%= submenu %>
              </ul>

            </nav>
          <% end %>
        <% end %>

          <div class="clear"></div><!-- clear -->

        </header>
      </div><!-- row -->
      <section class="row">
        <div class="col_16 col">

            <% no_notice =  flash[:notice].blank? %>
            <% if @dj_message.present? && no_notice %>
                  <%= content_tag :div, (@dj_message.join("</br>") + "<br/><small>(click to hide)</small>").html_safe, :id => "flash_notice", :onclick => "$(this).hide()"  %>
            <% end %>
            <%- flash.each do |name, msg| -%>
                  <% if name == :notice %>
                    <% msg = @dj_message.present? ? (msg + "</br>" + @dj_message.join("</br>")) : msg  %>
                  <% end %>
                  <%= content_tag :div, (msg + "<br/><small>(click to hide)</small>").html_safe, :id => "flash_#{name}", :id => "flash_#{name}", :onclick => "$(this).hide()" if msg.is_a?(String) %>
            <% end %>


        </div>

        <div id="delayed-job-status" class="col_16 col">

            <% if @backdrop.present? %>
                  <% namespace = case current_user.role
                                   when 'ADMIN'
                                     'admin'
                                   when "BOOKER", "MANAGER", "EXECUTIVE", "TEAM"
                                     'client'
                                 end
                  %>
              <% if @backdrop.file.present? %>
                 <%= render :partial => "#{namespace}/delayed_jobs/download" %>
              <% else %>
                  <%= render :partial => "#{namespace}/delayed_jobs/waiting" %>
              <% end %>
            <% end %>

        </div>

      </section>
      <section>
      <%= yield unless @multirow%>
      </section>
      <%= yield if @multirow%>

      <footer class="row">
        <div class="col_16 col">all rights reserved &copy; <a href="https://www.servace.co.uk/">Servace</a></div>
      </footer>
    </div>

  </body>
</html>
