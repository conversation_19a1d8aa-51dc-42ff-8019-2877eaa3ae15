<div class="row">
  <div class="col-md">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h4>Send Surveys</h4>
      </div>
      <div class="panel-body">
        <div class="col-md" style="margin-bottom: 15px">
          <% if current_user.is_an_administrator? %>
            <%= render partial: 'filter' %>
          <% end %>
        </div>
        <table data-cy="sendSurveysTable" class="table table-striped">
          <thead>
          <tr>
            <th>RFQ ID</th>
            <th>Name</th>
            <th>Created date</th>
            <th>Link text</th>
            <th>Preview survey</th>
            <% if current_user.is_an_administrator? %>
              <th>Email survey</th>
            <% end %>
            <th>Enable / Disable</th>
            <th>Mobile Only</th>
          </tr>
          </thead>
          <tbody>
          <% @surveys.each do |survey| %>
            <% if survey.invalidated? %>
              <tr style="text-decoration: line-through">
                <td><%= survey.rfq_request_id %></td>
                <td><%= h survey.name %></td>
                <td><%= survey.created_at.strftime("%b %Y") %></td>
                <td><%= h get_survey_url(survey) %></td>
                <td>
                  n/a
                </td>
                <% if current_user.is_an_administrator? %>
                  <td>
                    n/a
                  </td>
                <% end %>
                <td>
                  <button data-cy="enableSurvey" type="button" id="btn_<%= survey.id %>" class="btn btn-default btn-sm revalidate_survey">
                    <span class="glyphicon glyphicon-ok-circle"></span> Enable
                  </button>
                </td>
                <td>
                </td>
              </tr>
            <% else %>
              <tr>
                <td><%= survey.rfq_request_id %></td>
                <td><%= h survey.name %></td>
                <td><%= survey.created_at.strftime("%b %Y") %></td>
                <% if survey.mobile_only? %>
                  <td>n/a</td>
                  <td>n/a</td>
                  <% if current_user.is_an_administrator? %>
                    <td>n/a</td>
                  <% end %>
                <% else %>
                  <td><%= h get_survey_url(survey) %></td>
                  <td>
                    <%= link_to feedback_answer_path(survey) do %>
                      <%= content_tag(:i, '', class: 'glyphicon glyphicon-search') %>
                    <% end %>
                  </td>
                  <% if current_user.is_an_administrator? %>
                    <td>
                      <%= link_to feedback_mail_survey_path(survey) do %>
                        <%= content_tag(:i, '', class: 'glyphicon glyphicon-envelope') %>
                      <% end %>
                    </td>
                  <% end %>

                <% end %>
                
                <% if survey.invalidated? %>
                <td>
                  <button data-cy="enableSurvey" type="button" id="btn_<%= survey.id %>_enable" class="btn btn-default btn-sm revalidate_survey">
                    <span class="glyphicon glyphicon-ok-circle"></span> Enable
                  </button>
                </td>
                <% else %>
                  <td>
                    <button data-cy="disableSurvey" type="button" id="btn_<%= survey.id %>_disable" class="btn btn-danger btn-sm invalidate_survey">
                      <span class="glyphicon glyphicon-remove-sign"></span> Disable
                    </button>
                  </td>
                <% end %>

                <td>
                  <% if survey.mobile_only? %>
                    <button data-cy="disableMobile" type="button" id="btn_<%= survey.id %>" class="btn btn-success btn-sm disable_mobile_only">
                      <span class="glyphicon glyphicon-remove-sign"></span> Disable Mobile Only
                    </button>
                  <% else %>
                    <button data-cy="enableMobile" type="button" id="btn_<%= survey.id %>" class="btn btn-danger btn-sm enable_mobile_only">
                      <span class="glyphicon glyphicon-ok-circle"></span> Enable Mobile Only
                    </button>
                  <% end %>
                </td>
              </tr>
            <% end %>
          <% end %>
          </tbody>
        </table>
        <div class="pager">
          <%= paginate @surveys %>
        </div>
        <%= page_entries_info @surveys %>
      </div>

    </div>
  </div>
</div>