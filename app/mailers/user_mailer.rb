class UserMailer < OneStopBaseMailer

  #helper UserMailerHelper

  default :from => "<EMAIL>"

  def csv_upload_confirmation(user, chain, count)
    @user = user
    @chain = chain
    @count = count

    load_attachments

    mail :to => user.email,
         :subject => "#{@chain.name} Registration at Conferencestop.co.uk  - Final update required"
  end


  def welcome_sales_manager(user)
    hotel = user.contact.parent
    @contact_name = user.full_name_or_email
    @contact_email = user.email
    @creator_name = user.creator.full_name_or_email
    @site = "cs"
    load_attachments

    mail :to => user.email,
         :subject => "#{hotel.name} Registration at Conferencestop.co.uk  - Final update required"
  end


  def welcome_user(user, opts={})
    @promoting = true if opts[:promoting].present?
    @user = user
    @site = "hg"
    load_attachments
    attachments.inline["pin-mini-cs.png"] = File.read("#{Rails.root}/app/assets/images/imgphase3/pin-mini-cs.png")

    mail :to => user.email,
         :subject => "Welcome to Servace"
  end

  def welcome_business_unit_manager(user)
    @user = user
    @contact_name = user.name
    load_attachments
    attachments.inline["pin-mini-cs.png"] = File.read("#{Rails.root}/app/assets/images/imgphase3/pin-mini-cs.png")

    mail :to => user.email,
        :subject => "Welcome to the Servace Application"
  end


  def advanced_user(user)
    @user = user
    @site = "hg"
    load_attachments
    attachments.inline["pin-mini-cs.png"] = File.read("#{Rails.root}/app/assets/images/imgphase3/pin-mini-cs.png")

    mail :to => user.email,
         :subject => "Congratulations, now you are an Advanced User"
  end


  def earned_loyalty(earned_loyalty)
    @earned_loyalty = earned_loyalty
    accepted_quote = @earned_loyalty.opportunity.present? ?
        @earned_loyalty.opportunity.package.accepted_quote :
        @earned_loyalty.conference_date.conference.accepted_quote
    @hotel = accepted_quote.hotel
    @main_contact = @earned_loyalty.opportunity.present? ?
        @earned_loyalty.opportunity.main_contact :
        @earned_loyalty.conference_date.conference.opportunity.main_contact

    load_attachments
    @site = "cs"
    mail :to => @earned_loyalty.user.email,
         :subject => "Congratulations! You Have Earned Loyalty Points!"
  end

  def invite_user(invitation, resend = false)
    @invitation = invitation
    @user = @invitation.user
    @contact = @user.contact
    @inviting = true
    @loyalty_not_wanted = true if @user.contact.parent.name =~ /babcock/i
    @resend = resend
    @invitation.update_columns(:email_last_sent_at => Time.zone.now)
    load_attachments
    mail :to => @user.email,
    :subject => "Welcome to Servace! #{' - Reminder' if @resend}"
  end

end
