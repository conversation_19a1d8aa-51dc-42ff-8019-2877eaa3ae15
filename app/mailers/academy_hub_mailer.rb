# encoding: utf-8

class AcademyHubMailer < OneStopBaseMailer
  include ActiveStorage::Blob::Analyzable

  require 'date'

  default :from => "<EMAIL>",
          :cc => "<EMAIL>"

  def send_hotel_booking_request(booking_id)
    get_instance_variables(booking_id)
    load_rfq_attachments
    mail(:to      => @booking.hotel.reservations_contact.email,
          :cc      =>  ["<EMAIL>", "<EMAIL>"],
          :subject => "Academy Hub Booking Request: #{@programme.name}, #{@booking.booker_full_name}") do |format|
        format.html { render :layout => 'adult_program_mailer'}
    end
  end

  def send_hotel_proforma(booking_id)
    get_instance_variables(booking_id)

    return if @booking.payment_method != 'BACS / Credit Card (Paid to Hotel)'
    output = AcademyHubProformaPdf.new({:booking => @booking})
    filename = "Academy-Proforma-#{@booking.id}"
    attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                        :content => output.to_pdf }
    load_rfq_attachments
    mail(:to      => @booking.booker_email,
      :cc      =>  ["<EMAIL>", "<EMAIL>"],
      :subject => "Payment Due for: #{@programme.name}, #{@booking.booker_full_name}") do |format|
      format.html { render :layout => 'adult_program_mailer'}
    end
  end

  def send_stripe_receipt(booking_id)
    get_instance_variables(booking_id)
    return if @booking.payment_method != 'Card'
    output = AcademyHubStripeReceiptPdf.new({:booking => @booking})
    filename = "Academy-Stripe-Receipt-#{@booking.id}"
    attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                        :content => output.to_pdf }
    load_rfq_attachments
    mail(:to      => @booking.booker_email,
      :cc      =>  ["<EMAIL>", "<EMAIL>"],
      :subject => "Payment Receipt for: #{@programme.name}, #{@booking.booker_full_name}") do |format|
      format.html { render :layout => 'adult_program_mailer'}
    end
      
  end

  def send_booking_confirmation(booking_id, updating = false)
    get_instance_variables(booking_id)
    @updating = updating
    emailToList = []
    emailToList << @booking.booker_email
    if @rfq_request.adult_confirmation_to_guest?
      emailToList << @booking.booking_attendees.map(&:email)
    end
    if @rfq_request.adult_additional_confirmation_recipient?
      emailToList << @rfq_request.adult_additional_confirmation_recipient
    end
    load_rfq_attachments
    mail(:to      => emailToList,
          :cc      =>  ["<EMAIL>", "<EMAIL>"],
          :subject => "Booking #{@updating ? "Update" : "Confirmation"}: #{@programme.name}, #{@booking.booker_full_name}") do |format|
        format.html { render :layout => 'adult_program_mailer'}
    end
  end

  def send_booking_declined(booking_id)
    get_instance_variables(booking_id)
    emailToList = []
    emailToList << @booking.booker_email
    if @rfq_request.adult_confirmation_to_guest?
      emailToList << @booking.booking_attendees.map(&:email)
    end
    if @rfq_request.adult_additional_confirmation_recipient?
      emailToList << @rfq_request.adult_additional_confirmation_recipient
    end
    load_rfq_attachments
    mail(:to      => emailToList,
          :cc      =>  ["<EMAIL>", "<EMAIL>"],
          :subject => "Booking Declined: #{@programme.name}, #{@booking.booker_full_name}") do |format|
        format.html { render :layout => 'adult_program_mailer'}
    end
  end

  def send_booking_cancelled(booking_id)
    get_instance_variables(booking_id)
    emailToList = []
    emailToList << @booking.booker_email
    if @rfq_request.adult_confirmation_to_guest?
      emailToList << @booking.booking_attendees.map(&:email)
    end
    if @rfq_request.adult_additional_confirmation_recipient?
      emailToList << @rfq_request.adult_additional_confirmation_recipient
    end
    load_rfq_attachments
    mail(:to      => emailToList,
          :cc      =>  ["<EMAIL>", "<EMAIL>"],
          :subject => "Booking Cancelled: #{@programme.name}, #{@booking.booker_full_name}") do |format|
        format.html { render :layout => 'adult_program_mailer'}
    end
  end 


  def load_rfq_attachments
    if @detail.logo.present? && Rails.env.production?
      attachments.inline["right-logo.jpg"] = @detail.logo.file.read
    end
    attachments.inline["Accommodation-hub.jpeg"] = File.read("#{Rails.root}/app/assets/images/imgphase3/Accommodation-hub.jpeg")
    attachments.inline["spacer.gif"] = File.read("#{Rails.root}/app/assets/images/user_mailer/spacer.gif")
    attachments.inline["ServaceAcademyHub.png"] = File.read("#{Rails.root}/app/javascript/images/adult-bookings/ServaceAcademyHub.png")
  end

  def get_instance_variables(booking_id)
    @booking = Booking.find booking_id
    @rfq_location = @booking.rfq_location
    @rfq_request = @rfq_location.rfq_request

    @programme = @rfq_request.rfq_programme
    @rfq_response = @booking.booking_attendees.first.rfq_response_room.rfq_response
    @hbrief = @rfq_response.rfq_hotel_briefings.first
    @detail =  @rfq_location.adult_accomm_detail || AdultAccommDetail.new
    @hotel = @booking.hotel
  end

end
