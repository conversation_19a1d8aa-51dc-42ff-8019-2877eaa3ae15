# encoding: utf-8

class AccBookingMailer < OneStopBaseMailer
  include ActiveStorage::Blob::Analyzable

  require 'date'

  default :from => "<EMAIL>",
          :cc => "<EMAIL>"

  helper RfqJoiningInstructions<PERSON>elper

  def group_booking_created_manager(gb_id, ap_id)
    @booking_head = AccBookingHeader.find gb_id
    @acc_booking_person = @booking_head.acc_booking_people.find ap_id
    @learner = @acc_booking_person.rfq_learner
    @bu = @learner.rfq_business_unit
    @programme = @bu.rfq_programme
    @hotel = @booking_head.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @rfq_location = @booking_head.rfq_location
    @rfq_response = @rfq_location.accepted_response

    @site = "ap"
    # load_rfq_attachments

    mail :to => @acc_booking_person.email_confirmation_to,
         :subject => "#{@learner.full_name} #{@programme.name} Urgent: Confirmation of dates to hold." do |format|
      format.html { render :layout => 'program_mailer' }
    end

  end

  def virtual_group_booking_created_manager(gb_id, ap_id)
    @booking_head = AccBookingHeader.find gb_id
    @acc_booking_person = @booking_head.acc_booking_people.find ap_id
    @learner = @acc_booking_person.rfq_learner
    @bu = @learner.rfq_business_unit
    @programme = @bu.rfq_programme
    @rfq_location = @booking_head.rfq_location

    @site = "ap"
    load_rfq_attachments

    mail :to => @acc_booking_person.email_confirmation_to,
         :subject => "#{@learner.full_name} #{@programme.name} Virtual Training." do |format|
      format.html { render :layout => 'program_mailer' }
    end

  end

  def send_initial_admin_email(gb_id)
    @booking_head = AccBookingHeader.find gb_id
    @rfq_request = @booking_head.rfq_location.rfq_request
    @programme = @rfq_request.rfq_programme
    @client = @programme.client
    @site = "ap"
    load_attachments

    mail :to => @booking_head.booker.email,
         :subject => "Apprentice Group Booking, #{@programme.name}"
  end

  def send_initial_hotel_email(gb_id)
    @booking_head = AccBookingHeader.find gb_id
    @rfq_request = @booking_head.rfq_location.rfq_request
    @programme = @rfq_request.rfq_programme
    @client = @programme.client
    @hotel = @booking_head.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @site = "ap"
    load_attachments
    if @booking_head.acc_bookings.first.late_booking?
      cc = @client.apprentice_contract_level == "FULLY" ? ["<EMAIL>", "<EMAIL>"] : ["<EMAIL>"]
      mail :to => @hotel.reservations_contact.email,
           :subject => "URGENT: Group booking for #{@programme.name} (Within #{@booking_head.acc_bookings.first.late_booking_threshold} days)",
           :cc => cc
    else
      mail :to => @hotel.reservations_contact.email,
           :subject => "URGENT: Group booking for #{@programme.name}"
    end

  end

  def send_joining_instructions(booking_id, lemail = false, ji_edited = false)
    @lemail = lemail
    @booking = AccBooking.find booking_id
    @person = @booking.acc_booking_person
    return if @person.person_type == "PRO" || @person.person_type == "TRA"
    @rfq_location = @booking.acc_booking_header.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @client = @rfq_request.rfq_programme.client
    if @booking.virtual_flag?
      @booking.update_column(:joining_inst_sent_at, Time.zone.now)
      @rfq_joining_instruction = get_ji
      if !@lemail && @rfq_request.ji_to_learner?
        if @person.rfq_learner&.email.present?
          AccBookingMailer.delay.send_ji_to_learner(booking_id)
        else
          @no_learner = true
        end
      end
      if @lemail
        attach_vm_ical_file(@booking)
      end
      attachments.inline["confirm_button.png"] = File.read("#{Rails.root}/app/assets/images/confirm-booking-btn.png")
      rfq_task = @rfq_request.task_for_code("join_instr")
      if @rfq_joining_instruction.class == JoiningInstruction
        @rfq_joining_instruction.attachments.each do |file|
          attachments[file.filename.to_s] = file.download
        end
      else
        if rfq_task.rfq_documents.any?
          rfq_task.rfq_documents.each do |doc|
            attachments[doc.file.name] = doc.file.file.read if Rails.env.production?
          end
        end
      end

      to = @booking.acc_booking_person.person_type == "LEA" ? @booking.acc_booking_person.rfq_learner.manager_email : @booking.acc_booking_person.rfq_trainer.email
      to = add_finance_manager(to) unless @booking.acc_booking_person.person_type == "TRA"
      mail :to => to,
          :subject => "URGENT: Joining instructions for #{@person.business_unit_for_table} for #{@person.name_for_table}" do |format|
        format.html { render :layout => 'program_mailer' }
      end
    else
      #residential
      @rfq_response = @rfq_location.accepted_response
      @booking.reset_confirmation_answers!
      @email = true
      if @booking.joining_inst_sent_at.present?
        @booking.create_ji_chase!
      end
      @booking.update_column(:joining_inst_sent_at, Time.zone.now)
      @cbrief = @rfq_response.rfq_client_briefings.first
      @hbrief = @rfq_response.rfq_hotel_briefings.first

      @rfq_joining_instruction = get_ji
      @rfq_task = @rfq_joining_instruction.rfq_task

      # whenever ji is sent also send to learner when required
      if @rfq_request.ji_to_learner? && @booking&.acc_booking_person&.rfq_learner&.email&.present?
        AccBookingMailer.delay.send_ji_to_learner(booking_id, ji_edited)
      end
      @site = "ap"
      if @booking.should_send_apprentice_proforma? && @booking.card_pay?
        output = AprProformaPdf.new({ :booking => @booking })
        filename = "Proforma-#{@booking.id}"
        attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                           :content => output.to_pdf }
      end
      load_rfq_attachments if Rails.env.production?

      # system to attach through active storage if new type of JI
      if @rfq_joining_instruction.class == JoiningInstruction
        @rfq_joining_instruction.attachments.each do |file|
          attachments[file.filename.to_s] = file.download
        end
      else
        if @rfq_task.rfq_documents.any?
          @rfq_task.rfq_documents.each do |doc|
            attachments[doc.file.name] = doc.file.file.read if Rails.env.production?
          end
        end
      end
    end

    citb_attachment if Rails.env.production?

    to = @booking.acc_booking_person.person_type == "LEA" ? @booking.acc_booking_person.rfq_learner.manager_email : @booking.acc_booking_person.rfq_trainer.email
    to = add_finance_manager(to) unless @booking.acc_booking_person.person_type == "TRA"
    subject = "URGENT: Joining instructions for #{@person.business_unit_for_table} for #{@person.name_for_table}"
    subject.insert(0, "Revised JI. ") if ji_edited
    mail :to => to,
        :subject => subject do |format|
      format.html { render :layout => 'program_mailer' }
    end

    fix_mixed_attachments unless Rails.env == "development"
  end

  def send_ji_to_learner(booking_id, ji_edited = false)
    @booking = AccBooking.find booking_id
    @person = @booking.acc_booking_person
    @rfq_location = @booking.acc_booking_header.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @client = @rfq_request.rfq_programme.client

    if @booking.virtual_flag?
      @rfq_joining_instruction = get_ji
      load_rfq_attachments
      attachments.inline["confirm_button.png"] = File.read("#{Rails.root}/app/assets/images/confirm-booking-btn.png")
      email = @booking.acc_booking_person.rfq_learner.present? ? @booking.acc_booking_person.rfq_learner.email : @booking.acc_booking_person.rfq_trainer.email
      mail :to => email,
          :subject => "URGENT: Joining instructions for #{@person.business_unit_for_table} for #{@person.name_for_table}" do |format|
        format.html { render :layout => 'program_mailer' }
      end

    else
      # residential
      @rfq_response = @rfq_location.accepted_response
      @cbrief = @rfq_response.rfq_client_briefings.first
      @hbrief = @rfq_response.rfq_hotel_briefings.first
      @rfq_joining_instruction = get_ji

      if @rfq_request.ji_attachments_to_learner?
        # system to attach through active storage if new type of JI
        if @rfq_joining_instruction.class == JoiningInstruction
          @rfq_joining_instruction.attachments.each do |file|
            attachments[file.filename.to_s] = file.download
          end
        else
          @rfq_task = @rfq_joining_instruction.rfq_task
          if @rfq_task.rfq_documents.any?
            @rfq_task.rfq_documents.each do |doc|
              attachments[doc.file.name] = doc.file.file.read if Rails.env.production?
            end
          end
        end
      end

      @site = "ap"
      load_rfq_attachments
      citb_attachment
      subject = "IMPORTANT: Joining instructions for #{@person.business_unit_for_table}"
      subject.insert(0, "Revised JI. ") if ji_edited
      mail :to => @booking.acc_booking_person.rfq_learner.email,
        :subject => subject do |format|
        format.html { render :layout => 'program_mailer' }
      end
    end
    fix_mixed_attachments unless Rails.env == "development"
  end

  def send_virtual_multi_ji_to_learner(booking_header, booking_person, bookings, isLearner = false)
    @bookings = bookings
    @person = booking_person
    @booking_header = booking_header
    @rfq_location = booking_header.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @ji = @rfq_request.virtual_join_instruction

    if (@rfq_request.ji_to_learner? && @person.rfq_learner&.email.present?) || !isLearner
      send_virtual_multi_ji(isLearner)
    end
  end

  def send_virtual_multi_ji(isLearner = false)
    # rfq_task = @rfq_request.task_for_code("join_instr")
    # if rfq_task.rfq_documents.any?
    #   rfq_task.rfq_documents.each do |doc|
    #     attachments[doc.file.name] = doc.file.file.read
    #   end
    # end
    # load_rfq_attachments
    # attachments.inline["confirm_button.png"] = File.read("#{Rails.root}/app/assets/images/confirm-booking-btn.png")

    # booking = @bookings.first
    # @learner = isLearner

    @bookings.each do |booking|
      if booking.can_send_ji?
        send_joining_instructions(booking.id, isLearner)
      end
      # booking.update_column(:joining_inst_sent_at, Time.zone.now)
    end

    # if booking.acc_booking_person.email_confirmation_to.is_a?(Array) && !isLearner
    #   to = booking.acc_booking_person.email_confirmation_to[0]
    # elsif isLearner && @person&.rfq_learner&.email&.present?
    #   to = booking.acc_booking_person.rfq_learner.email
    # else
    #   to = booking.acc_booking_person.email_confirmation_to
    # end

    # mail :to => to,
    #      :subject => "URGENT: Joining instructions for #{@person.business_unit_for_table} for #{@person.name_for_table}" do |format|
    #   format.html { render :layout => 'program_mailer' }
    # end

    # fix_mixed_attachments unless Rails.env == "development"
  end

  def send_virtual_multi_confirmation(booking_ids, l_email = nil, reminder = false)
    @l_email = l_email
    @bookings = AccBooking.where(id: booking_ids)
    @person = @bookings.first.acc_booking_person
    @booking_header = @bookings.first.acc_booking_header
    @rfq_location = @booking_header.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @programme = @rfq_location.rfq_request.rfq_programme
    @reminder = reminder

    rfq_task = @rfq_request.task_for_code("join_instr")
    if rfq_task.rfq_documents.any?
      rfq_task.rfq_documents.each do |doc|
        attachments[doc.file.name] = doc.file.file.read
      end
    end


    if @l_email.present?
      @bookings.each do |booking|
        booking.update_column(:conf_sent_at, Time.zone.now)
        attach_vm_ical_file(booking)
      end
    end

    @booking = @bookings.first
    @ji = get_ji

    if @booking.acc_booking_person.email_confirmation_to.is_a?(Array) && @person&.rfq_learner&.email&.present? && @l_email.blank?
      to = @booking.acc_booking_person.email_confirmation_to[0]
    elsif @l_email.present?
      to = @booking.acc_booking_person.rfq_learner.email
    else
      to = @booking.acc_booking_person.email_confirmation_to
    end

    mail(:to => to,
         :cc => "<EMAIL>",
         :subject => "Booking Confirmation: #{@programme.name}, #{@person.name_for_ji}") do |format|
      format.html { render :layout => 'program_mailer' }
    end

    fix_mixed_attachments unless Rails.env == "development"

  end

  def send_booking_confirmation(booking_id, reminder = false, l_email = nil)
    @l_email = l_email
    @reminder = reminder
    @booking = AccBooking.find booking_id
    @person = @booking.acc_booking_person
    @booking.update_column(:conf_sent_at, Time.zone.now)

    if @booking.acc_booking_header.adult_booking?
      @rfq_response = @booking.rfq_response_room.rfq_response
      @hbrief = @rfq_response.rfq_hotel_briefings.first
      @cbrief = @rfq_response.rfq_client_briefings.first
      @site = "as"
    elsif @booking.virtual_flag?
      @site = "ap"
      @rfq_location = @booking.acc_booking_header.rfq_location
      @rfq_request = @rfq_location.rfq_request
      rfq_task = @rfq_request.task_for_code("join_instr")
      if rfq_task.rfq_documents.any?
        rfq_task.rfq_documents.each do |doc|
          attachments[doc.file.name] = doc.file.file.read
        end
      end
    else
      @hbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_hotel_briefings.first
      @cbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_client_briefings.first
      @rfq_location = @booking.acc_booking_header.rfq_location
      @rfq_response = @rfq_location.accepted_response
      @site = "ap"
      @rfq_request = @rfq_location.rfq_request
      @client = @rfq_location.rfq_request.rfq_programme.client
    end

    @rfq_location = @booking.acc_booking_header.rfq_location
    @programme = @rfq_location.rfq_request.rfq_programme

    unless @booking.virtual_flag? || @booking.subsistence_only?
      @hotel = @booking.hotel
      if @hotel.main_photo.present?
        attachments.inline["hotel_snap.png"] = @hotel.main_photo.file.read if Rails.env.production?
      end
    end

    #Added condition, where we don't show this for a learner
    # we don't send to subsistence only bookings
    # we add the card pay condition in here because stripe bookings should send proforma before confirmation 
    if !l_email && @booking.should_send_apprentice_proforma? && !@booking.card_pay?
      output = AprProformaPdf.new({ :booking => @booking })
      filename = "Proforma-#{@booking.id}"
      attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                         :content => output.to_pdf }
    end

    if @booking.virtual_flag?
      @ji = @rfq_request.virtual_join_instruction
      if @l_email.present?
        attach_vm_ical_file(@booking)
      end
    end
    load_rfq_attachments if Rails.env.production? # unless no branding or no ji??
    if @booking.acc_booking_person.email_confirmation_to.is_a?(Array) && @person&.rfq_learner&.email&.present? && @l_email.blank?
      to = @booking.acc_booking_person.email_confirmation_to[0]
      if (lemail = @booking.acc_booking_person.email_confirmation_to[1])
        AccBookingMailer.delay.send_booking_confirmation(@booking.id, reminder, lemail)
      end
    elsif @l_email.present?
      to = @l_email
    else
      to = @booking.acc_booking_person.email_confirmation_to
    end
    to = add_finance_manager(to) if @booking.acc_booking_person.person_type == "LEA"

    if to.present?
      mail(:to => to,
          :cc => "<EMAIL>",
          :subject => "Booking Confirmation: #{@programme.name}, #{@person.name_for_ji}") do |format|
            format.html { render :layout => 'program_mailer' }
          end
    else
      raise "No recipient found" and return
    end

    fix_mixed_attachments unless Rails.env == "development"

  end

  def send_booking_confirmation_from_hotel(booking_id)
    @booking = AccBooking.find booking_id
    @person = @booking.acc_booking_person
    @booking.update_column(:conf_sent_at, Time.zone.now)

    if @booking.acc_booking_header.adult_booking?
      @rfq_response = @booking.rfq_response_room.rfq_response
      @hbrief = @rfq_response.rfq_hotel_briefings.first
      @cbrief = @rfq_response.rfq_client_briefings.first
      @site = "as"
    else
      @hbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_hotel_briefings.first
      @cbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_client_briefings.first
      @rfq_location = @booking.acc_booking_header.rfq_location
      @rfq_response = @rfq_location.accepted_response
      @site = "ap"
      @rfq_request = @rfq_location.rfq_request
      @client = @rfq_location.rfq_request.rfq_programme.client
    end

    @rfq_location = @booking.acc_booking_header.rfq_location
    @programme = @rfq_location.rfq_request.rfq_programme

    if @booking.total_due > 0.0
      #Added condition, where we don't show this for a learner
      output = AprProformaPdf.new({ :booking => @booking })
      filename = "Proforma-#{@booking.id}"
      attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                         :content => output.to_pdf }
    end

    load_rfq_attachments if Rails.env.production? # unless no branding or no ji??

    to = @booking.acc_booking_person.person_type == "LEA" ? @booking.acc_booking_person.rfq_learner.manager_email : @booking.acc_booking_person.rfq_trainer.email
    to = add_finance_manager(to) unless @booking.acc_booking_person.person_type == "TRA"

    mail(:to => to,
         :cc => "<EMAIL>",
         :subject => "Booking Confirmation: #{@programme.name}, #{@person.name_for_ji}") do |format|
      format.html { render :layout => 'program_mailer' }
    end

    fix_mixed_attachments unless Rails.env == "development"

  end

  def send_bulk_cancellation(booking_ids, block_cancel = false)
    @bookings = AccBooking.where(id: booking_ids)
    @booking_header = @bookings.first.acc_booking_header
    @rfq_location = @booking_header.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @ji = @rfq_request.virtual_join_instruction

    load_rfq_attachments

    send_to = @bookings.first.acc_booking_person.email_cancellation_to(block_cancel)

    mail(:to => send_to,
         :subject => "Booking Cancellation") do |format|
      format.html { render :layout => 'program_mailer' }
    end
  end

  def send_individual_cancellation(booking_id, block_cancel = false)
    sending = true
    @booking = AccBooking.find booking_id
    if ["AGB", "PGB"].include? @booking.acc_booking_person.person_type
      @conference = @booking.acc_booking_header.conference
    elsif @booking.virtual_flag?
      @rfq_location = @booking.acc_booking_header.rfq_location
    else
      @cbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_client_briefings.first
      @rfq_location = @booking.acc_booking_header.rfq_location
      @rfq_response = @rfq_location.accepted_response
    end
    load_rfq_attachments

    send_to = @booking.acc_booking_person.email_cancellation_to(block_cancel)
    if !@booking.virtual_flag? && !@booking.subsistence_only? && !block_cancel
      send_to << @booking.hotel.reservations_contact.email unless @booking.card_pay?
    end
    if sending
      mail(:to => send_to,
           :subject => "Booking Cancellation") do |format|
        format.html { render :layout => 'program_mailer' }
      end
    end
  end

  def cancel_booking_hotel_card_pay(booking_id)
    @booking = AccBooking.find booking_id
    @cbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_client_briefings.first
    @rfq_location = @booking.acc_booking_header.rfq_location
    @rfq_response = @rfq_location.accepted_response
    load_rfq_attachments
    # TODO handle subsistence only bookings
    mail(:to => @booking.hotel.reservations_contact.email,
         :subject => "Booking Cancellation") do |format|
      format.html { render :layout => 'program_mailer' }
    end
  end

  def booking_amended(booking_id)
    @booking = AccBooking.find booking_id
    return if @booking.subsistence_only? # Early return if it's a subsistence booking

    hotel = @booking.get_booking_hotel
    @cbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_client_briefings.first
    load_rfq_attachments

    if @booking.should_send_apprentice_proforma? && @booking.card_pay?
      output = AprProformaPdf.new({ :booking => @booking })
      filename = "Proforma-#{@booking.id}"
      attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                         :content => output.to_pdf }
    end

    mail(to: hotel.reservations_contact.email,
         subject: "Booking Amended") do |format|
      format.html { render layout: 'program_mailer' }
    end
  end

  def send_block_added(block_id)
    @acc_booking_block = AccBookingBlock.find block_id
    @booking_head = @acc_booking_block.acc_booking_header
    @hotel = @acc_booking_block.acc_booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @rfq_request = @booking_head.rfq_location.rfq_request
    @programme = @rfq_request.rfq_programme
    @site = "ap"
    load_attachments

    if @acc_booking_block.start_date <= Date.today + 4.weeks
      mail(:to => @hotel.reservations_contact.email,
           :subject => "URGENT within 4 weeks - Block Added for #{@programme.name}",
           :cc => "<EMAIL>")
    else
      mail(:to => @hotel.reservations_contact.email,
           :subject => "URGENT: Block Added for #{@programme.name}")
    end
  end

  def send_admin_block_added(block_id)
    @acc_booking_block = AccBookingBlock.find block_id
    @booking_head = @acc_booking_block.acc_booking_header
    @hotel = @acc_booking_block.acc_booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @client = @booking_head.rfq_location.rfq_request.rfq_programme.client
    @rfq_request = @booking_head.rfq_location.rfq_request

    @site = "ap"
    load_attachments

    mail(:to => @booking_head.booker.email,
         :subject => "Block Added")
  end

  def send_provisionals_added(head_id, provs_added)
    @booking_header = AccBookingHeader.find head_id
    @provs_added = provs_added
    @hotel = @booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @rfq_request = @booking_header.rfq_location.rfq_request
    @programme = @rfq_request.rfq_programme

    @site = "ap"
    load_attachments

    mail(:to => @hotel.reservations_contact.email,
         :subject => "Provisionals Added")

  end

  def send_admin_provisionals_added(head_id, provs_added)
    @booking_head = AccBookingHeader.find head_id
    @provs_added = provs_added
    @client = @booking_head.rfq_location.rfq_request.rfq_programme.client
    @rfq_request = @booking_head.rfq_location.rfq_request

    @site = "ap"
    load_attachments

    mail(:to => @booking_head.booker.email,
         :subject => "Provisional Places Added")

  end

  def send_added_new_person(person_id)
    @person = AccBookingPerson.find person_id
    @booking_header = @person.acc_booking_header
    @hotel = @booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @rfq_request = @booking_header.rfq_location.rfq_request
    @programme = @rfq_request.rfq_programme

    @site = "ap"
    load_attachments

    if @person.acc_bookings.where("first_checkin > ? and first_checkin < ? ", Date.today, Date.today + 4.weeks).any?
      mail(:to => @hotel.reservations_contact.email,
           :subject => "URGENT within 4 weeks - Added Additional Person #{@programme.name}",
           :cc => "<EMAIL>")
    else
      mail(:to => @hotel.reservations_contact.email,
           :subject => "URGENT: Added Additional Person #{@programme.name}")
    end

  end

  def send_admin_added_new_person(person_id)
    @person = AccBookingPerson.find person_id
    @booking_head = @person.acc_booking_header
    @rfq_request = @booking_head.rfq_location.rfq_request
    @client = @rfq_request.rfq_programme.client
    @site = "ap"
    load_attachments

    mail(:to => @booking_head.booker.email,
         :subject => "Provisional Places Added")

  end

  def send_hotel_block_cancelled(block_id)
    @acc_booking_block = AccBookingBlock.find block_id
    @booking_head = @acc_booking_block.acc_booking_header
    @hotel = @acc_booking_block.acc_booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @rfq_request = @booking_head.rfq_location.rfq_request
    @programme = @rfq_request.rfq_programme
    @site = "ap"
    load_attachments

    mail(:to => @hotel.reservations_contact.email,
         :subject => "Block Cancelled")

  end

  def send_admin_block_cancelled(block_id)
    @acc_booking_block = AccBookingBlock.find block_id
    @booking_head = @acc_booking_block.acc_booking_header
    @hotel = @acc_booking_block.acc_booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel
    @client = @booking_head.rfq_location.rfq_request.rfq_programme.client
    @rfq_request = @booking_head.rfq_location.rfq_request

    @site = "ap"
    load_attachments

    mail(:to => @booking_head.booker.email,
         :subject => "Block Cancelled")
  end

  #nb status can be "CONFIRMATION", "CANCELLATION" or "AMENDMENT"

  def send_hotel_trainer_booking(book_id, status)
    @status = status.upcase
    @booking = AccBooking.find(book_id)
    @hotel = Hotel.find(@booking.hotel.id)
    @hbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_hotel_briefings.first
    @person = @booking.acc_booking_person.rfq_trainer
    @rfq_name = @booking.acc_booking_header.rfq_location.rfq_request.try(:name)
    @site = "ap"
    load_attachments
    mail :to => @hotel.reservations_contact.email,
         :subject => "URGENT: Trainer booking for #{@rfq_name }"
  end

  def send_trainer_booking(book_id, status, audience)
    @status = status.upcase
    @booking = AccBooking.find(book_id)
    @hotel = @booking.hotel
    @hbrief = @booking.acc_booking_header.rfq_location.accepted_response.rfq_hotel_briefings.first
    @person = audience == :booker ? @booking.acc_booking_header.booker : @booking.acc_booking_person.rfq_trainer
    @rfq_name = @booking.acc_booking_header.rfq_location.rfq_request.try(:name)
    @site = "ap"
    load_attachments
    attachments.inline["hotel_snap.png"] = @hotel.main_photo.file.read
    mail :to => @person.email,
         :subject => "Trainer booking for #{@rfq_name}"
  end

  def send_payment_failed(booking_id)
    @booking = AccBooking.find(booking_id)
    @site = "ap"
    load_attachments
    mail :to => @booking.acc_booking_person.rfq_learner.manager_email,
         :subject => "Servace - Payment has failed"
  end

  def hotel_processed_booking(book_id, audience)
    @booking = AccBooking.find(book_id)
    @hotel = Hotel.find(@booking.hotel.id)
    @booker = @booking.acc_booking_header.booker
    @person = @booking.acc_booking_person.person_type == "LEA" ? @booking.acc_booking_person.rfq_learner : @booking.acc_booking_person.rfq_trainer
    @location = @booking.acc_booking_header.rfq_location
    @rfq_name = @location.rfq_request.try(:name)

    load_attachments
    @to, @name = case audience
                when :hg
                    ["<EMAIL>", 'HG']
                when :booker
                    [@booker.email, @booker.full_name]
                # when :person
                else
                    [@person.email, @person.full_name]
                end
    mail :to => @to,
        :cc => nil,
        :subject => "#{@booking.hotel_confirmed_at&.to_fs(:long) ? "Room availability confirmed by hotel" : "Room availability declined by hotel and booking cancelled"} - for #{@rfq_name}"
  end

  def booking_payment_receipt(book_id, bp_id = nil, current_user_name = nil)
    @booking = AccBooking.find book_id
    @booking_payment = @booking.booking_payments.find(bp_id) if bp_id
    @rfq_location = @booking.acc_booking_header.rfq_location
    @rfq_response = @rfq_location.accepted_response
    @cbrief = @rfq_location.accepted_response.rfq_client_briefings.first
    load_rfq_attachments
    output = AprStripeSummaryPdf.new({ :booking => @booking, :booking_payment => @booking_payment })

    filename = "booking-#{@booking.id}-summary"
    attachments[filename + ".pdf"] = { :mime_type => 'application/pdf', :content => output.to_pdf }

    fix_mixed_attachments unless Rails.env == "development"

    @booking_payment.update(reminded_at: Time.now, reminded_by: current_user_name.blank? ? "" : current_user_name) if bp_id

    to = @booking.acc_booking_person.rfq_learner.manager_email
    to = add_finance_manager(to) unless @booking.acc_booking_person.person_type == "TRA"
    mail(:to => to,
         :subject => "Booking Payment Summary for #{@booking.id} ") do |format|
      format.html { render :layout => 'program_mailer' }
    end
  end

  def vat_summary(manager_email, booking_ids)
    @bookings = AccBooking.where(id: booking_ids)
    # this needs updating to work with multiple bookings at some point
    @learner = @bookings.first.acc_booking_person.rfq_learner
    business_unit = @learner.rfq_business_unit
    @site = "ap"
    @show_footer = false
    load_attachments
    output = StripeReconciliationMonthlyInvoicePdf.new(@bookings, business_unit).print_data.render
    filename = "bookings-VAT-summary"
    attachments[filename + ".pdf"] = { :mime_type => 'application/pdf', :content => output }
    to = [manager_email]
    @bookings.map {|booking| (to << booking.acc_booking_person.rfq_learner.rfq_business_unit.finance_manager_email) if booking.acc_booking_person.rfq_learner.rfq_business_unit.send_finance_manager_joining_instructions}
    mail(:to => to, :subject => "Booking VAT Payment Summary")
  end

  def send_late_reminders(hotel_id)
    hotel = Hotel.find(hotel_id)
    @hotel_contact = hotel.reservations_contact

    mail(:to => @hotel_contact.email,
         :subject => "Late booking reminder") do |format|
      format.html { render :layout => 'program_mailer' }
    end
  end

  def notify_hotel_confirmed(booking)
    @booking = booking
    mail(:to => @booking.acc_booking_header.booker.email,
         :subject => "Booking ID:#{@booking.id} for #{@booking.acc_booking_person.rfq_learner.full_name} confirmed") do |format|
      format.html { render :layout => 'program_mailer' }
    end
  end

  def send_outstanding_non_arrival_emails(email, booking_id, client_name)
    @booking_id = booking_id
    @client_name = client_name
    mail(:to => email,
         :subject => "#{client_name} - Non Arrival Reminder for #{booking_id}") do |format|
      format.html { render :layout => 'program_mailer' }
    end
  end

  def send_ji_preview(ji, contact, manager, subsistence, subject)
    @rfq_joining_instruction = ji
    @person = contact
    @booking = ji.dummy_booking

    dt = DateTime.now
    if ji.ji_type == JoiningInstruction.ji_types['epa']
      @booking.epa_booking = true;
      @booking.epa_training_datetimes = [{ date: dt.strftime("%d/%m/%Y"), time: dt.strftime("%H:%M") }, { date: dt.strftime("%d/%m/%Y"), time: dt.strftime("%H:%M") }]
    end

    if ji.ji_type == JoiningInstruction.ji_types['virtual']
      @booking.virtual_flag = true;
    end

    if subsistence
      @booking.subsistence_only = true
    else
      @booking.first_checkin = dt.strftime("%d/%m/%Y")
      @booking.last_check_out = dt.next_day.strftime("%d/%m/%Y")
    end

    @rfq_joining_instruction.attachments.each do |file|
      attachments[file.blob.filename.to_s] = {
        mime_type: file.blob.content_type,
        content: file.blob.download
      }
    end

    if manager
      mail(:to => @person.email, :cc => "<EMAIL>", :subject => subject) do |format|
        format.html do
          render layout: 'program_mailer', template: 'acc_booking_mailer/send_joining_instructions'
        end
      end
    else
      mail(:to => @person.email,
           :cc => "<EMAIL>",
           :subject => subject) do |format|
        format.html do
          render layout: 'program_mailer', template: 'acc_booking_mailer/send_ji_to_learner'
        end
      end
    end
  end

  def send_pro_forma(booking_id)
    @booking = AccBooking.find(booking_id)
    @person = @booking.acc_booking_person
    @rfq_location = @booking.acc_booking_header.rfq_location
    @programme = @rfq_location.rfq_request.rfq_programme

    to = @booking.acc_booking_person.email_confirmation_to
    to = add_finance_manager(to) if @booking.acc_booking_person.person_type == "LEA"

    output = AprProformaPdf.new({ :booking => @booking })
    filename = "Proforma-#{@booking.id}"
    attachments[filename + ".pdf"] = { :mime_type => 'application/pdf',
                                         :content => output.to_pdf }
    mail(:to => to,
        :cc => "<EMAIL>",
        :subject => "Booking Confirmation: #{@programme.name}, #{@person.name_for_ji}") do |format|
          format.html { render :layout => 'program_mailer' }
        end
  end

  def send_headless_booking_list(booking_ids)
    @bookings = AccBooking.joins(acc_booking_person: :rfq_learner).where('acc_bookings.id in (?)', booking_ids).select('acc_bookings.id, rfq_learners.forename as forename, rfq_learners.surname as surname')
    mail(:to => '<EMAIL>',
      :cc => "<EMAIL>",
      :subject => "Headless Bookings Deleted") do |format|
        format.html { render :layout => 'program_mailer' }
      end
  end

  def dummy_admin
    dummy_admin = User.new
    dummy_admin.role = 'ADMIN'
    dummy_admin
  end

  def load_rfq_attachments
    attachments.inline["spacer.gif"] = File.read("#{Rails.root}/app/assets/images/user_mailer/spacer.gif")
  end

  def citb_attachment
    if CITB.include? @rfq_location.rfq_request.id
      filename = "Learner_Accommodation_Code_of_Conduct_revised_July_14.docx"
      f = open("#{Rails.root}/app/assets/docs/" + filename)
      attachments[filename] = { :mime_type => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', :content => f.read }
    end
  end

  def attach_vm_ical_file(booking)
    cal = Icalendar::Calendar.new
    abh = booking.acc_booking_header
    if abh.multi_modules_selected?
      vm = booking.acc_selected_module.virtual_module
    else
      vm = abh.online_resource
    end

    title = vm.blank? ? booking.id : vm.try(:title)
    ical = Tempfile.new("#{title}.ics", "#{Rails.root.to_s}/tmp/")
    if abh.multi_modules_selected?
      ical << booking.acc_selected_module.ical
    else
      ical << abh.ical
    end
    ical.flush
    attachments["#{title}.ics"] = { :mime_type => "text/calendar", :content => open(ical.path).read }
  end

  def get_ji
    if @booking.joining_instruction_id.present?
      JoiningInstruction.find(@booking.joining_instruction_id)
    else
      get_legacy_jis
    end
  end

  def get_legacy_jis
    if @booking.virtual_flag?
      @rfq_request.virtual_join_instruction
    else
      @rfq_task = @rfq_response.rfq_tasks.find_by(name: "Joining Instructions")
      ji_legacy_type = @booking.subsistence_only? ? RfqJoiningInstruction.statuses[:subsistence] : RfqJoiningInstruction.statuses[:residential]
      @rfq_joining_instruction = RfqJoiningInstruction.where(rfq_task_id: @rfq_task.id, ji_type: ji_legacy_type, epa_ji: @booking.epa_booking?)
                                                    .where.not(approved_client_at: nil).last

      if !@rfq_joining_instruction.present?
        raise "No approved JI found in AccBookingMailer.get_legacy_jis() for this booking(#{@booking.id}), please contact Servace"
      end
      return @rfq_joining_instruction
    end
  end

  def add_finance_manager(to)
    person = @booking.acc_booking_person.get_person
    finance_manager_email = person&.rfq_business_unit&.finance_manager_email
    if person&.rfq_business_unit.send_finance_manager_joining_instructions && finance_manager_email&.present?
      # Added to capture invalid finance manager emails, should be caught on client
      if finance_manager_email.match(/^(.+)@(.+)$/)
        to = [to]
        to << finance_manager_email
      end
    end
    return to
  end

end
