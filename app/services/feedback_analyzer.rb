class FeedbackAnalyzer

  require 'gemini-ai'

  # If changes are made to the structire here, we will need to update / remove any results in the database
  def self.analyze_survey_responses(month, year, survey_id, type = :hotel, force_refresh: false)
    # Validate type is a valid enum value
    unless ['trainee', 'hotel', :trainee, :hotel].include?(type)
      raise ArgumentError, "Invalid type: #{type}. Must be one of: trainee, hotel"
    end

    current_result = FeedbackAiResult.find_by(survey_id: survey_id, result_type: type.to_sym, month: month, year: year)

    if force_refresh && current_result
      Rails.logger.info("FeedbackAnalyzer: Force refresh - deleting existing result for survey_id=#{survey_id}")
      current_result.destroy
      current_result = nil
    end

    if current_result
      Rails.logger.info("FeedbackAnalyzer: Using cached result for survey_id=#{survey_id}")
      begin
        parsed_result = current_result.result.is_a?(String) ? JSON.parse(current_result.result) : current_result.result
        Rails.logger.info("FeedbackAnalyzer: Successfully parsed cached result, keys: #{parsed_result.is_a?(Array) ? parsed_result[0]&.keys : parsed_result&.keys}")
        return parsed_result
      rescue JSON::ParserError => e
        Rails.logger.error("FeedbackAnalyzer: Failed to parse cached result: #{e.message}")
        current_result.destroy
        Rails.logger.info("FeedbackAnalyzer: Deleted corrupted cached result, proceeding with fresh analysis")
      end
    end

    survey_data = fetch_survey_data(survey_id, month, year, type)
    
    # Early exit for empty datasets
    if survey_data.empty?
      Rails.logger.warn("FeedbackAnalyzer: No survey responses found for survey_id=#{survey_id}, month=#{month}, year=#{year}, type=#{type}")
      empty_result = [{ overall_summary: { overall_quality: "No Data", key_themes: [], major_red_flags: [], areas_for_improvement: [] }, questions: {} }]
      # Store empty result to avoid re-processing
      FeedbackAiResult.create!(
        survey_id: survey_id,
        month: month,
        year: year,
        result_type: type,
        result: empty_result.to_json,
        status: 'completed'
      )
      return empty_result
    end
    
    # Log performance metrics (convert to array to avoid SQL count issues with complex selects)
    survey_data_array = survey_data.to_a
    Rails.logger.info("FeedbackAnalyzer: Processing #{survey_data_array.count} survey responses")
    start_time = Time.current
    
    analyzed_data = analyze_feedback(survey_data_array, type)
    
    processing_time = Time.current - start_time
    Rails.logger.info("FeedbackAnalyzer: Completed analysis in #{processing_time.round(2)} seconds")

    # Store the result for future use
    FeedbackAiResult.create!(
      survey_id: survey_id,
      month: month,
      year: year,
      result_type: type,
      result: analyzed_data.to_json,
      status: 'completed'
    )

    return analyzed_data


    # pp format_analysis_results(analyzed_hotel_data)
  end


  def self.format_analysis_results(results)
    output = ""

    results.each do |result|  # Iterate through the array (even if it only has one element)
      overall_summary = result[:overall_summary]
      questions = result[:questions]

      output += "Overall Summary:\n"
      output += "----------------\n"
      output += "Quality: #{overall_summary['overall_quality']}\n"
      output += "Key Themes: #{overall_summary['key_themes'].join(', ') || "N/A"}\n"
      output += "Major Red Flags: #{overall_summary['major_red_flags'].join(', ') || "N/A"}\n"
      output += "Areas for Improvement: #{overall_summary['areas_for_improvement'].join(', ') || "N/A"}\n"
      output += "Action Effectiveness: #{overall_summary['action_effectiveness'] || "N/A"}\n\n"

      output += "Question-Specific Feedback:\n"
      output += "------------------------\n"

      # Table header
      output += "| Question | Quality | Key Themes | Major Red Flags | Areas for Improvement | Action Effectiveness |\n"
      output += "|----------|---------|------------|----------------|----------------------|---------------------|\n"

      # Table rows
      questions.each do |question, analysis|
        # Format each field for table cell
        quality = analysis['overall_quality'] || "N/A"
        key_themes = (analysis['key_themes'] || []).join(', ') || "N/A"
        major_red_flags = (analysis['major_red_flags'] || []).join(', ') || "N/A"
        areas_for_improvement = (analysis['areas_for_improvement'] || []).join(', ') || "N/A"
        action_effectiveness = analysis['action_effectiveness'] || "N/A"

        # Escape pipe characters in text to avoid breaking table format
        question = question.gsub('|', '\\|')
        key_themes = key_themes.gsub('|', '\\|')
        major_red_flags = major_red_flags.gsub('|', '\\|')
        areas_for_improvement = areas_for_improvement.gsub('|', '\\|')
        action_effectiveness = action_effectiveness.gsub('|', '\\|')

        # Add table row
        output += "| #{question} | #{quality} | #{key_themes} | #{major_red_flags} | #{areas_for_improvement} | #{action_effectiveness} |\n"
      end

      output += "\n"
    end

    output
  end


  private


  def self.fetch_survey_data(survey_id, month, year, type = :hotel)
    SurveyResponse.joins(:answers)
      .joins("INNER JOIN questions ON answers.question_id = questions.id")
      .joins("LEFT JOIN organisation_actions ON organisation_actions.answer_id = answers.id")
      .where(survey_id: survey_id)
      .where("DATE_PART('month', survey_responses.created_at::timestamp) = ? AND DATE_PART('year', survey_responses.created_at::timestamp) = ?", month, year)
      .where("questions.trainee_or_hotel = ?", Question.trainee_or_hotels[type.to_sym])
      .select('survey_responses.id as response_id, questions.title as question_title, questions.id as question_id, answers.question_id as aq_id, questions.options as options,
      answers.chosen_option as answer_option, answers.comments as answer_comments, questions.options[answers.chosen_option + 1] AS selected_option_text,
      organisation_actions.details as action_details, organisation_actions.created_at as action_created_at,
      CASE WHEN answers.comments IS NOT NULL AND answers.comments != \'\' THEN true ELSE false END as has_comments')
      .order('questions.id, survey_responses.id')
      .limit(5000) # Prevent accidentally loading massive datasets
  end

  def self.analyze_feedback(survey_data, type)
    analyzed_data = []

    survey_data.each_slice(500) do |batch|
      batch_analysis = analyze_feedback_batch(batch, type)
      if batch_analysis.present?
        if batch_analysis.is_a?(Hash)
          # Accumulate overall_summary and questions
          if analyzed_data.empty?
            analyzed_data << { overall_summary: batch_analysis[:overall_summary] || {}, questions: batch_analysis[:questions] || {} }
          else
            analyzed_data[0][:overall_summary] = (analyzed_data[0][:overall_summary] || {}).merge(batch_analysis[:overall_summary] || {})
            analyzed_data[0][:questions] = (analyzed_data[0][:questions] || {}).merge(batch_analysis[:questions] || {})
          end
        end
      end
    end

    analyzed_data
  end

  def self.analyze_feedback_batch(batch, type)
    client = get_client

    prompt = build_gemini_batch_prompt(batch, type)
    batch_analysis = {} # Initialize as a hash

    begin
      response = client.generate_content(
        {
          contents: {
            role: 'user',
            parts: {
              text: prompt
            }
          },
          generation_config: {
            temperature: 0,
            top_p: 0.1,
            top_k: 1
          }
        }
      )

      if response.is_a?(Hash) && response['candidates'] && response['candidates'][0] && response['candidates'][0]['content'] && response['candidates'][0]['content']['parts'] && response['candidates'][0]['content']['parts'][0]['text']
        json_string = response['candidates'][0]['content']['parts'][0]['text']

        json_string.gsub!("```json", "")
        json_string.gsub!("```", "")
        json_string.strip!

        batch_analysis = process_gemini_batch_response(batch, json_string) # Now returns a hash
      else
        Rails.logger.error("Unexpected Gemini response format: #{response.inspect}")
        batch_analysis = { overall_summary: { quality: "Invalid Response" }, questions: {} } # Return a default hash
      end
    end

    batch_analysis # Return the hash
  end

  def self.build_gemini_batch_prompt(batch, type)
    prompt_text = "Analyze the following #{type} feedback, considering the question title, response distribution, comments, and any actions taken for *each question*:\n\n"
    prompt_text += "There are two types of questions: yes/no questions, 1 and 2, and ofsted questions with ratings 1-4.\n"
    prompt_text += "For ofsted questions, the ratings are: 1=Outstanding, 2=Good, 3=Requires Improvement, 4=Inadequate.\n"
    prompt_text += "For yes/no questions, the ratings are: 1=Yes, 2=No.\n"
    prompt_text += "Only questions with a rating of 4 Inadequate will have comments, but all ratings should be analysed, and comments should reflect that they may be the views of a minority if the ratings are mostly 1-3, as comments can only be added if responder selects 4.\n"
    prompt_text += "Please interpret the chosen_option accordingly when analyzing the feedback quality.\n\n"
    prompt_text += "IMPORTANT: Focus ONLY on analyzing the content of the questions and answers provided. Do NOT suggest changes to the survey structure, question design, or data collection methods.\n\n"

    # Add explicit instructions for rating quality based on responses
    prompt_text += "QUALITY RATING GUIDELINES:\n"
    prompt_text += "1. When all or most responses are 'Outstanding' (1), rate the question as 'Outstanding'.\n"
    prompt_text += "2. When all or most responses are 'Good' (2), rate the question as 'Good'.\n"
    prompt_text += "2. When all or most responses are 'Requires Improvement' (3), rate the question as 'Requires Improcement'.\n"
    prompt_text += "2. When all or most responses are 'Inadequate' (4), rate the question as 'Inadequate'.\n"
    prompt_text += "3. When there's a mix of ratings, look at the majority:\n"
    prompt_text += "5. For yes/no questions, indicate in results that these are Yes/No questions, not ofsted responses.\n\n"

    # Add red flag guidelines
    prompt_text += "RED FLAG GUIDELINES:\n"
    prompt_text += "- Only raise red flags for a question when responses indicate actual problems:\n"
    prompt_text += "- CRITICAL: Always consider the proportion of negative vs positive responses to add some context to the red flags.\n"
    prompt_text += "- When mentioning issues, ALWAYS include the incidence rate (e.g., '1 out of 124 respondents (0.8%)').\n"
    prompt_text += "- DO NOT catastrophize or generalize based on minority responses:\n"
    prompt_text += "  1. If less than 10% of responses are negative, phrase it as 'isolated incident' or 'rare occurrence'.\n"
    prompt_text += "  2. If 10-20% are negative, phrase it as 'small minority' or 'limited instances'.\n"
    prompt_text += "  3. Only use terms like 'widespread', 'significant', or 'major' if >20% of responses indicate problems.\n"
    prompt_text += "  4. For safety questions: if 99% feel safe and 1% don't, the conclusion is 'overwhelmingly positive safety perception with isolated concern'.\n"
    prompt_text += "  5. NEVER say there's a 'widespread feeling of unsafety' when 95%+ respondents feel safe.\n\n"

    context_text = type == :hotel ?
      "Particularly in the context of hotel accommodation for minors, with safeguarding issues raised." :
      "Particularly in the context of training effectiveness and participant experience."
    example_summary_text = "overall_summary"

    # Group feedback by question title and sort for consistency
    feedback_by_question = batch.group_by(&:question_title)

    feedback_by_question.sort_by { |question_title, _| question_title }.each do |question_title, feedback_items|
      next if question_title.include?("If no, please give a reason")

      prompt_text += "Question: #{question_title}\n"

      # Calculate and add response distribution
      all_options = feedback_items.map(&:answer_option)
      option_counts = all_options.tally
      total_responses = all_options.size

      prompt_text += "  Response distribution (#{total_responses} total responses):\n"
      option_counts.sort_by { |option, _| option || 0 }.each do |option, count|
        percentage = (count.to_f / total_responses * 100).round(1)
        option_text = feedback_items.find { |f| f.answer_option == option }&.selected_option_text || "Option #{option}"
        prompt_text += "    - #{option_text} (Option #{option}): #{count} responses (#{percentage}%)\n"
      end
      prompt_text += "\n"

      # Collect and add comments and actions (limit to avoid overly long prompts)
      # Sort by response_id for consistency between runs
      comments_with_actions = feedback_items
        .sort_by(&:response_id)
        .filter_map do |feedback|
          if feedback.answer_comments.present?
            comment_text = "  - Comment: #{feedback.answer_comments}"
            if feedback.action_details.present?
              action_date_str = feedback.action_created_at ? feedback.action_created_at.strftime("%Y-%m-%d") : "Unknown date"
              comment_text += "\n    Action taken: #{feedback.action_details} (Date: #{action_date_str})"
            end
            comment_text
          end
        end.take(10) # Limit to first 10 comments to keep prompt manageable

      if comments_with_actions.any?
        prompt_text += "  Comments and Actions:\n"
        prompt_text += comments_with_actions.join("\n")
        if feedback_items.count { |f| f.answer_comments.present? } > 10
          remaining_count = feedback_items.count { |f| f.answer_comments.present? } - 10
          prompt_text += "\n    ... and #{remaining_count} additional similar comments"
        end
        prompt_text += "\n"
      else
        prompt_text += "  No comments provided for this question.\n"
      end
      prompt_text += "\n"
    end

    prompt_text += "\nProvide a summary analysis in JSON format.\n"
    prompt_text += "IMPORTANT: Use the EXACT field names shown in the example: 'major_red_flags' for all red flag sections (NOT 'flags' or any other variation).\n"
    prompt_text += "We want an overview of the comments raised, any significant issues or areas for improvement highlighted, and the effectiveness of actions taken.\n"
    prompt_text += context_text # Add context based on type
    prompt_text += "\nUse the question title to give context.\n"

    prompt_text += "\nIMPORTANT: For each question, base the overall quality rating on the ACTUAL distribution of responses:\n"
    prompt_text += "- If 75% or more responses are 'Good' or better, the rating MUST be either 'Good' or 'Outstanding'.\n"
    prompt_text += "- Never rate a question as 'Requires Improvement' if most responses are 'Good' or 'Outstanding'.\n"
    prompt_text += "- Only use 'Requires Improvement' if there is a significant percentage of responses at level 3.\n"
    prompt_text += "- Only use 'Inadequate' if there are level 4 responses or serious issues in comments.\n\n"

    prompt_text += "For *each question*, provide:\n"
    prompt_text += "1. Overall quality rating - USE THE MODE (most frequent response):\n"
    prompt_text += "   - Find the option with the highest number of responses and use that as the quality rating\n"
    prompt_text += "   - If Option 1 has the most responses: rate as 'Outstanding'\n"
    prompt_text += "   - If Option 2 has the most responses: rate as 'Good'\n"
    prompt_text += "   - If Option 3 has the most responses: rate as 'Requires Improvement'\n"
    prompt_text += "   - If Option 4 has the most responses: rate as 'Inadequate'\n"
    prompt_text += "   - For yes/no: if Option 1 (Yes) has most responses, rate as 'Good'; if Option 2 (No) has most, rate as 'Requires Improvement'\n"
    prompt_text += "2. Key themes from comments (up to 3) - with incidence rates when discussing negative themes.\n"
    prompt_text += "3. ONLY if applicable, major_red_flags - include any concerning issues but ALWAYS with incidence rates. Include both low incidence (<10%) and higher incidence (>10%) issues.\n"
    prompt_text += "4. Areas for improvement (up to 3) - proportional to actual issues found.\n"
    prompt_text += "5. Effectiveness of actions taken.\n"
    prompt_text += "6. When mentioning ANY negative finding, ALWAYS include the incidence rate (e.g., '2 of 150 respondents (1.3%)').\n\n"

    prompt_text += "In the overall summary, provide:\n"
    prompt_text += "6. Overall quality of all feedback.\n"
    prompt_text += "7. Key themes across all questions (up to 5).\n"
    prompt_text += "8. major_red_flags across all questions - include both low incidence (<10%) and higher incidence (>10%) concerns with rates.\n"
    prompt_text += "9. Areas for improvement across all questions (up to 5).\n"
    prompt_text += "10. Overall effectiveness of actions.\n\n"

    prompt_text += "\nCRITICAL REMINDERS FOR CONSISTENCY:\n"
    prompt_text += "1. Quality ratings MUST be based ONLY on the percentages shown in 'Response distribution' - ignore subjective interpretation.\n"
    prompt_text += "2. Use the EXACT percentages provided to determine quality ratings - this ensures consistency between runs.\n"
    prompt_text += "3. Comments are ONLY from people who selected 'Inadequate' (option 4) - remember this represents a minority view if most responses are good.\n"
    prompt_text += "4. For analysis consistency: if the percentages are identical to a previous run, your quality rating MUST be identical.\n"
    prompt_text += "5. Example calculation: if 85 responses are Good (option 2), 10 are Outstanding (option 1), 5 are Requires Improvement (option 3), then Option 2 has the most responses = rate as 'Good'.\n"
    prompt_text += "6. Never change quality ratings based on comment sentiment alone - comments represent the minority who selected 'Inadequate'.\n"
    prompt_text += "7. CRITICAL: Use EXACTLY these field names - 'major_red_flags' (NOT 'flags', 'red_flags', or 'flags_across_all_questions'). Follow the example JSON structure exactly.\n"

    # Example (now type-dependent)
    if batch.present?
      example_feedback_by_question = batch.group_by(&:question_title)

      prompt_text += "\nExamples as follows:\n"

      example_json = {
        example_summary_text => { # Use the correct key based on type
          "overall_quality": "Good",
          "key_themes": ["Example Theme 1", "Example Theme 2"],
          "major_red_flags": [],
          "areas_for_improvement": ["Example Improvement 1", "Example Improvement 2"],
          "action_effectiveness": "Actions taken were effective in addressing the issues"
        },
        "questions": {}
      }

      example_json["questions"] = {}  # Initialize "questions" as a hash
      example_feedback_by_question.each do |question_title, feedback_items|
          unless question_title.include?("If no, please give a reason")
              example_json["questions"][question_title] = {
                "overall_quality": "Good",
                "key_themes": ["Example Theme 1"],
                "major_red_flags": [],
                "areas_for_improvement": [],
                "action_effectiveness": "Actions taken were effective in addressing the issues"
              }
          end
      end

      prompt_text += <<~PROMPT
        ```json
        #{example_json.to_json}
        ```
      PROMPT
    end

    prompt_text
  end


  def self.process_gemini_batch_response(batch, analysis_results)
    begin
      json_analysis = JSON.parse(analysis_results)
      batch_analysis = { overall_summary: {}, questions: {} }

      if json_analysis['overall_summary']
        batch_analysis[:overall_summary] = json_analysis['overall_summary']

        # Calculate rating counts from questions data
        rating_counts = {
          outstanding: 0,
          good: 0,
          requires_improvement: 0,
          inadequate: 0
        }

        # Count ratings across all questions
        json_analysis['questions']&.each do |_, question_data|
          case question_data['overall_quality'].to_s.downcase
          when 'outstanding'
            rating_counts[:outstanding] += 1
          when 'good'
            rating_counts[:good] += 1
          when 'requires improvement'
            rating_counts[:requires_improvement] += 1
          when 'inadequate'
            rating_counts[:inadequate] += 1
          end
        end

        # Only include counts that are greater than 0
        batch_analysis[:overall_summary][:rating_counts] = rating_counts.select { |_, count| count > 0 }
      end

      if json_analysis['questions']
        batch_analysis[:questions] = json_analysis['questions']
      end

      # Log the analysis for debugging
      Rails.logger.info("Processed batch analysis with #{batch_analysis[:questions].keys.size} questions")

      # Ensure we have entries for all questions in the batch
      batch_questions = batch.map(&:question_title).uniq
      missing_questions = batch_questions - batch_analysis[:questions].keys

      if missing_questions.any?
        Rails.logger.warn("Missing analysis for questions: #{missing_questions.join(', ')}")

        # Add placeholder entries for missing questions
        missing_questions.each do |question_title|
          batch_analysis[:questions][question_title] = {
            "overall_quality": "No Data",
            "key_themes": [],
            "major_red_flags": [],
            "areas_for_improvement": [],
            "action_effectiveness": "No actions taken"
          }
        end
      end

      batch_analysis # Return the structured hash
    rescue JSON::ParserError => e
      Rails.logger.error("Error processing response: #{e.message}, Response: #{analysis_results}")
      { overall_summary: { quality: "Processing Error" }, questions: {} } # Return a basic structure

      Rollbar.error(e, {
        message: "Error processing Gemini response in FeedbackAnalyzer (JSON Parser Error)",
        response: analysis_results
      })
    rescue StandardError => e
      Rollbar.error(e, {
      message: "Error processing Gemini response in FeedbackAnalyzer (StandardError)",
      response: analysis_results
      })
      Rails.logger.error("Error processing response: #{e.message}, Response: #{analysis_results}")
      { overall_summary: { quality: "Processing Error" }, questions: {} } # Return a basic structure
    end
  end

  def self.get_client
    Gemini.new(
      credentials: {
        service: 'generative-language-api',
        api_key: ENV['GEMINI_API_KEY']
      },
      options: { model: 'gemini-2.5-flash', server_sent_events: true }
    )
  end

end
