class StripeReconciliationJob < ApplicationJob
  queue_as :default

  def perform(action, params)
    @params = params # Store params as an instance variable

    # TODO: log job start on db
    Rails.logger.info "Job started: #{action}, Params: #{params}"

    begin
      case action.to_sym
      when :generate_data_rake
        generate_data
        save_hotel_data_to_db
      when :populate_stripe_fees_rake
        populate_stripe_fees
      # when :generate_data
      #   generate_data
      # when :get_month_payment_data
      #   get_month_payment_data
      # when :reconcile_payments
      #   reconcile_payments
      # when :invoice_payments
      #   invoice_payments
      # else
      #   Rails.logger.warn "Unknown action: #{action}"
      end

      # TODO: Probs wants splitting out into three columns
      StripeJobResult.create(job_id: @job_id, status: 'completed', result: @result)
      Rails.logger.info "Job completed successfully: #{action}"
    rescue StandardError => e
      Rails.logger.error "Job failed: #{e.message}"
      # Optionally re-raise the error to let ActiveJob handle it
      raise e
    end
  end

  private

  def generate_data
    hotels = get_payment_data
    @result = { hotels: hotels }
  end

  def get_payment_data(skip_processing = false)
    @bookings = AccBooking.joins(:booking_payments).where('card_pay is true and stripe_payments.stripe_connect_payment is false and confirmed_at is not null and extract(year from first_checkin) = ?', @params[:filter_params][:year].to_i).uniq
    process_bookings_by_hotel() if !skip_processing
  end

  def process_bookings_by_hotel()
    # Group the @bookings by hotel to get main data body
    hotels = []

    @bookings.group_by(&:hotel).each do |hotel, hotel_bookings|
      booking_ids = hotel_bookings.map(&:id)
      hotel_payments = getPayments(booking_ids)

      # get all @payments where booking id = metadata and then get all amounts from @payments and sum
      total_paid = hotel_bookings.sum(&:total_paid).to_f
      total_subsistence_fees = hotel_bookings.sum(&:calculate_subsistence_fee).to_f

      total_room_cost = hotel.present? ? hotel_bookings.sum(&:calculate_room_cost).to_f : 0
      total_servace_commission = hotel.present? ? hotel_bookings.sum(&:calculate_booking_commission).to_f : 0
      total_due_hotel = total_room_cost - total_servace_commission

      total_servace_stripe_fees = hotel_payments.sum(&:servace_stripe_fee).to_f
      total_stripes_stripe_fees = hotel_payments.sum(&:stripe_fee).to_f

      total_stripe_discrepancy = total_servace_stripe_fees - total_stripes_stripe_fees
      hotel = {
        id: hotel.present? ? hotel.id : 0,
        name: hotel.present? ? hotel.name : 'Subsistence Only',
        total_paid: total_paid,
        total_stripe_fees: total_servace_stripe_fees,
        total_subsistence_fees: total_subsistence_fees,
        total_room_cost: total_room_cost,
        total_servace_commission: total_servace_commission,
        total_due_hotel: total_due_hotel,
        total_stripe_discrepancy: total_stripe_discrepancy,
        months: []
      }

      # for each hotel break down into monthly data chunks
      hotel_bookings.group_by do |b|
        b.first_checkin.beginning_of_month
      end.sort_by { |month| month[0] }.each_with_index do |month, index|
        months_bookings = month[1]
        hotel_id = hotel[:id]
        hotel_name = hotel[:name]
        months_payments = getPayments(months_bookings.map(&:id))

        month_total_paid = months_bookings.sum(&:total_paid).to_f * 100
        month_total_subsistence_fees = months_bookings.sum(&:calculate_subsistence_fee).to_f * 100

        month_room_cost = hotel.present? ? months_bookings.sum(&:calculate_room_cost).to_f * 100 : 0
        month_servace_commission = hotel.present? ? months_bookings.sum(&:calculate_booking_commission).to_f * 100 : 0
        month_due_hotel = month_room_cost - month_servace_commission

        month_servace_stripe_fees = months_payments.sum(&:servace_stripe_fee).to_f
        month_stripes_stripe_fees = months_payments.sum(&:stripe_fee).to_f 

        month_stripe_discrepancy = month_servace_stripe_fees - month_stripes_stripe_fees
        hotel[:months] << {
          key: index,
          date: month[0],
          month_total_paid: month_total_paid,
          month_total_subsistence_fees: month_total_subsistence_fees,
          month_room_cost: month_room_cost,
          month_servace_commission: month_servace_commission,
          month_due_hotel: month_due_hotel,
          month_stripe_fees: month_stripes_stripe_fees,
          month_servace_stripe_fees: month_servace_stripe_fees,
          month_stripe_discrepancy: month_stripe_discrepancy,
          reconciled: BookingPayment.apprentice_payments.where(paymentable_id: @bookings.map(&:id)).where.not(stripe_charge_id: nil).all?(&:payment_reconciled),
          hotel_id: hotel_id,
          hotel_name: hotel_name,
          booking_ids: months_bookings.map(&:id)
        }
      end
      hotels << hotel
    end
    hotels
  end


  # Include other helper methods like get_payments_from_stripe, filter_bookings, etc.
  # def get_payments_from_stripe
  #   # get @payments from stripe
  #   @payments = []
  #   query = 'status:\'succeeded\''
  #   query << if Rails.env.production?
  #              ' AND metadata["environment"]:null'
  #            else
  #              " AND metadata['environment']:'#{Rails.env}'"
  #            end
  #   if @params[:filter_params].present? && @params[:filter_params][:refunded].present?
  #     query << " AND refunded:'#{@params[:filter_params][:refunded]}'"
  #   end
  #   payment_batch = Stripe::Charge.search({ query: query, limit: 100, expand: ['data.balance_transaction.source'] })
  #   @payments.concat(payment_batch.data)

  #   while payment_batch.has_more
  #     payment_batch = Stripe::Charge.search({ query: query, limit: 100, expand: ['data.balance_transaction.source'],
  #                                             page: payment_batch.next_page })
  #     @payments.concat(payment_batch.data)
  #   end

  #   @payments
  # end

  def populate_stripe_fees
    get_payment_data(true)
    payments = StripePayment.where("paymentable_id IN (?) and paymentable_type = 'AccBooking' and stripe_charge_id is not null and stripe_connect_payment = false and (stripe_fee = 0 or stripe_fee = null or servace_stripe_fee = 0 or servace_stripe_fee = null)", @bookings.uniq.pluck(:id))
    payments.each do |payment|
      begin
        balance_transaction = payment.getBalanceTransaction(Stripe::PaymentIntent.retrieve(payment.stripe_charge_id), {})
        payment.update(stripe_fee: balance_transaction.fee, servace_stripe_fee: payment.paymentable.calculate_servace_stripe_fee_total(payment.amount))
      rescue Exception => e
        Rails.logger.error "Error getting balance transaction for payment: #{payment.id} (booking: #{payment.paymentable_id}), #{e.message}"
      end
    end
  end

  def save_hotel_data_to_db
    begin
      @result[:hotels].each do |hotel|
        hotel[:months].each do |month|
          report_month = StripeReportMonth.where(hotel_id: hotel[:id] == 0 ? nil : hotel[:id], subsistence_only: hotel[:id] == 0 ? true : false).where('extract(month from report_date) = ?', month[:date].month).first
          report_month = StripeReportMonth.new if report_month.blank?
          report_month.update(hotel_id: hotel[:id] == 0 ? nil : hotel[:id], 
            report_date: month[:date], 
            hotel_name: hotel[:id] == 0 ? 'Subsistence Only' : hotel[:name],
            total_paid: month[:month_total_paid], 
            total_subsistence_fees: month[:month_total_subsistence_fees], 
            total_room_cost: month[:month_room_cost], 
            total_servace_commission: month[:month_servace_commission], 
            total_due_hotel: month[:month_due_hotel], 
            total_stripe_fees: month[:month_stripe_fees], 
            total_servace_stripe_fees: month[:month_servace_stripe_fees], 
            total_stripe_discrepancy: month[:month_stripe_discrepancy], 
            subsistence_only: hotel[:id] == 0 ? true : false
          )
          report_month.save!
          month[:booking_ids].each do |booking_id|
            booking = AccBooking.find(booking_id)
            payments = booking.booking_payments
            booking_report = StripeReportBookingData.where(acc_booking_id: booking_id).first
            booking_report = StripeReportBookingData.new if booking_report.blank?
            client = booking.acc_booking_header.rfq_location.rfq_request.rfq_programme.client
            booking_report.update(stripe_report_month_id: report_month.id, acc_booking_id: booking_id, hotel_id: hotel[:id] == 0 ? nil : hotel[:id],
              organisation_id: client.id, organisation_name: client.name, total_paid: (booking.total_paid.to_f * 100).to_i,
              total_subsistence_fees: (booking.calculate_subsistence_fee.to_f * 100).to_i, total_room_cost: (booking.calculate_room_cost.to_f * 100).to_i,
              total_servace_commission: (booking.calculate_booking_commission.to_f * 100).to_i, total_due_hotel: ((booking.calculate_room_cost - booking.calculate_booking_commission).to_f * 100).to_i,
              total_stripe_fees: payments.sum(&:stripe_fee), total_servace_stripe_fees: payments.sum(&:servace_stripe_fee), total_stripe_discrepancy: (payments.sum(&:servace_stripe_fee) - payments.sum(&:stripe_fee))
            )
          end
        end
      end
    rescue StandardError => e
      Rails.logger.error "Error saving hotel data to db: #{e.message}"
      raise e
      
    end
  end

  def getPayments(booking_ids)
    StripePayment.apprentice_payments.where(paymentable_id: booking_ids)
  end
end
