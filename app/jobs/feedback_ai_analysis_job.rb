class FeedbackAiAnalysisJob < ApplicationJob
  queue_as :default

  def perform(survey_id, month, year, type, force_refresh: false)
    # Check if we already have a result
    existing_result = FeedbackAiResult.find_by(
      survey_id: survey_id,
      month: month,
      year: year,
      result_type: type
    )

    # Only proceed if we don't have a result or if the existing result failed or if force_refresh
    if existing_result.nil? || existing_result.status == 'failed' || force_refresh
      # Delete failed result to avoid conflicts
      existing_result&.destroy if existing_result&.status == 'failed'
      
      # FeedbackAnalyzer now handles storing the result internally
      result = FeedbackAnalyzer.analyze_survey_responses(month, year, survey_id, type, force_refresh: force_refresh)
    end
  end
end
