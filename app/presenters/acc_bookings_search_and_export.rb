class AccBookingsSearchAndExport

  def initialize(search_params, audience = nil, current_user = nil, booking_id = nil)
    raise "No Audience Present" unless [:hg, :hotel, :client].include? audience
    raise "No User Present" unless current_user.present?
    @current_user = current_user
    @audience = audience
    @search_params = search_params
    @base_search = nil
    @client = nil

    @base = nil
    @base = setup_base
    # TODO lots of the 'possible' methods could be stripped out
    @programme = nil
    if @search_params.rfq_programme_id.present?
      @programme = possible_programmes.where(:id => @search_params.rfq_programme_id).first
    end

    if search_params.booking_id.present?
      @scoped = AccBooking.find(search_params.booking_id)
    end

    if @search_params.client_eq.present?
      @client = Organisation.find @search_params.client_eq
    end

    # Does this @scoped variable do anything anymore? The setup_scope method is still used
    @scoped = setup_scope

    @base_search
  end

  def scoped
    @scoped
  end

  def base
    @base
  end

  def looking_for_booking?
    @found_booking = nil
    id = @search_params.booking_id.to_i
    @found_booking ||= AccBooking.where(:id => id).first if id != 0
    id > 0
  end

  def found_booking
    @found_booking
  end

  def possible_rooms
    RfqResponseRoom::ROOM_TYPES | RfqResponseRoom::ADULT_ROOM_TYPES
  end

  def possible_business_units
    if @programme.present?
      RfqBusinessUnit.where(:rfq_programme_id => @programme.id).
        order("name asc")
    else
      RfqBusinessUnit.where(:rfq_programme_id => self.possible_prog_ids).
        order("name asc")
    end
  end

  def possible_groups
    # AccBookingPerson.select("acc_booking_people.learner_group_code").where("acc_booking_people.learner_group_code is not null").joins(:acc_booking_header => {:rfq_location => :rfq_request}).
    #     where("rfq_requests.rfq_programme_id in (?)", self.possible_prog_ids).uniq.
    #     pluck("acc_booking_people.learner_group_code").uniq.sort
    if @programme.present?
      @programme.group_codes_for_select
    elsif @client.present?
      @client.group_codes_for_select
    else
      []
    end
  end

  def possible_hotel_ids
    if @audience == :hotel
      if @possible_hotel_ids.nil?
        @possible_hotel_ids = @current_user.all_hotels.collect { |x| x.id }
      else
        @possible_hotel_ids
      end
    else
      raise "NOT A SUPPLIER!"
    end
  end

  def possible_programmes
    if @possible_programmes.nil?
      case @audience
      when :hotel
        requests = RfqRequest.joins(:rfq_locations => { :acc_booking_headers => :acc_bookings }).
          where("acc_bookings.hotel_id" => self.possible_hotel_ids).
          where(lost_at: nil)
      when :hg
        requests = RfqRequest.joins(:rfq_locations).where("(rfq_locations.released_at is not null or  rfq_requests.released_at is not null)")
      when :client
        requests = RfqRequest.joins(:rfq_locations).where("(rfq_locations.released_at is not null or  rfq_requests.released_at is not null)").where(:rfq_programme_id => RfqProgramme.allowed_programmes_for(@current_user.contact, nil, "Apprentice"))
      end
      if @search_params&.checkin_year.present?
        yr = @search_params.checkin_year.to_i
        # Changed by ES to prevent using current year if not selected in filter
        requests = requests.where("date_part('year', rfq_requests.end_date) >= :yr and date_part('year', rfq_requests.start_date) <= :yr", :yr => yr)
      else
        # yr = Date.today.year
      end
      @possible_programmes = RfqProgramme.where(:id => requests.map(&:rfq_programme_id).uniq)
      if @search_params.client_eq.present?
        @possible_programmes = @possible_programmes.where("client_id = ? ", @search_params.client_eq)
      end
    end
    @possible_programmes.order("name ASC")
  end

  def possible_tags
    @possible_tags = []
    if @current_user.is_an_administrator?
      if @search_params.client_eq.present?
        organisation = Organisation.find(@search_params.client_eq)
        @possible_tags = filter_tags(organisation)
      else
        return []
      end
    else
      organisation = @current_user.contact.parent
      @possible_tags = filter_tags(organisation)
    end
    @possible_tags
  end

  def filter_tags(organisation)
    tags = organisation.tags.order("name ASC").select('id, name')
    return [] if tags.empty?
    tags = tags.uniq {|t| t.name}
    tags
  end

  def possible_prog_ids
    if @possible_prog_ids.present?
      @possible_prog_ids
    else
      @possible_prog_ids = possible_programmes.pluck(:id)
    end
  end

  def possible_hotels
    base = @base_search.where(rfq_lost_at: nil)
    base = base.where(client_id: @client.id) if @client.present?
    # This sort of thing is not performant, as it queries the whole table without pagination
    hotel_ids = base.where("bookings_results.hotel_id IS NOT NULL").pluck("bookings_results.hotel_id").uniq
    Hotel.where(id: hotel_ids).order("hotels.name ASC")
  end

  def live_programmes
    if @live_programmes.nil?
      @live_programmes = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_locations.released_at is not null").
        where("rfq_requests.lost_at is null").
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_programmes.id in (?)", possible_prog_ids).distinct
    else
      @live_programmes
    end
  end

  def live_orgs
    # TODO could this be cached?
    Organisation.joins(:rfq_programmes => { :rfq_requests => :rfq_locations }).
      where("rfq_programmes.rfq_type = ?", "Apprentice").
      where("rfq_requests.lost_at is null").
      where("rfq_requests.end_date >= ?", Date.today).
      where("(rfq_locations.released_at is not null or rfq_requests.released_at is not null)").
      distinct.order("name ASC")
  end

  def headers
    my_headers = []
  end

  def setup_base
    @base_search = BookingsResult.all
    if @current_user.is_a_supplier?
      @base_search = @base_search.where("bookings_results.hotel_id in (?)", self.possible_hotel_ids).or(
        @base_search.where("bookings_results.subsistence_only = 't' and bookings_results.subsistence_provider = 'Hotel' and bookings_results.rfq_proposed_hotel_id in (?)", self.possible_hotel_ids)
      )
    elsif @current_user.is_a_client?
      @base_search = @base_search.where("prog_id in (?)", self.possible_prog_ids)
    end
    @base_search
  end

  def setup_scope
    apply_std_filters
    apply_sort
  end

  def stripe_charge_ids
    @stripe_ids = {}
    @base_search
      .joins(:booking_payments)
      .select(
        "bookings_results.id, " \
        "array_agg(stripe_payments.stripe_charge_id ORDER BY stripe_payments.id ASC) AS stripe_charge_ids, " \
        "array_agg(stripe_payments.stripe_refund_id ORDER BY stripe_payments.id ASC) AS stripe_refund_ids, " \
        "array_agg((stripe_payments.amount / 100.00) ORDER BY stripe_payments.id ASC) AS stripe_amount"
      )
      .group("bookings_results.id")
      .reorder(nil)
      .map do |b|
        @stripe_ids[b.id] = {
          charge_ids: b.stripe_charge_ids.reject(&:nil?),
          refund_ids: b.stripe_refund_ids.reject(&:nil?),
          amounts: b.stripe_amount.reject(&:nil?)
        }
      end
  end

  def export(filename = nil)
    filename ||= "apprentice-bookings-#{Time.zone.now.to_fs(:long)}.xls"
    path = "#{Rails.root}\/tmp\/#{filename}"
    book = WriteExcel.new(path)
    sheet = book.add_worksheet
    num_fmt = book.add_format
    num_fmt.set_num_format("#,##0.00")
    date_fmt = book.add_format
    date_fmt.set_num_format('dd-mmm-yyyy hh:mm')
    date_fmt.set_align('vjustify')
    no_time = book.add_format
    no_time.set_num_format('dd-mmm-yyyy')
    no_time.set_align('vjustify')
    #add headers
    hdrs = report_headers_for_user
    sheet.write_row(0, 0, hdrs)
    #set column widths
    sheet.set_column(0, hdrs.size - 1, 22)
    #add rows

    @base_search.each_with_index do |booking, idx|
      bpres = AccBookingPresenter.new(booking, true)
      data = gen_report_row(bpres)
      case @audience
      when :hg
        write_hg_row(data, sheet, idx, num_fmt, date_fmt, no_time)
      when :client
        write_client_row(data, sheet, idx, num_fmt, date_fmt, no_time)
      when :hotel
        write_supp_row(data, sheet, idx, num_fmt, date_fmt, no_time)
      end
    end
    book.close
    return path
  end

  def charge_ids_for_booking(id)
    stripe_charge_ids
    h = @stripe_ids[id]

    if h.present?
      (h[:charge_ids] + h[:refund_ids]).join(", ")
    else
      ""
    end
  end

  def stripe_amounts_for_booking(id)
    stripe_charge_ids
    h = @stripe_ids[id]
    if h.present?
      h[:amounts].join(", ")
    else
      ""
    end
  end

  def write_hg_row(data, sheet, idx, num_fmt, date_fmt, no_time)
    data.each_with_index do |cell, ptr|
      begin
        cell_value = cell.nil? ? '' : cell.to_s.strip
        if [43, 44, 46, 47, 48].include?(ptr)
          sheet.write(idx + 1, ptr, cell_value, num_fmt)
        elsif [28, 35, 36].include?(ptr)
          sheet.write_date_time(idx + 1, ptr, cell_value, no_time)
        elsif [53, 55, 56, 58, 60, 61, 62].include?(ptr)
          sheet.write_date_time(idx + 1, ptr, cell_value, date_fmt)
        else
          # Force string interpretation for cell values that could be mistaken as references
          sheet.write(idx + 1, ptr, cell_value.start_with?('=') ? "'#{cell_value}" : cell_value)
        end
      rescue => e
        Rails.logger.warn "Error writing cell at row #{idx + 1}, column #{ptr}: #{e.message}"
        Rails.logger.warn "Problematic data: #{cell.inspect}"
        # Optionally, write a placeholder or skip the cell
        sheet.write(idx + 1, ptr, "ERROR: #{e.message} - Cell data: #{cell.inspect}")
      end
    end
  end

  def write_client_row(data, sheet, idx, num_fmt, date_fmt, no_time)
    data.each_with_index do |cell, ptr|
      if [38, 39, 43, 44].include?(ptr)
        sheet.write(idx + 1, ptr, cell, num_fmt)
      elsif [25, 32, 33].include?(ptr)
        sheet.write_date_time(idx + 1, ptr, cell, no_time)
      elsif [49, 51, 52, 54, 56, 57, 58].include?(ptr)
        sheet.write_date_time(idx + 1, ptr, cell, date_fmt)
      else
        sheet.write(idx + 1, ptr, cell)
      end
    end
  end

  def write_supp_row(data, sheet, idx, num_fmt, date_fmt, no_time)
    data.each_with_index do |cell, ptr|
      if [27, 28, 30, 31, 32].include?(ptr)
        sheet.write(idx + 1, ptr, cell, num_fmt)
      elsif [25, 26, 35, 36].include?(ptr)
        sheet.write_date_time(idx + 1, ptr, cell, no_time)
      elsif [37].include?(ptr)
        sheet.write_date_time(idx + 1, ptr, cell, date_fmt)
      else
        sheet.write(idx + 1, ptr, cell)
      end
    end
  end

  def gen_report_row(bp)
    case @audience
    when :hg
      [
        bp.id,
        bp.booking_reference,
        bp.learner_id,
        bp.client_name,
        bp.programme_name,
        bp.rfq_name,
        bp.hotel_name || bp.hotel_name_conferma,
        bp.hotel_city,
        bp.hotel_county,
        bp.rfq_type,
        bp.room_type,
        bp.single_reason,
        bp.person_type,
        bp.group_code,
        bp.learner_reference,
        bp.person_forename,
        bp.person_surname,
        bp.person_telephone,
        bp.person_email,

        bp.learner_programme_name,
        bp.learner_start_date,
        bp.learner_proposed_end_date,
        bp.learner_course_code,
        bp.learner_additional_information,
        bp.learner_employer_name,
        bp.learner_employer_contact_email,
        bp.learner_employer_contact_number,
        bp.learner_levy,

        bp.dob.to_s,
        bp.age,
        bp.gender,
        bp.room_pkg,
        bp.transport_included,
        bp.special_requirements,
        bp.rooming_preference,
        bp.stay_check_in.to_s,
        bp.stay_check_out.to_s,
        bp.nice_vslot,
        bp.link_clicked_at.present? ? "Yes" : "No",
        bp.nights.to_i,
        bp.pppn_inc_vat.to_f,
        charge_ids_for_booking(bp.id),
        stripe_amounts_for_booking(bp.id),
        bp.export_stay_cost.to_f,
        bp.export_stay_sub_cost.to_f,
        bp.response_parking_charge,
        ( bp.export_stay_cost.to_f + bp.export_stay_sub_cost.to_f + bp.response_parking_charge.to_f ),
        bp.commission.to_f,
        bp.business_unit,
        bp.centre_name,
        bp.manager_name,
        bp.manager_email,
        bp.manager_telephone,
        bp.booked_at.to_s,
        bp.booked_by,
        bp.hotel_confirmed_at.to_s,
        bp.confirmed_at.to_s,
        bp.confirmed_by,
        bp.amended_at.to_s,
        bp.amended_by,
        bp.last_early_arrival_at.to_s,
        bp.last_non_arrival_at.to_s,
        bp.cancelled_at.to_s,
        bp.cancelled_by,
        bp.cancellation_reason,
        bp.hg_note,
        bp.real_pay_method,
        bp.account_number,
        bp.booking_cost_code,
        bp.client_note,
        bp.client_block_note,
        bp.train_details_req,
        bp.from_station,
        bp.joining_instructions_title,
        bp.tags
      ] + bp.confirmation_answers
    when :client
      [
        bp.id,
        bp.booking_reference,
        bp.programme_name,
        bp.rfq_name,
        bp.hotel_name || bp.hotel_name_conferma,
        bp.rfq_type,
        bp.room_type,
        bp.single_reason,
        bp.person_type,
        bp.group_code,
        bp.learner_reference,
        bp.learner_email,
        bp.person_forename,
        bp.person_surname,
        bp.person_telephone,
        bp.person_email,

        bp.learner_programme_name,
        bp.learner_start_date,
        bp.learner_proposed_end_date,
        bp.learner_course_code,
        bp.learner_additional_information,
        bp.learner_employer_name,
        bp.learner_employer_contact_email,
        bp.learner_employer_contact_number,
        bp.learner_levy,

        bp.dob.to_s,
        bp.age,
        bp.gender,
        bp.room_pkg,
        bp.transport_included,
        bp.special_requirements,
        bp.rooming_preference,
        bp.stay_check_in.to_s,
        bp.stay_check_out.to_s,
        bp.nice_vslot,
        bp.link_clicked_at.present? ? "Yes" : "No",
        bp.nights.to_f,
        bp.pppn_inc_vat.to_f,
        charge_ids_for_booking(bp.id),
        stripe_amounts_for_booking(bp.id),
        bp.export_stay_cost.to_f,
        bp.export_stay_sub_cost.to_f,
        bp.response_parking_charge,
        ( bp.export_stay_cost.to_f + bp.export_stay_sub_cost.to_f + bp.response_parking_charge.to_f ),
        bp.business_unit,
        bp.centre_name,
        bp.manager_name,
        bp.manager_email,
        bp.manager_telephone,
        bp.booked_at.to_s,
        bp.booked_by,
        bp.hotel_confirmed_at.to_s,
        bp.confirmed_at.to_s,
        bp.confirmed_by,
        bp.amended_at.to_s,
        bp.amended_by,
        bp.last_early_arrival_at.to_s,
        bp.last_non_arrival_at.to_s,
        bp.cancelled_at.to_s,
        bp.cancelled_by,
        bp.cancellation_reason,
        bp.real_pay_method,
        bp.account_number,
        bp.booking_cost_code,
        bp.client_note,
        bp.client_block_note,
        bp.train_details_req,
        bp.from_station,
        bp.joining_instructions_title,
        bp.tags
      ] + bp.confirmation_answers
    when :hotel
      [
        bp.id,
        bp.booking_reference,
        bp.programme_name,
        bp.rfq_name,
        bp.hotel_name || bp.hotel_name_conferma,
        bp.room_type,
        bp.single_reason,
        bp.manager_name,
        bp.manager_email,
        bp.manager_telephone,
        bp.person_type,
        bp.group_code,
        bp.learner_course_code,
        bp.learner_reference,
        bp.person_forename,
        bp.person_surname,
        bp.person_telephone,
        bp.gender,
        bp.age,
        bp.business_unit,
        bp.centre_name,
        bp.room_pkg,
        bp.transport_included,
        bp.special_requirements,
        bp.rooming_preference,
        bp.stay_check_in.to_s,
        bp.stay_check_out.to_s,
        bp.nights.to_f,
        bp.pppn_inc_vat.to_f,
        charge_ids_for_booking(bp.id),
        stripe_amounts_for_booking(bp.id),
        bp.export_stay_cost.to_f,
        bp.export_stay_sub_cost.to_f,
        bp.response_parking_charge,
        ( bp.export_stay_cost.to_f + bp.export_stay_sub_cost.to_f + bp.response_parking_charge.to_f ),
        bp.last_early_arrival_at.to_s,
        bp.last_non_arrival_at.to_s,
        bp.cancelled_at.to_s,
        bp.real_pay_method,
        bp.account_number,
        bp.booking_cost_code] + bp.confirmation_answers
    else
      raise "wrong audience for report: #{@audience}"
    end
  end

  def report_headers_for_user
    case @audience
    when :hg
      [
        "ID", "Booking Reference", "Learner ID", "Client", "Programme Name","Course Name", "Hotel Name", "Hotel City", "Hotel County", "RFQ Type",
        "Room Type", "Single room reason", "Guest Type", "Group Code", "Learner Ref", "Guest Forename",
        "Guest Surname", "Guest Phone", "Guest Email",
        "Programme Name", "Guest Start Date", "Guest End Date", "Guest Course Code", "Guest Additional Information",
        "Employer Name", "Employer Contact Email", "Employer Contact Number", "Guest Levy",
        "Date of Birth", "Age at stay", "Gender",
        "Package Type", "Transport Included?", "Special Requirements",
        "Rooming Preference", "Check-In", "Check-Out", "Virtual Slot", "Virtual Link Clicked", "Total Nights", "Cost per night", "Stripe Charge IDs", "Stripe Amount Paid",
        "Stay Cost", "Subsistence Due", "Parking Charge Per Day (£)", "Total Cost", "Commission %", "Business Unit Name", "Center Name", "Manager", "Manager Email", "Manager Tel",
        "Booked", "Booked by", "Hotel Confirmed", "Confirmed", "Confirmed By", "Amended",
        "Amended By", "Early Arrival Recorded", "Non Arrival Recorded", "Cancelled", "Cancelled By", "Cancellation Reason", "HG Note", "Payment Method", "Account Number", "Cost Code", "Client Note", "Client Block Note",
        "Train Tickets", "From Station",  "Joining Instructions Title", "Tags", "Confirmation Answers",
      ]
    when :client
      [
        "ID", "Booking Reference", "Programme Name","Course Name", "Hotel Name", "RFQ Type",
        "Room Type", "Single room reason", "Guest Type", "Group Code", "Learner Ref", "Learner Email", "Guest Forename", "Guest Surname",
        "Guest Phone", "Guest Email",
        "Programme Name", "Guest Start Date", "Guest End Date", "Guest Course Code", "Guest Additional Information",
        "Employer Name", "Employer Contact Email", "Employer Contact Number", "Guest Levy",
        "Date of Birth", "Age at stay", "Gender",
        "Package Type", "Transport Included?", "Special Requirements",
        "Rooming Preference", "Check-In", "Check-Out", "Virtual Slot", "Virtual Link Clicked", "Total Nights", "Cost per night", "Stripe Charge IDs", "Stripe Amount Paid",
        "Stay Cost", "Subsistence Due", "Parking Charge Per Day (£)", "Total Cost", "Business Unit Name", "Center Name", "Manager", "Manager Email", "Manager Tel",
        "Booked", "Booked by", "Hotel Confirmed", "Confirmed", "Confirmed By", "Amended",
        "Amended By", "Early Arrival Recorded", "Non Arrival Recorded", "Cancelled", "Cancelled By", "Cancellation Reason", "Payment Method", "Account Number", "Cost Code", "Client Note",
        "Client Block Note", "Train Tickets", "From Station",  "Joining Instructions Title", "Tags", "Confirmation Answers"
      ]
    when :hotel
      [
        "ID", "Booking Reference", "Programme Name", "Course Name", "Hotel Name",
        "Room Type", "Single room reason", "Manager Name", "Manager Email", "Manager Telephone", "Guest Type", "Group Code", "Guest Course Code", "Learner Ref", "Guest Forename",
        "Guest Surname", "Guest Telephone", "Gender", "Age", "Business Unit", "Training Centre",
        "Package Type", "Transport Included?", "Special Requirements",
        "Rooming Preference", "Check-In", "Check-Out", "Total Nights", "Cost per night", "Stripe Charge IDs", "Stripe Amount Paid",
        "Stay Cost", "Subsistence Due",  "Parking Charge Per Day (£)", "Total Cost", "Early Arrival Recorded", "Non Arrival Recorded",
        "Cancelled", "Payment Method", "Account Number", "Cost Code", "Confirmation Answers"
      ]
    else
      raise "wrong audience for report: #{@audience}"
    end
  end

  def export_dupes(bk_ids = [])
    @scoped = @scoped.where("id in (?)", bk_ids)
    self.export("possible_dupe_app_bookings_#{Date.today.yesterday}.xls")
  end

  def apply_std_filters
    if @search_params.tags.present?
      # We need the name not id, as we don't necessarily get all the ids in the search
      tag_ids = @search_params.tags.reject(&:blank?)
      unless tag_ids.empty?
        tags = Tag.where(id: tag_ids)
        tag_names = tags.map(&:name)
        @base_search = @base_search.joins("left join tags on tags.taggable_id = acc_booking_headers.id and tags.taggable_type = 'AccBookingHeader'").
          where("tags.name in (?)", tag_names)
      end
    end

    if @search_params.course_name_like.present?
      compare = "%" + @search_params.course_name_like + "%"
      @base_search = @base_search.where("(rfq_name ilike ?)", compare)
    end

    if @search_params.confirmation_like == "trainer hotel not confirmed"
      @base_search = trainer_hotel_not_confirmed(@base_search)
      return @base_search
    end

    if @search_params.confirmation_like == "apprentice hotel not confirmed"
      @base_search = apprentice_not_confirmed(@base_search, @search_params.days_to_go, "hotel")
      return @base_search
    end

    if @search_params.confirmation_like == "apprentice client not confirmed"
      @base_search = apprentice_not_confirmed(@base_search, @search_params.days_to_go, "client")
      return @base_search
    end

    if @search_params.confirmation_like == "Hotel Confirmed"
      @base_search = @base_search.where("hotel_confirmed_at is not null")
    end

    if @search_params.confirmation_like == "Hotel Not Confirmed"
      @base_search = @base_search.where("hotel_confirmed_at is null")
    end

    if @current_user.is_a_client?
      @base_search = @base_search.where("l_client_hidden is not true")
    end

    if @search_params.rfq_programme_id.present?
      @base_search = @base_search.where("prog_id = ?", @search_params.rfq_programme_id.to_i)
    end

    if @search_params.booking_reference.present?
      @base_search = @base_search.where("booking_ref = ?", @search_params.booking_reference.to_i)
    end

    if @search_params.booking_type == "Provisional - No Name"
      @base_search = @base_search.where("confirmed_at is null and cancelled_at is null and confirmed_by is null and confirmed_flag = ? and person_type = ?", false, "PRO")
    end

    if @search_params.booking_type == "Provisional - Name"
      @base_search = @base_search.where("confirmed_at is null and cancelled_at is null and confirmed_by is null and confirmed_flag = ?  and person_type = ?", false, "LEA")
    end

    if @search_params.booking_type == "Not Confirmed - Unpaid"
      @base_search = @base_search.where("confirmed_at is null and cancelled_at is null")
    end

    if @search_params.booking_type == "Provisional"
      @base_search = @base_search.where("confirmed_at is null and cancelled_at is null and confirmed_by is null and confirmed_flag = ?", false)
    end

    if @search_params.booking_type == "Confirmed - Paid"
      @base_search = @base_search.where("confirmed_at is not null and cancelled_at is null")
    end

    if @search_params.booking_type == "Cancelled"
      @base_search = @base_search.where("cancelled_at is not null")
    end

    if @search_params.booking_type == "Not Cancelled"
      @base_search = @base_search.where("cancelled_at is null")
    end

    if @search_params.booking_type == "Declined - Not Resolved"
      @base_search = @base_search.where("hotel_declined_at is not null and resolved_at is null")
    end

    if @search_params.booking_type == "Declined - Resolved"
      @base_search = @base_search.where("hotel_declined_at is not null and resolved_at is not null")
    end

    if @search_params.booking_type == "Amended"
      @base_search = @base_search.where("amended_at is not null and cancelled_at is null")
    end

    if @search_params.checkin_start.present? && @search_params.checkin_end.blank?
      @base_search = @base_search.where("stay_check_in >= ?", @search_params.checkin_start)
    end

    if @search_params.checkin_end.present? && @search_params.checkin_start.blank?
      @base_search = @base_search.where("stay_check_in <= ?", @search_params.checkin_end)
    end

    if @search_params.checkin_start.present? && @search_params.checkin_end.present?
      @base_search = @base_search.where("stay_check_in <= ? and stay_check_out >= ? ", @search_params.checkin_end, @search_params.checkin_start)
    end

    if @search_params.room_type.present?
      @base_search = @base_search.where("room_type = ?", @search_params.room_type)
    end

    if @search_params.person_surname_like.present?
      compare = "%" + @search_params.person_surname_like + "%"
      @base_search = @base_search.where("(s_surname ilike ? or l_surname ilike ? or t_surname ilike ?)", compare, compare, compare)
    end

    if @search_params.manager_like.present?
      compare = "%" + @search_params.manager_like + "%"
      @base_search = @base_search.where("manager ilike ?", compare)
    end

    if @search_params.business_unit.present?
      @base_search = @base_search.where("bunit_id = ?", @search_params.business_unit)
    end

    if @search_params.amended_after.present?
      @base_search = @base_search.where("(amended_at >= ? or cancelled_at >= ?)", @search_params.amended_after, @search_params.amended_after)
    end

    if @search_params.group_code.present? && @audience == :hotel
      @base_search = @base_search.where("group_code ilike ?", ["%", @search_params.group_code, "%"].join)
    elsif @search_params.group_code.present?
      @base_search = @base_search.where("group_code = ?", @search_params.group_code)
    end

    if @search_params.course_code.present?
      @base_search = @base_search.where("l_course_code ilike ?", ["%", @search_params.course_code, "%"].join)
    end

    if @search_params.client_eq.present?
      @base_search = @base_search.where("client_id = ?", @search_params.client_eq.to_i)
    end

    if @search_params.client_id.present?
      @base_search = @base_search.where("client_id = ?", @search_params.client_id.to_i)
    end

    if @search_params.hotel_id.present?
      @base_search = @base_search.where("bookings_results.hotel_id = ?", @search_params.hotel_id)
    end

    if @search_params.person_type.present?
      @base_search = person_type_clause(@base_search)
    end

    if @search_params.subsistence_only == "1" || @search_params.subsistence_only == "true"
      @base_search = @base_search.where("subsistence_only is true")
    end
    if @search_params.without_subsistence == "1" || @search_params.without_subsistence == "true"
      @base_search = @base_search.where("subsistence_only is false ")
    end

    case @search_params.rfq_type
    when "Virtual"
      @base_search = @base_search.where("rfq_mode = 'Virtual'")
    when "Residential"
      @base_search = @base_search.where("rfq_mode = 'Residential'")
    else
      #nothing
    end

    case @search_params.hotel_confirmed
    when "Confirmed"
      @base_search = @base_search.where.not(hotel_confirmed_at: nil)
    when "Declined (Resolved)"
      # we only want declined bookings that are resolved
      # had to do this in string syntax because for some reason the active record not syntax was returning incorrect results
      @base_search = @base_search.where('hotel_declined_at is not null and resolved_at is not null')
    when "Declined (Unresolved)"
      # we only want declined bookings that are not resolved
      @base_search = @base_search.where('hotel_declined_at is not null and resolved_at is null')
    when "Unprocessed"
      @base_search = @base_search.where(hotel_confirmed_at: nil, hotel_declined_at: nil)
    end

    if @search_params.late_booked.present? && !@search_params.late_booked.empty?
      @base_search = @base_search.where(rfq_late_bookings_enabled: true)
      case @search_params.late_booked
      when "Late Booked"
        @base_search = @base_search.where("(DATE(created_at) + late_booking_threshold) >= first_checkin")
      when "Not Late Booked"
        @base_search = @base_search.where("(DATE(created_at) + late_booking_threshold) <= first_checkin")
      end
    end


    if @search_params.epa_booking.present? && !@search_params.epa_booking.empty?
      case @search_params.epa_booking
      when "EPA"
        @base_search = @base_search.where(epa_booking: true)
      when "Non-EPA"
        @base_search = @base_search.where(epa_booking: false)
      end
    end

    if @search_params.epa_booking == "1"
      @base_search = @base_search.where(epa_booking: true)
    end

    case @search_params.payment_method
      when 'Stripe Card'
        @base_search = @base_search.joins(:booking_payments).where(booking_payments: {payment_type: StripePayment.payment_types[:card]})
      when 'Stripe BACS'
        @base_search = @base_search.joins(:booking_payments).where(booking_payments: {payment_type: StripePayment.payment_types[:bacs_debit]})
      when 'Stripe Card/BACS'
        @base_search = @base_search.joins(:booking_payments).where(booking_payments: {payment_type: [StripePayment.payment_types[:card], StripePayment.payment_types[:bacs_debit]]})
      when 'Credit Account'
        @base_search = @base_search.joins(:booking_payments).where(booking_payments: {payment_type: StripePayment.payment_types[:credit_account]})
      when 'Non Stripe'
        @base_search = @base_search.where(card_pay: false)
    end

    if @search_params.checkin_year.present?
      @base_search = @base_search.where("first_checkin >= ? and first_checkin <= ? ", Date.new(@search_params.checkin_year.to_i - 1, 9, 1), Date.new(@search_params.checkin_year.to_i, 8, 1))
    end

    if @search_params.created_on_after.present?
      @base_search = @base_search.where("bookings_results.created_at >= ?", Date.parse(@search_params.created_on_after))
    end
    if @search_params.booking_id.present?
      @base_search = @base_search.where("bookings_results.id = ?", @search_params.booking_id.to_i)
    end

    if @search_params.learner_type.present?
      case @search_params.learner_type
      when "Adults Only"
        @base_search = @base_search.where("l_adult = 't'")
      when "Apprentices Only"
        @base_search = @base_search.where("l_adult <> 't'")
      end
    end

    if @search_params.click.present?
      case @search_params.click
      when "Clicked"
        @base_search = @base_search.where("rfq_link_clicked is true and link_clicked_at is not null")
      when "Not Clicked"
        @base_search = @base_search.where("rfq_link_clicked is true and link_clicked_at is null")
      else
      end
    end

    case @search_params.under_18
    when "Yes"
      @base_search = @base_search.where('dob > ?', Date.today - 18.years)
    when "No"
      @base_search = @base_search.where('dob < ?', Date.today - 18.years)
    end

    case @search_params.payment_status
    when "Pending"
      @base_search = @base_search.joins(:booking_payments).where(booking_payments: {status: 'pending'})
    when "Failed"
      @base_search = @base_search.joins(:booking_payments).where(booking_payments: {status: 'failed'})
    when "Unprocessed"
      @base_search = @base_search.where.not('exists (?)', StripePayment.where('stripe_payments.paymentable_id = id'))
    end

    case @search_params.payment_destination
    when "Servace"
      @base_search = @base_search.joins(:booking_payments).where(booking_payments: {stripe_connect_payment: false})
    when "Connect"
      @base_search = @base_search.joins(:booking_payments).where(booking_payments: {stripe_connect_payment: true})
    end

    if @search_params.joining_instruction_id.present?
      @base_search = @base_search.where("joining_instruction_id = ?", @search_params.joining_instruction_id)
    end
  end

  # Done
  def trainer_hotel_not_confirmed(bookings)
    bookings = bookings.where("created_at BETWEEN ? AND ?", (Date.today - 30.days), (Date.today + 30.days))
    .where("hotel_confirmed_at is null")
    .where("cancelled_at is null")
    .where("person_type = ? ", "TRA")
    return bookings
  end

  # Done
  def apprentice_not_confirmed(bookings, days_to_go, who_is_not_confirmed)
    days_to_go = days_to_go.to_i

    if @current_user.is_a_client?
      # bookings = bookings.joins(:acc_booking_header => { :rfq_location => { :rfq_request => { :rfq_programme => :client } } })
      bookings = bookings.where(client_id: @current_user.contact.parent.id)
    # else
      # bookings = bookings.joins(:acc_booking_header => {:rfq_location => :rfq_request})
    end

    bookings = bookings.where("person_type in (?)", ["LEA", "PRO"])
    .where("first_checkin BETWEEN ? AND ?", (Date.today), (Date.today + days_to_go.days))
    .where("cancelled_at is null")
    .where("subsistence_only is false AND virtual_flag is false")

    if who_is_not_confirmed == "hotel"
      bookings = bookings.where("hotel_confirmed_at is null")
                          .where("subsistence_only = ?", false)
                          .where("rfq_mode = ?", RfqRequest::MODES[0])

    elsif who_is_not_confirmed == "client"
      bookings = bookings.where("confirmed_at is null")
    end
    return bookings
  end

  # Done
  def person_type_clause(search)
    case @search_params.person_type
    when "LEA", "PRO", "TRA"
      search.where("person_type = ? ", @search_params.person_type)
    when "LEA & PRO"
      search.where("person_type in ('LEA', 'PRO') ")
    when "ADU"
      search.where("person_type ='LEA' and l_adult = 't' ")
    when "APP"
      search.where("person_type ='LEA' and l_adult <> 't' ")
    end
  end

  # Done
  def apply_sort
    @base_search = @base_search.order("stay_check_in ASC,l_surname ASC")
  end

  def add_additional_export_fields

  end

  def to_csv

  end

end
