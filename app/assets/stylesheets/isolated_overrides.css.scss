.bs5{

/*** Added by Stof ***/
body {
    margin: 0;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 13px;
    line-height: 18px;
    color: #807f83;
    background-color: #EFEFF0;
  }

  body.modal-open {

    overflow: hidden;

  }

  h1,
  h2 {
    font-size: 21px;
    line-height: 36px;
  }

  h3 {
    font-size: 18px;
    line-height: 27px;
  }

  h4 {
    font-size: 14px;
    line-height: 18px;
    margin: 0;
  }

  h1,
  h2,
  h3 {
    margin: 0 0 18px 0;
  }

  .alert {

    h1,
    h2,
    h3,
    h4 {
      margin: 5px 0;
    }
  }

  .alert-warning {

    h1,
    h2,
    h3,
    h4 {
      color: #c09853 !important;
    }
  }

  .alert-danger {

    h1,
    h2,
    h3,
    h4 {
      color: #b94a48 !important;
    }

    background-color:#F2DEDE !important;
  }

  #bg {
    width: 75rem;
    margin: 0 auto 0 auto;
    padding: 30px 0 0 0;
    background: #fff;
    -moz-box-shadow: 0 0 0 1px #fff, 0px 0px 20px rgba(0, 0, 0, .05);
    -webkit-box-shadow: 0 0 0 1px #fff, 0px 0px 20px rgba(0, 0, 0, .05);
    box-shadow: 0 0 0 1px #fff, 0px 0px 20px rgba(0, 0, 0, .05);
  }

  a {
    -webkit-transition: background-color 100ms ease;
    -moz-transition: background-color 100ms ease;
    -o-transition: background-color 100ms ease;
    -ms-transition: background-color 100ms ease;
    transition: background-color 100ms ease;
  }

  .hg,
  .os {
    a {
      color: #5b3768;
      text-decoration: none;
    }
  }

  .hg,
  .os {
    a:hover {
      color: #8c27b0;
      text-decoration: underline;
    }
  }

  .cs a {
    color: #ad1a59;
  }

  .cs a:hover {
    color: #a30143;
  }

  .as a {
    color: #00a7e5;
  }

  .as a:hover {
    color: #008bbf;
  }

  .ap a {
    color: #004F59;
  }

  .ap a:hover {
    color: #63951d;
  }

  .es a {
    color: #f8981d;
  }

  .es a:hover {
    color: #d67801;
  }

  #top-nav {
    position: fixed;
    background: #232B31;
    color: #fff;
    width: 100%;
    border-bottom: 1px solid #fff;
    top: 0;
    z-index: 250;
    left: 0;
  }

  #top-nav ul {
    float: left;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  #top-nav ul li {
    float: left;
    margin: 0;
    line-height: 18px;
  }

  #top-nav ul li a {
    float: left;
    padding: 7px 15px;
    color: #ececec;
  }

  #top-nav ul li a:hover,
  #top-nav ul li a:focus,
  #top-nav ul li a.active {
    color: #fff;
    background-color: #3c464d;
    text-decoration: none;
  }

  #top-nav .divider-vertical {
    width: 1px;
    height: 30px;
    margin: 0;
    overflow: hidden;
    background-color: #0e1215;
    border-right: 1px solid #3c464d;
  }

  #top-nav li a.icon {
    float: left;
    color: #fff;
    text-decoration: none;
    background-image: url(/assets/imgphase3/icon-set.png);
    text-indent: -9999px
  }

  #top-nav li a.fb {
    background-position: -97px 103px
  }

  #top-nav li a.tweet {
    background-position: 3px -97px
  }

  #top-nav li a.email {
    background-position: 3px 3px;
  }

  #top-nav li a.gplus {
    background-position: -397px 3px
  }

  #top-nav li a.linkedin {
    background-position: -197px -97px
  }

  #top-nav li a.rss {
    background-position: -97px 3px
  }

  #top-nav #header-right {
    float: right;
    /*width: 440px; */
    width: auto;
    margin: 0;
  }

  #top-nav #header-right li {
    float: left;
    text-transform: none;
  }

  #top-nav #header-right li a {
    padding: 7px 9px;
  }

  #top-nav form {
    margin: 0 0 0 5px;
    padding: 5px 0 0 0;
    /*display:inline;
    *display: inline;   */
    float: left;
  }

  #top-nav form input {
    font-size: 11px;
  }

  #top-nav form {

    input[type="text"],
    input[type="password"],
    input[type="email"] {
      display: inline-block;
      padding: 0 4px;
      font-size: 13px;
      line-height: 18px;
      color: #fff;
      height: 21px;
      background: #666;
      border: 1px solid #0e1215;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
    }
  }

  #top-nav .btn-mini {
    line-height: 18px;
  }

  #top-nav .button_to .btn-mini {
    margin-top: 2px;
    *margin-top: 1px;
  }

  #top-nav {

    label,
    input {
      font-size: 13px;
      font-weight: normal;
      line-height: 18px;
    }
  }

  .smallertext {
    font-size: 11px;
    line-height: 14px;
  }

  .header {
    margin-top: 30px;
    margin-bottom: 30px;
  }

  .strapline {
    padding: 20px 0 0 0;
    font-size: 16px;
    line-height: 14px;
  }

  .strapline strong {
    font-size: 16px;
    font-weight: 400;
  }

  .strapline p {
    margin-bottom: 0;
  }

  .strapline p.small {
    font-size: 11px;
    line-height: 14px;

    a {
      color: #807f83;
    }
  }

  .strapline p.call {
    margin-top: 10px;

    strong {
      font-size: 20px;
    }
  }

  .hg,
  .os {
    .strapline strong {
      color: #5B3768;
    }
  }

  .cs .strapline strong {
    color: #B7355F;
  }

  .as .strapline strong {
    color: #00a7e5;
  }

  .ap .strapline strong {
    color: #004F59;
  }

  .es .strapline strong {
    color: #f8981d;
  }

  .ads {
    margin: 20px 0 20px 0;
  }

  .ads img {
    width: 100%;
  }

  .btn-stuck-top>.btn:first-child {
    -webkit-border-top-left-radius: 0;
    border-top-left-radius: 0;
    -moz-border-radius-topleft: 0;
  }

  .btn-stuck-top>.btn:last-child,
  .btn-stuck-top>.dropdown-toggle {
    -webkit-border-top-right-radius: 0;
    border-top-right-radius: 0;
    -moz-border-radius-topright: 0;
  }

  .btn.btn-wrap {
    max-width: 80%;
    word-wrap: break-word;
  }

  .gotoref {
    margin: 45px 0 10px 0;
  }

  .gotoref #opportunity_id {
    width: 140px;
  }

  /****   maintabs   */
  .maintabs {
    margin: 10px 0 10px 0;
  }

  .maintabs li {
    margin-bottom: -2px;
  }

  .maintabs li a {
    color: #b0b0b2;
    background-image: url(/assets/imgphase3/pins.png);
    background-repeat: no-repeat;
    padding-left: 24px;
    line-height: 18px;
    *line-height: 16px !important;
  }

  .maintabs li a strong,
  .maintabs li.active a:hover strong,
  .maintabs li.active a:hover .dot {
    color: #242021
  }

  .maintabs li a:hover {
    background-position: 7px -96px;
    border-bottom: none;
  }

  .maintabs li a:hover strong,
  .maintabs li a:hover .dot {
    color: #fff;
  }

  li.hg a,
  li.hg.active a:hover {
    background-position: 7px -149px;
  }

  li.hg a:hover {
    /*background-color:#232b31;
    border-color:#111417;*/
    background-color: #5B3768;
    border-color: #472653;
    background-position: 7px -176px;
  }

  li.os a,
  li.os.active a:hover {
    background-position: 7px 9px;
  }

  li.os a .dot,
  li.os.active a:hover .dot {
    color: #5B3768;
  }

  li.os a:hover {
    background-color: #5B3768;
    border-color: #472653;
  }

  li.cs a,
  li.cs.active a:hover {
    background-position: 7px -17px;
  }

  li.cs a .dot,
  li.cs.active a:hover .dot {
    color: #B7355F;
  }

  li.cs a:hover {
    background-color: #B7355F;
    border-color: #99254a;
  }

  li.as a,
  li.as.active a:hover {
    background-position: 7px -43px;
  }

  li.as a .dot,
  li.as.active a:hover .dot {
    color: #00a7e5;
  }

  li.as a:hover {
    background-color: #00a7e5;
    border-color: #0087b9;
  }

  li.ap a,
  li.ap.active a:hover {
    background-position: 7px -69px;
  }

  li.ap a .dot,
  li.ap.active a:hover .dot {
    color: #004F59;
  }

  li.ap a:hover {
    background-color: #004F59;
    border-color: #63951d;
  }

  li.es a,
  li.es.active a:hover {
    background-position: 7px -216px;
  }

  li.es a .dot,
  li.es.active a:hover .dot {
    color: #f8981d;
  }

  li.es a:hover {
    background-color: #f8981d;
    border-color: #d67801;
  }

  li.ac a,
  li.ac.active a:hover {
    background-position: 7px -216px;
  }

  li.ac a .dot,
  li.ac.active a:hover .dot {
    color: #E84F1C;
  }

  li.ac a:hover {
    background-color: #E84F1C;
    border-color: #E84F1C;
  }

  /*** End maintabs   ***/

  .mainnavtop {
    /*margin:0 0 36px 0;*/
    margin: 0 0 20px 0;
  }

  .navbar {
    margin: 0;
  }

  .navbar-inner {
    padding-left: 0;
    padding-right: 0;
    background-color: #f9f9f9;
    background-image: none;
    background-repeat: no-repeat;
    filter: none;
    border: none;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;

    min-height: 30px;

  }

  .hg,
  .os {
    .navbar-inner {
      background-color: #5b3768;
    }
  }

  .cs .navbar-inner {
    background-color: #B7355F;
  }

  .as .navbar-inner {
    background-color: #00a7e5;
  }

  .ap .navbar-inner {
    background-color: #004F59;
  }

  .es .navbar-inner {
    background-color: #f8981d;
  }


  .navbar .nav {
    position: relative;
    left: 0;
    display: block;
    float: left;
    margin: 0 10px 0 0;
  }

  .navbar .nav>li {
    display: block;
    float: left;
  }

  .navbar .divider-vertical {
    width: 1px;
    height: 35px;
    margin: 0;
    overflow: hidden;
    border-left: none;
  }

  .hg,
  .os {
    .navbar .divider-vertical {
      background-color: #462751;
      border-right: 1px solid #714382;
    }
  }

  .navbar .nav>li>a:hover {
    color: #ffffff;
  }

  .hg,
  .os {
    .navbar .nav>li>a:hover {
      text-decoration: none;
      background-color: #714581;
    }
  }

  .cs .navbar .nav>li>a:hover {
    background-color: #cf3f6e;
  }

  .as .navbar .nav>li>a:hover {
    background-color: #30baed;
  }

  .ap .navbar .nav>li>a:hover {
    background-color: #004F59;
  }

  .es .navbar .nav>li>a:hover {
    background-color: #f7ab49;
  }


  .hg,
  .os {

    .navbar .nav .active>a,
    .navbar .nav .active>a:hover {
      color: #ffffff;
      text-decoration: none;
      background-color: #492955;
    }
  }



  .cs .navbar .nav .active>a,
  .cs .navbar .nav .active>a:hover {
    color: #ffffff;
    text-decoration: none;
    background-color: #801f3e;
  }

  .as .navbar .nav .active>a,
  .as .navbar .nav .active>a:hover {
    color: #ffffff;
    text-decoration: none;
    background-color: #0086b8;
  }

  .ap .navbar .nav .active>a,
  .ap .navbar .nav .active>a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #63951d;
  }

  .es .navbar .nav .active>a,
  .es .navbar .nav .active>a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #d67801;
  }

  .ac .navbar .nav .active>a,
  .ac .navbar .nav .active>a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #d67801;
  }

  .navbar .divider-vertical {
    width: 1px;
    height: 35px;
    margin: 0;
    overflow: hidden;

  }

  .hg,
  .os {
    .navbar .divider-vertical {
      background-color: #462751;
      border-right: 1px solid #714382;
    }
  }


  .cs .navbar .divider-vertical {
    background-color: #952549;
    border-right: 1px solid #c73d6a;
  }

  .as .navbar .divider-vertical {
    background-color: #0086b8;
    border-right: 1px solid #30baed;
  }

  .ap .navbar .divider-vertical {
    background-color: #63951d;
    border-right: 1px solid #004F59;
  }

  .es .navbar .divider-vertical {
    background-color: #d67801;
    border-right: 1px solid #f7ab49;
  }

  .navbar .nav>li>a {
    float: none;
    padding: 8px 10px;
    line-height: 19px;
    color: #ececec;
    text-decoration: none;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  }

  .nav>li>a:hover {
    text-decoration: none;
    color: #fff;
  }

  .nav>li>a {
    display: block;
    color: #ccc;
  }

  .navbar-fixed {
    position: fixed;
    top: 32px;
    left: 0;
    right: 0;
    z-index: 1022;
    border-bottom: 1px solid #462751;
    -moz-box-shadow: 0px 0px 20px rgba(0, 0, 0, .5);
    -webkit-box-shadow: 0px 0px 20px rgba(0, 0, 0, .5);
    box-shadow: 0px 0px 20px rgba(0, 0, 0, .5);
  }

  .navbar-fixed .nav-collapse {
    width: 75rem;
    margin: 0px auto;
  }


  /* subnav */
  .subnav {
    width: 100%;
    min-height: 20px;
  }

  .hg,
  .os {
    .subnav {
      background: #492955;
    }
  }

  .hg,
  .os {
    .subnav .divider-vertical {
      background-color: #462751;
      border-right: 1px solid #714382;
    }
  }


  .cs .subnav {
    background: #801F3E;
  }

  .as .subnav {
    background: #0086b8;
  }

  .ap .subnav {
    background: #63951d;
  }

  .es .subnav {
    color: #d67801;
  }

  .subnav .nav-pills>.active>a,
  .subnav .nav-pills>.active>a:hover {
    color: #333;
    background-color: #fff;
    text-shadow: none;
  }

  .subnav .nav-pills>li>a {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-top: 7px;
    margin-bottom: 0;
    margin-left: 5px;
    -webkit-border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    border-radius: 5px 5px 0 0;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  }

  .subnav .nav-pills>li>a:hover {
    background: none;
    color: #fff;
  }

  .subnav-fixed {
    position: fixed;
    top: 35px;
    left: 0;
    right: 0;
    z-index: 1020;
    border-bottom: 1px solid #fff;
    -moz-box-shadow: 0px 0px 5px rgba(0, 0, 0, .7);
    -webkit-box-shadow: 0px 0px 5px rgba(0, 0, 0, .7);
    box-shadow: 0px 0px 5px rgba(0, 0, 0, .7);
  }

  .subnav-fixed .nav {
    margin: 0px auto;
    width: 75rem;
  }

  /* Fourth level navigation: Nav pills*/
  .fourthlevel {
    .nav-pills>li>a {
      text-decoration: none;
      background-color: #eeeeee;
      color: #777;
    }

    .nav-pills>li>a.danger {
      text-decoration: none;
      background-color: #C43C35;
      color: #fff;
    }
  }

  .fourthlevel {

    .nav-pills>li.active>a,
    .nav-pills>li.active>a:hover {
      color: #fff;
    }
  }

  .fourthlevel {

    .nav-pills>li>a:hover,
    .nav-pills>li>a:focus {
      background: #807f83;
      color: #fff;
    }

    .nav-pills>li>a.danger:focus {
      text-decoration: none;
      background-color: #a13b34;
      color: #fff;
    }

    .nav-pills>li>a.danger:hover {
      text-decoration: none;
      background-color: #a13b34;
      color: #fff;
    }
  }

  .hg,
  .os {
    .fourthlevel {

      .nav-pills>li.active>a,
      .nav-pills>li.active>a:hover {
        background: #5B3768;
      }
    }
  }

  .cs .fourthlevel {

    .nav-pills>li.active>a,
    .nav-pills>li.active>a:hover {
      background: #B7355F;
    }
  }

  .as .fourthlevel {

    .nav-pills>li.active>a,
    .nav-pills>li.active>a:hover {
      background: #00a7e5;
    }
  }

  .ap .fourthlevel {

    .nav-pills>li.active>a,
    .nav-pills>li.active>a:hover {
      background: #004F59;
    }
  }

  .es .fourthlevel {

    .nav-pills>li.active>a,
    .nav-pills>li.active>a:hover {
      color: #f7ab49;
    }

    ;
  }

  .hg,
  .os {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: #5B3768;
    }
  }

  .cs h1,
  .cs h2,
  .cs h3,
  .cs h4,
  .cs h5,
  .cs h6 {
    color: #B7355F
  }

  .as h1,
  .as h2,
  .as h3,
  .as h4,
  .as h5,
  .as h6 {
    color: #00a7e5
  }

  .ap {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: #004F59;
    }
  }

  .es {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: #f8981d;
    }
  }


  .title {
    width: 100%;
    margin-bottom: 18px;
    position: relative;
  }

  .hg,
  .os {
    .title {
      border-bottom: 1px solid #5B3768;
    }
  }

  .cs .title {
    border-bottom: 1px solid #B7355F;
  }

  .as .title {
    border-bottom: 1px solid #00a7e5;
  }

  .ap .title {
    border-bottom: 1px solid #004F59;
  }

  .es .title {
    color: #f8981d;
    border-bottom: 1px solid #f8981d;
  }

  .es .title-reverse {
    color: #fff;
    background: #f8981d;
    margin-bottom: 18px;
    position: relative;
    padding: 5px 10px;
    display: block;
  }

  .title h1 {
    width: auto;
  }

  .title span {
    float: right;
    width: auto;
    position: absolute;
    bottom: 5px;
    right: 0;
  }

  .title span#conf-title {
    float: none;
    position: relative;
    bottom: auto;
    right: auto;
  }


  /* Logos footer */
  #logos-footer {
    margin: 40px 0 20px 0;
    padding: 30px 0 0 0;
    border-top: 1px solid #EFEFF0;
  }


  #hotel_logos ul {
    float: left;
    width: 100%;
    list-style: none;
    margin: 0;
    height: 36px;
    overflow: hidden;
  }

  #hotel_logos ul li {
    display: inline;
    margin: 0 10px 0 0;
    height: 36px;
  }

  #event-sponsors ul {
    float: left;
    width: 100%;
    list-style: none;
    margin: 0;
    height: 250px;
    overflow: hidden;
  }

  div.container div.row-fluid div#event-sponsors.span3.offset1 ul li {
    display: inline;
    margin: 0 10px 0 0;
    height: 250px;
  }

  #logos-footer .hbaa-aim {
    margin: 10px 0 0 0;

    img {
      max-width: 100px;
      height: 67px;
      width: auto;
      margin: 0 5px;
    }

    img:last-child {
      max-width: 65px;
    }
  }

  /* Footer */
  footer {
    float: left;
    background: #232B31;
    color: #7F7F7F;
    width: 100% !important;
    font-size: 0.75rem;
  }

  footer ul {
    list-style: none;
    margin: 0;
    padding: 20px;
  }

  footer ul li {
    display: inline;
    margin: 0 15px 0 0;
  }

  footer p {
    float: right;
    padding: 20px;
    margin: 0;
  }

  footer a {
    color: #7F7F7F !important;
    border-bottom: 1px dotted #7F7F7F;
  }

  footer a:hover,
  footer a:focus {
    color: #fff !important;
    text-decoration: none;
    border-bottom: 1px solid #fff;
  }

  #footer_nav {
    float: left;
    width: auto;
    padding: 20px;
  }

  #footer_nav ul {
    float: left;
    padding: 0;
    margin: 0;
    list-style: none;
  }

  #footer_nav li {
    float: left;
    margin: 0 10px 0 0;
  }

  a.edit {
    float: left;
    width: 16px;
    height: 16px;
    background: url(../img/page_edit.png) left top no-repeat;
    text-indent: -9999px;
    margin: 0 5px 0 0;
  }

  a.delete {
    float: left;
    width: 16px;
    height: 16px;
    background: url(../img/delete.png) left top no-repeat;
    text-indent: -9999px;
    margin: 0 5px 0 0;
  }

  input.margeright {
    margin-right: 12px;
  }

  .create_conf {
    float: left;
    width: 500px;
  }

  .orglogo {
    float: right;
    margin-top: 18px;
    padding: 5px;
    border: 1px solid #ddd;
    background: white;
    width: 100%;
  }

  .orglogo img {
    float: left;
    max-width: 100%;
    max-height: 80px;
  }

  .clientlogo {
    padding: 0;
    border: none;
    width: 175px;

    a span.toppart {
      float: left;
      width: 100%;
      margin: 0 0 3px 0;
      /*position:relative;  */
      text-align: center;

      img {
        /* position:absolute;
        bottom:0;  */
        max-height: 60px;
        max-width: 175px;
      }
    }
  }

  .lineof2 {
    float: left;
    width: 100%;
    /*margin-top:18px;*/
  }

  .form-horizontal .lineof2 .control-group {
    width: 50%;
    float: left;
  }

  .well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    border: 1px solid #EFEFF0;
    -webkit-box-shadow: inset 0 0 15px rgba(189, 228, 229, .2);
    -moz-box-shadow: inset 0 0 15px rgba(189, 228, 229, .2);
    box-shadow: inset 0 0 15px rgba(189, 228, 229, .2);
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
  }

  .well .well {
    background: #fff;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }

  .well .nav-tabs li.active a {
    background: none;
    border-bottom-color: #F9F9F9;
  }

  .cs .well .nav-tabs li.active a {
    color: #333;
  }

  .cs .well .nav-tabs li a {
    color: #99254a;
  }

  .well .nav-tabs li a:hover {
    background: #B7355F;
    border: 1px solid #99254a;
    color: #fff;
  }

  .well .nav-tabs li.active a:hover {
    background: none;
    border: 1px solid #DDD;
    border-bottom-color: #F9F9F9;
  }

  .well .panel {
    margin-bottom: 20px;
  }

  .well .panel:last-child {
    margin-bottom: 0;
  }

  .home_login {
    padding: 5px;
    float: left;
  }

  .home_login a {
    color: #fff;
  }

  .home_login a:hover,
  .home_login a:focus {
    color: #fff;
  }

  .home_login form {
    margin: 0;
  }

  .home_login input {
    width: 240px;
    margin-bottom: 5px;
  }

  .home_login input.btn {
    float: right;
    width: auto;
    margin-bottom: 0;
  }

  .cs .home_login {
    background: #B7355F;
  }

  #home-columns {
    margin-bottom: 20px;
  }

  #home-columns .sameHeight .well:last-child {
    margin-bottom: 0;
  }

  #col2,
  #searchmap {
    background: #a5bfdd;
  }

  #col3 {
    background: #09C;
  }

  /* carousel homepage */
  #myCarousel {
    margin-bottom: 40px;
  }

  .span4 #myCarousel {
    margin-bottom: 0;
  }

  #myCarousel .carousel-control {
    font-family: Helvetica, Arial, sans-serif;
    position: absolute;
    top: 50%;
    left: 0;
    width: 29px;
    height: 40px;
    margin-top: -20px;
    font-size: 60px;
    font-weight: 100;
    line-height: 30px;
    color: #ffffff;
    text-align: center;
    background: #000;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    -webkit-border-top-right-radius: 8px;
    -webkit-border-bottom-right-radius: 8px;
    -moz-border-radius-topright: 8px;
    -moz-border-radius-bottomright: 8px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    opacity: 0.4;
    filter: alpha(opacity=40);
    border: 0;
  }

  #myCarousel .carousel-control.right {
    right: 0;
    left: auto;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    -webkit-border-top-left-radius: 8px;
    -webkit-border-bottom-left-radius: 8px;
    -moz-border-radius-topleft: 8px;
    -moz-border-radius-bottomleft: 8px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  #myCarousel .carousel-control:hover {
    color: #ffffff;
    text-decoration: none;
    opacity: 0.60;
    filter: alpha(opacity=60);
    -webkit-transition: opacity 200ms;
    -moz-transition: opacity 200ms;
    -o-transition: opacity 200ms;
    transition: opacity 200ms;
  }

  #myCarousel .carousel-caption {
    background: rgba(0, 0, 0, .7);
    color: #fff;
    padding-bottom: 15px;
  }

  #myCarousel .carousel-caption h4 {
    font-size: 18px;
    line-height: 24px;
    color: #fff;
  }



  .venue_advert {
    position: relative;
  }

  .venue_advert span {
    /* position: absolute;*/
    float: left;
    left: 0;
    top: 0;
    right: 0;
    /*background: rgba(0, 0, 0, .7);*/
    padding: 10px;
    font-size: 16px;

    /* transition: background .2s;
    -moz-transition: background .2s;
    -webkit-transition: background .2s;
    -o-transition: background .2s; */
  }

  .venue_advert a,
  .venue_advert a:hover,
  .venue_advert a:focus {
    color: #fff;
  }

  /*.cs .venue_advert a:hover span, .cs .venue_advert a:focus span {
    background: rgba(183, 53, 95, .9);
  }

  .as .venue_advert a:hover span, .cs .venue_advert a:focus span {
    background: rgba(0, 167, 229, .9);
  }    */

  #latest_news_holder ul,
  #latest_offers_holder ul {
    padding: 0;
    margin: 10px 0 0 0;
    list-style: none;
  }

  #latest_offers_holder h3 {
    font-size: 14px;
    line-height: 24px;
    margin: 0;
  }

  #latest_news_holder ul li {
    padding: 5px 0 5px 0;
    margin: 5px 0 5px 0;
    border-bottom: 1px solid #EFEFF0;
  }

  #latest_news_holder ul li:last-child {
    border-bottom: none;
  }

  #latest_offers_holder .jcarousel-clip {
    overflow: hidden;
    width: 260px;
    height: 155px;
  }

  #offers_carousel {
    width: 260px;
    height: 155px;
    overflow: hidden;
    position: relative;
  }

  #hotel_logos_holder {
    overflow: hidden;
    width: 500px;
    float: left;
  }

  #hotel_logos_holder h3 {
    float: left;
    margin-bottom: 10px;
    width: 100%;
  }

  #logo_carousel {
    height: 36px;
    width: 500px;
    position: relative;
    overflow: hidden;
  }

  #logo_carousel li {
    display: inline !important;
    margin: 0 10px 0 0;
    background: #F69;
  }

  #logo_carousel img {
    height: 36px;
    background: #639;
  }

  .hg-logo-foot {
    float: right;
    width: auto;
  }

  /* Latest offers */
  #latest_offers_holder,
  #latest_offers_holder ul,
  #latest_offers_holder ul li,
  #latest_offers_holder ul li .article {
    float: left;
    width: 100%;
  }

  #latest_offers_holder .articlelist h2 {
    font-size: 20px;
    margin: 0;
    padding: 0;
    line-height: 28px;
  }

  #latest_offers_holder ul {
    margin: 0;
  }

  #latest_offers_holder li h3 {
    font-size: 14px;
    line-height: 24px;
    color: #fff;
  }

  #latest_offers_holder li p {
    margin: 0;
  }

  #latest_offers_holder ul li a {
    float: left;
    width: 100%;
    margin: 0 0 3px 0;
    background: #EFEFF0;
    padding: 5px;
    color: #232B31;
    box-sizing: border-box;
    -webkit-transition: all 200ms ease;
    -moz-transition: all 200ms ease;
    -o-transition: all 200ms ease;
    -ms-transition: all 200ms ease;
    transition: all 200ms ease;

  }

  #latest_offers_holder ul li a img {
    float: left;
    margin: 0 5px 0 0;
    border: 1px solid #fff;
    width: 60px;
    height: auto;
  }

  #latest_offers_holder ul li a h3 {
    color: #232B31;
  }

  #latest_offers_holder ul li a:hover {
    background: #666e74;
    color: #EFEFF0;
    text-decoration: none;
  }

  #latest_offers_holder ul li a:hover h3,
  #latest_offers_holder ul li a:hover h2 {
    color: #EFEFF0;
  }

  /* logged in landing */
  .landing {
    padding: 0 0 40px 0;
    border-bottom: 1px solid #EFEFF0;
    margin: 30px 0 30px 0;
  }

  .landing .span4 {
    position: relative;
  }

  .landing .span4 a {
    float: left;
    height: auto;
    width: 260px;
    padding: 20px;
  }

  .landing .span4 a.cs {
    background: #b0315a;
  }

  .landing .span4 a.cs:hover {
    background-color: #99254a;
  }

  .landing .span4 a.as {
    background: #00a3e3;
  }

  .landing .span4 a.as:hover {
    background-color: #0087b9;
  }

  .landing .span4 a.ap {
    background: #86c20a;
  }

  .landing .span4 a.ap:hover {
    background-color: #63951d;
  }

  .landing .span4 img {
    max-height: 80px;
  }

  .landing .span4 .noaccess {
    position: absolute;
    top: 0;
    left: 0;
    width: 260px;
    height: 80px;
    padding: 20px;
    background: rgba(0, 0, 0, .75);
    color: #fff;
  }

  .landing .span4 .noaccess:hover {
    background: rgba(0, 0, 0, .85);
    text-decoration: none;
  }

  .header .span4 img,
  .header .span3 img {
    max-height: 90px;
  }

  .header .span3.testilink a {
    color: #fff;
    text-align: center;
    display: block;
    margin-top: 10px;
    text-decoration: none;
    font-size: 18px;
    line-height: 24px;
  }



  /* tables */

  .table {
    font-size: 13px;
    line-height: 18px;

    td,
    th {
      padding: 6px;
    }

    th a {
      color: #fff;
      border-bottom: 1px dotted #ccc;
    }

    th a:hover {
      color: #fff;
      border-bottom: none;
      text-decoration: none;
    }
  }

  .table-small {
    font-size: 12px;
    line-height: 16px;

    td,
    th {
      padding: 5px;
    }

  }

  table#admin-fees div.controls {
    margin-left: 0;
  }

  div.admin_fees_app_chrg_note div.controls {
    margin-left: 0;
  }

  div.admin_fees_app_chrg_note textarea {
    width: 96%;
    height: 185px;
  }

  div#app-fee-output {
    overflow-y: scroll;
    height: 185px;
  }

  body.admin_interface div.span6.room_type_packages label.checkbox {
    min-width: auto;
  }

  .hg,
  .os {
    .table th {
      background: #5b3768;
      color: #fff;
    }
  }




  .cs {
    .table-bordered th {
      background: #B7355F !important;
      border: 1px solid #952549 !important;
      color: #fff;
      border-right: none !important;
    }

    .table-bordered th:last-child {
      border-right: 1px solid #952549 !important;
    }
  }

  .as {
    .table th {
      background: #00a7e5;
      color: #fff;
    }
  }

  .ap {
    .table th {
      background: #004F59;
      color: #fff;
    }
  }

  .hg,
  .os,
  .as,
  .ap,
  .cs {
    .table-white {

      td,
      th {
        background: #fff;
      }

      th {
        color: #333;
      }
    }
  }

  .table-striped tbody tr.red_row td {
    background-color: #f4e6e9 !important;
    color: #333;
  }

  .table tbody tr.red_row td {
    background-color: #f4e6e9 !important;
    color: #333;
  }

  .table-striped tbody tr.green_row td {
    background-color: #D0F6A4 !important;

  }

  .table tbody tr.green_row td {
    background-color: #D0F6A4 !important;

  }

  .table thead.quotation th {
    background-color: #EEE;
    color: #777;
  }

  .table-narrow-hdr th {
    width: 200px;

  }

  .table-narrow-hdr td {
    font-size: 14px;

  }

  .table-narrow-hdr {
    width: 500px;
  }

  .table-nhdr-full th {
    width: 200px;
  }

  .table-nhdr-full td {
    font-size: 14px;
  }

  .table-nhdr-full {
    width: 100%;
  }

  .well h4 {
    color: #333;
    padding: 5px;
    background: #e0e0e0;
  }

  .well table:last-child {
    margin: 0;
  }

  /* key principles */
  #image-wrap {
    width: 400px;
    float: left;
  }

  #copy-wrap {
    width: 220px;
    float: left;
  }

  #copy-wrap h1 {
    font-size: 1em;
    padding: 0;
  }

  .image {
    width: 122px;
    height: 122px;
    float: left;
  }

  .hidden {
    display: none;
  }


  /* forms */

  body.admin_interface.hg.index.admin-admin_fee_lines.modal-open textarea#admin_fee_line_note {
    width: 95% !important;
  }

  form#fees-form input {
    width: 90%;
  }

  .span6 .well label {
    float: left;
    width: 140px;
    /*text-align:right;*/
    margin: 5px 15px 0 0;

  }

  .span6 .well.longlabels label {
    float: left;
    width: 300px;
    /*text-align:right;*/
    margin: 5px 15px 0 0;

  }

  .span6 .well .control-group.longlabels label {
    float: left;
    width: 200px;
    /*text-align:right;*/
    margin: 5px 15px 0 100px;
    clear: both;
  }

  .span6 .well.chkbox-long-label label {
    width: 175px;
  }

  .span6 .well.chkbox-long-label label.checkbox input {
    float: left;
  }

  .well span.label {
    width: 120px;
    /*text-align:right;*/
    margin: 5px 15px 0 0;
    background-color: #FFF;
    color: #000;
    text-decoration: none;
    font-weight: normal;
    text-shadow: none;
    white-space: normal;
  }

  .conferences .well span.label {
    margin: 0;
  }

  .well.longlabels span.label {
    width: 160px;
  }

  body.client-conferences .span6 .well .well {
    width: 380px;
  }

  .row-fluid.conferences .span4 div.well .alert {
    width: 200px;
  }

  body.supplier-conference_dates .row-fluid.conferences .span4 div.well .alert {
    width: 209px;
  }

  .span6 .well {
    width: 420px;

    input[type="button"].pull-right {
      border: 5px solid red;
      float: right;
      display: inline-block;
    }

    .aligninputs {
      float: right;
    }

    .table label {
      width: auto;
    }

    label.forcheckbox {
      width: 400px;
      float: left;
      margin: 0 0 10px 0;
    }

    input[type="checkbox"] {
      float: right;
    }
  }

  #user_loyalty_scheme {
    margin-top: -2px;
  }

  #tacs label {
    width: auto;
    float: left;
  }

  #tacs .controls {
    float: left;
    width: 130px;
    margin: 5px 10px 0 10px;
  }

  #tacs input.input-block-level {
    min-height: 0;
  }

  .user_terms_and_conditions {
    padding: 5px 10px;
    margin: 0 0 2px 0;
    background: #e5e5e5;
    color: #666;
  }

  .span8 {
    .aligninputs {
      float: right;
    }
  }

  #tacs .user_terms_and_conditions.error {
    background: #b94a48;
    margin: 0 0 15px 0;

    .control-label,
    .help-inline {
      color: #fff;
    }

    a {
      color: #fff;
      text-decoration: underline;
    }

    #user_terms_and_conditions {
      margin: 3px 0 0 0;
    }

    .controls .checkbox {
      padding: 0;
    }
  }

  /* Registration 5 steps */
  ul.steps {
    font-size: 15px;
    margin-left: 0;
    list-style-type: none;

    li {
      margin: 0 0 5px 0;
    }

    .well {
      padding: 16px;
    }
  }

  ul.benefits {
    margin: 0;

    li {
      list-style-type: none;
      border-left: 3px solid #B7355F;
      margin: 0 0 20px 0;
      padding: 5px 5px 5px 10px;
      background: #f5f5f5;
      color: #666;
    }
  }

  #error-top {
    margin: 10px;
    color: #999;
    font-size: 14px;
    font-weight: bold;
  }

  .error-top {
    margin: 10px;
    color: red;
  }




  /* Offers */
  #offers img {

    padding: 4px;
    border: 1px solid #ccc
  }

  #offers {
    float: left;
    margin-right: 20px;
  }



  /* BUTTONS */

  .cs .btn-primary {
    background-color: #B7355F;
    background-image: -moz-linear-gradient(top, #B7355F, #952549);
    background-image: -ms-linear-gradient(top, #B7355F, #952549);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#B7355F), to(#952549));
    background-image: -webkit-linear-gradient(top, #B7355F, #952549);
    background-image: -o-linear-gradient(top, #B7355F, #952549);
    background-image: linear-gradient(top, #B7355F, #952549);
    background-repeat: repeat-x;
    border-color: #B7355F #B7355F #952549;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#B7355F', endColorstr='#952549', GradientType=0);
    filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  }

  .cs .btn-primary:hover,
  .cs .btn-primary:active,
  .cs .btn-primary.active,
  .cs .btn-primary.disabled,
  .cs .btn-primary[disabled] {
    background-color: #952549;
    *background-color: #952549;
  }

  .hg,
  .os {
    .btn-primary {
      background-color: #714382;
      background-image: -moz-linear-gradient(top, #714382, #462751);
      background-image: -ms-linear-gradient(top, #714382, #462751);
      background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#714382), to(#462751));
      background-image: -webkit-linear-gradient(top, #714382, #462751);
      background-image: -o-linear-gradient(top, #714382, #462751);
      background-image: linear-gradient(top, #714382, #462751);
      background-repeat: repeat-x;
      border-color: #714382 #714382 #462751;
      border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
      filter: progid:dximagetransform.microsoft.gradient(startColorstr='#714382', endColorstr='#462751', GradientType=0);
      filter: progid:dximagetransform.microsoft.gradient(enabled=false);
    }
  }

  .hg,
  .os {

    .btn-primary:hover,
    .btn-primary:active,
    .btn-primary.active,
    .btn-primary.disabled,
    .btn-primary[disabled] {
      background-color: #462751;
      *background-color: #462751;
      text-decoration: none;
    }
  }

  .as {
    .btn-primary {
      background-color: #30baed;
      background-image: -moz-linear-gradient(top, #30baed, #0086b8);
      background-image: -ms-linear-gradient(top, #30baed, #0086b8);
      background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#30baed), to(#0086b8));
      background-image: -webkit-linear-gradient(top, #30baed, #0086b8);
      background-image: -o-linear-gradient(top, #30baed, #0086b8);
      background-image: linear-gradient(top, #30baed, #0086b8);
      background-repeat: repeat-x;
      border-color: #30baed #30baed #0086b8;
      border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
      filter: progid:dximagetransform.microsoft.gradient(startColorstr='#30baed', endColorstr='#0086b8', GradientType=0);
      filter: progid:dximagetransform.microsoft.gradient(enabled=false);
    }
  }

  .as {

    .btn-primary:hover,
    .btn-primary:active,
    .btn-primary.active,
    .btn-primary.disabled,
    .btn-primary[disabled] {
      background-color: #0086b8;
      *background-color: #0086b8;
      text-decoration: none;
    }
  }

  .ap {
    .btn-primary {
      background-color: #004F59;
      background-image: -moz-linear-gradient(top, #004F59, #004F59);
      background-image: -ms-linear-gradient(top, #004F59, #004F59);
      background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#004F59), to(#004F59));
      background-image: -webkit-linear-gradient(top, #004F59, #004F59);
      background-image: -o-linear-gradient(top, #004F59, #004F59);
      background-image: linear-gradient(top, #004F59, #004F59);
      background-repeat: repeat-x;
      border-color: #004F59 #004F59 #004F59;
      border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
      filter: progid:dximagetransform.microsoft.gradient(startColorstr='#004F59', endColorstr='#004F59', GradientType=0);
      filter: progid:dximagetransform.microsoft.gradient(enabled=false);
    }
  }

  .ap {

    .btn-primary:hover,
    .btn-primary:active,
    .btn-primary.active,
    .btn-primary.disabled,
    .btn-primary[disabled] {
      background-color: #004F59;
      *background-color: #004F59;
      text-decoration: none;
    }
  }

  .es {
    .btn-primary {
      background-color: #f8981d;
      background-image: -moz-linear-gradient(top, #f8981d, #d67801);
      background-image: -ms-linear-gradient(top, #f8981d, #d67801);
      background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f8981d), to(#d67801));
      background-image: -webkit-linear-gradient(top, #f8981d, #d67801);
      background-image: -o-linear-gradient(top, #f8981d, #d67801);
      background-image: linear-gradient(top, #f8981d, #d67801);
      background-repeat: repeat-x;
      border-color: #f8981d #f8981d #d67801;
      border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
      filter: progid:dximagetransform.microsoft.gradient(startColorstr='#f8981d', endColorstr='#d67801', GradientType=0);
      filter: progid:dximagetransform.microsoft.gradient(enabled=false);
    }
  }

  .es {

    .btn-primary:hover,
    .btn-primary:active,
    .btn-primary.active,
    .btn-primary.disabled,
    .btn-primary[disabled] {
      background-color: #d67801;
      *background-color: #d67801;
      text-decoration: none;
    }
  }

  .cs,
  .hg,
  .os,
  .as,
  .ap,
  .es {
    .btn-primary {
      color: #fff !important;
    }
  }

  .btn-slim {
    line-height: 18px;
    padding: 0 10px;
  }

  .poptrigger {
    text-decoration: none;
    font-weight: bold;
  }

  div.popover div.popover-content table.table {
    font-size: 70%;
  }

  div.popover {
    max-width: 400px;
  }

  h5.pra-popover {
    height: 50px;
  }

  /* Abbr */
  .cs abbr {
    color: #B7355F;
  }

  .hg,
  .os {
    abbr {
      color: #714382;
    }
  }

  .ap abbr {
    color: #004F59;
  }

  .as abbr {
    color: #30baed;
  }

  /* Testimonials */
  #testimonials .fakearticle {
    float: left;
    margin: 0 0 20px 0;
    background: #f9f9f9;
    border: 1px solid #EFEFF0;
    padding: 20px;
    color: #232B31;
    box-sizing: border-box;
    -webkit-transition: all 200ms ease;
    -moz-transition: all 200ms ease;
    -o-transition: all 200ms ease;
    -ms-transition: all 200ms ease;
    transition: all 200ms ease;

    font-size: 14px;
    line-height: 20px;
    font-style: italic;
  }



  .alignleft {
    margin: 0 0 0 155px;
  }




  /* proposal */
  div#wrapper {
    width: 980px;
    margin: 0px auto;
    padding: 30px 0;
    background: #fff;
    border-left: 10px solid #fff;
    border-right: 10px solid #fff;
    -moz-box-shadow: 0 0 0 1px white, 0px 0px 20px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0 0 0 1px white, 0px 0px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 0 0 1px white, 0px 0px 20px rgba(0, 0, 0, 0.05);
  }

  /*loyalty*/
  #lpr-fields {
    width: 170px;
    clear: none;
  }

  #lpr-fields input {
    width: 120px;
  }

  div.loyalty_panel {
    height: 103px;
    min-height: 103px;
    margin-bottom: 2px;
    background: #f5f5f5;
    padding: 5px 0 5px 10px;
  }

  div.loyalty_panel a {
    text-decoration: none;
  }

  div.loyalty_panel a:hover {
    text-decoration: underline;
  }

  .loyalty_panel_extended {
    height: auto;
    min-height: 103px;
    margin-bottom: 2px;
  }

  div.loyalty_panel h3 {
    padding: 0;
    margin: 0;
  }

  div.loyalty_panel img {
    padding: 0;
    margin: -4px -4px 1px 1px;
    float: right;
  }

  div.loyalty_panel p {
    padding: 0;
    margin: 0;
    line-height: 20px;
  }

  div.loyalty_blank {
    height: 15px;
    min-height: 15px;
    background-color: #E7E8E9;
  }


  /* New meet_the_team page */
  /*** About us ***/

  .abouttable {
    float: left;
    width: 100%;
    margin: 0 0 20px 0;
    padding: 0;
    list-style: none;
    border-top: 5px solid #f5f5f5;
    border-left: 5px solid #f5f5f5;
    background: #f5f5f5;
  }

  .abouttable li {
    background: #f7f8f9;
    padding: 0;
    margin: 0;
    float: left;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border-bottom: 5px solid #f5f5f5;
    border-right: 5px solid #f5f5f5;
  }


  .abouttable li img {
    width: 100%;
    height: auto;
  }

  #allstaff li {
    width: 33%;
  }

  #allstaff li a {
    float: left;
    display: block;
    width: 100%;
    height: 100%;
  }

  #allstaff img.main {
    display: block;
  }

  #allstaff img.over {
    display: none;
  }

  #allstaff li#last {
    background: url("grainy-bg.jpg") left top repeat;
    color: #fff;
    border-bottom: none;
  }

  #allstaff li#last div {
    display: block;
    margin: 10px;
    font-size: 13px;
    line-height: 18px;
  }

  #allstaff li#last div a {
    float: none;
    display: inline;
  }

  #panels ul {
    float: left;
    width: 100%;
    display: block;
  }

  #panels li {
    width: 50%;
  }


  #panels li img {
    width: 100%;
    height: auto;
  }

  #main-content .inner div.quote {
    float: left;
    width: 100%;
    display: block !important;
  }

  #main-content .inner div.quote p {
    font-family: Georgia, "Times New Roman", Times, serif;
    font-style: italic;
  }

  #panels {
    position: relative;
  }

  .panel {
    position: relative !important;
  }

  .panel .quote {
    position: relative !important;
  }

  #panels section.title {
    float: left;
    width: 100%;
    padding-top: 0;
  }

  #main-content .inner p.who {
    float: left;
    width: auto;
  }

  .allstaff {
    float: right;
    width: auto;
    display: none;
  }

  #panels .panel p strong {
    color: #5B3768;
  }

  .panel h3 {
    margin: 0;
  }










  /* Image upload iframes */

  #new-image-iframe {
    background: #fff;
    border: 1px solid #cccccc;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    padding: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 30em;
    overflow: auto;
  }

  ifram#new-image-iframe {

    html,
    body {
      background-color: #fff !important;
    }
  }


  /* pagination */
  .pagination a {
    padding: 2px 6px;
    background: #fff;
    border: 1px solid #eee;
  }

  .pagination {

    .current,
    .disabled {
      padding: 2px 6px;
      background: #fff;
    }
  }

  .pagination a:hover {
    background: #807f83;
    color: #fff;
    text-decoration: none;
    border: 1px solid #555;
  }


  /* Jump To */
  .jumpto {
    /*background: #232B31; */
    color: #fff;
    font-size: 12px;
    /* margin: 3px 3px 0 0;  */
    padding: 7px 5px 5px 5px;

    form {
      margin: 0;
    }

    label {
      font-size: 12px;
      float: left;
      margin: 2px 5px 0 0;
    }

    input {
      margin: 0;

    }

    input[type="text"] {
      font-size: 12px;
      padding: 0 2px;
      width: 40px;
      height: 20px;
    }

    input.btn {
      height: 22px;
      line-height: 18px;
      margin: 0;
    }
  }


  /* Errors */
  .errorExplanation {
    color: #b94a48;
    background-color: #f2dede;
    border-color: #eed3d7;
    padding: 8px 35px 8px 14px;
    margin-bottom: 20px;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;

    h2 {
      color: #b94a48 !important;
    }
  }

  /* benefits pins */
  .container .row-fluid .benefits ul {
    list-style-type: none;
    margin: 0 0 10px 10px;

    li {
      background: left 2px no-repeat;
      padding-left: 15px;
    }
  }

  .os,
  .hg {
    .container .row-fluid .benefits ul li {
      background-image: url("imgphase3/pin-mini-hg.png")
    }
  }

  .cs .container .row-fluid .benefits ul li {
    background-image: url("imgphase3/pin-mini-cs.png")
  }

  .as .container .row-fluid .benefits ul li {
    background-image: url("imgphase3/pin-mini-as.png")
  }

  .ap .container .row-fluid .benefits ul li {
    background-image: url("imgphase3/pin-mini-ap.png")
  }

  .es .container .row-fluid .benefits ul li {
    background-image: url("imgphase3/pin-mini-es.png")
  }

  .benefits .logo {
    height: 55px;
  }



  #new_conference {


    .well {
      padding: 9px;
      margin-bottom: 10px;

      h3 {
        margin-bottom: 10px;
      }
    }

    table.table .control-group .controls {
      margin-left: 0;
    }

    div.control-group {
      padding: 0px;
      margin-bottom: 5px;

      .control-label,
      span.label {
        font-size: 12px;
        width: 115px;
        padding: 3px 0 0 0;

      }

      .controls {
        margin-left: 120px;
      }

      .data_value {
        padding: 5px 0 0 0;
      }
    }

    .listcheckbox label {
      font-size: 12px;
      width: 250px;
    }

    .qty {
      div {
        padding: 0
      }

      .control-group {
        .control-label {
          width: 200px;
        }

        .controls {
          margin-left: 210px;
        }
      }
    }

    #conference_opportunity_attributes_main_contact_name {
      width: 255px;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px;
    }

    .fields {
      margin-bottom: 10px;
    }

    .fields .controls {
      margin-left: 120px;
      margin-bottom: 5px;
    }

    .fields .input-small {
      width: 60px;
    }
  }


  .span8 div.postcodebutton {
    margin: 5px 5px 5px 25px;
  }



  .well {

    .table-striped tbody>tr:nth-child(odd)>td,
    .table-striped tbody>tr:nth-child(odd)>th {
      background-color: #fff;
    }
  }


  .cs.new.supplier-chains .form-horizontal .span6 .well .controls {
    margin-left: 155px;
  }

  #advanced_search {
    margin-bottom: 10px;

  }


  input#confirm {
    float: none;
    margin: -2px 0 0 4px;
  }




  /* Company values */

  .companyvalues {
    h4 {
      font-size: 17px;
      margin-bottom: 20px;
    }

    div {
      list-style: none;
      font-size: 14px;
      margin-bottom: 25px;
      padding: 5px 35px;

      strong {
        font-size: 18px;
        background-image: url(/assets/imgphase3/pins.png);
        background-repeat: no-repeat;
        padding-left: 20px;
        line-height: 20px;
      }
    }

    div.hg {
      strong {
        color: purple
      }
    }

    div.cs {
      strong {
        color: #B7355F;
        background-position: 0 -25px;
      }
    }

    div.as {
      strong {
        color: #00a7e5;
        background-position: 0 -50px;
      }
    }

    div.ap {
      strong {
        color: #004F59;
        background-position: 0 -75px;
      }
    }
  }

  table.table.table-compare {
    th {
      white-space: nowrap;
    }

    th,
    td {
      text-align: center;
      vertical-align: middle;
    }

    th:first-child,
    td:first-child {
      text-align: left;
    }

    td span.pinktick {
      background-image: url(/assets/imgphase3/pins.png);
      background-repeat: no-repeat;
      background-position: 0 -22px;
      height: 20px;
      width: 10px;
      display: inline-block;
    }

    tr td:nth-child(2) {
      background: #f9f9f9;
    }

    tr td:nth-child(3) {
      background: #f5f5f5;
    }

    tr td:nth-child(4) {
      background: #f0f0f0;
    }
  }




  .row-fluid.conferences div .well div.contract-invoice-new {
    padding: 10px 10px 0 10px;
    width: 880px;
  }



  .cs.supplier-quotations .tick_boxes_long_labels {
    margin-bottom: 20px;

    .control-group {
      display: block;
      position: relative;
    }

    label.control-label {
      margin-left: 20px;
      text-align: left;
      width: 400px !important;
    }

    .controls {
      left: -330px !important;
      top: 5px !important;
      position: absolute !important;
    }

    /* Added Value */
    .added_value {
      display: inline-block;
      width: 100%;
      margin-bottom: 5px;

      label {
        width: 400px;
        margin: 0;
      }

      input {
        position: absolute;
        margin: 3px 0 0 -20px;
        *margin: -3px 0 0 -20px;

      }
    }
  }

  .modal .modal-body label.longlabel {
    width: 300px;
  }

  div#quote-key-docs form {
    margin-left: 10px;
    margin-top: -20px;
  }

  div#quote-key-docs form label,
  div#quote-key-docs form input {
    font-size: 10px;

  }


  table.table tr td.align-right,
  table.table tr th.align-right {
    text-align: right !important;

    span.red-dash-link,
    a.red-dash-link {
      color: #FC2F2F !important;
    }
  }

  html body.cs div#bg div.container div.row-fluid div.span6 div.well table.table tbody tr th {
    color: white;
  }



  .alert.alert-error {

    h1,
    h2,
    h3,
    h4,
    h5,
    h5,
    a,
    ul,
    ol,
    li,
    p {
      color: #b94a48 !important;
    }
  }


  body.client-programmes .well .well {
    width: auto;
  }

  body.supplier-quotations .well .well {
    width: auto;
  }

  body.supplier-quotations span.radio.inline {
    width: auto;
    margin: 0;

    label {
      margin: 0;
      line-height: auto;
      width: auto;

      input.input-block-level {
        height: auto;
      }
    }
  }

  section#accom_options {
    .control-group .controls {
      margin: 0px;
      width: auto;
    }
  }

  body.client-programmes .well .well {
    width: auto;
  }

  body.client-conferences {
    table {

      .info,
      .financials {
        white-space: nowrap;

        span.label {
          color: #000;
          background: none;
          padding: 0;
          display: inline-block;
          text-shadow: none;
        }
      }

      .info {
        white-space: normal;

        span.label {
          width: 70px;
        }
      }

      .financials {
        span.label {
          width: 150px;
        }
      }

      .alert {
        margin-bottom: 0px;
      }
    }
  }

  #client_sla_modal {
    width: 1000px;
  }

  #roomrates div.quotation_rfq_response_rooms_rack_rate,
  #roomrates div.quotation_rfq_response_rooms_price_supplied {
    border-top-width: 0px !important;

    div.controls {
      margin-left: 0px !important;

      /* display:block !important;
      width: 70px !important;
      padding: 0px !important;    */
      input {
        /*  width:70px !important;
        margin:0px !important; */
        /*padding: 0px !important;    */
        width: 60px;
      }

    }

  }

  #roomrates {
    width: 420px !important;

    input.rackrate {
      width: 60px !important;
    }
  }

  .admin_interface.admin-quotations.edit #roomrates {
    width: 567px !important;
  }




  div#acc-modal.modal form.simple_form.form-horizontal.confirmation-q div.modal-body.row-fluid div.control-group div.controls input[type=text],
  textarea {
    width: 380px;
  }

  div#adu-det-modal.modal form.simple_form.form-horizontal div.modal-body.row-fluid div.control-group div.controls input[type=text],
  textarea {
    width: 380px;
  }

  div#adu-det-modal.modal {
    width: 700px;

  }

  div.showdata div.control-group,
  div.showdata div.controls {
    border-top-width: 0px !important;

    input[type="checkbox"] {
      margin-top: 0px;
    }
  }



  .person-selector {
    margin-top: -20px;
  }

  body.supplier-quotations div.quote_acomm th {
    color: white;
  }

  div.add-person-client.well {
    width: 380px;
  }




  /* New inner straplines */
  .straplines {
    font-size: 20px;
    font-family: "MuseoSlab500", 'Rokkitt', "Trebuchet MS";
    padding: 0;
    margin-bottom: 10px;
    background: #fff;
  }

  .straplines h4 {
    font-size: 30px;
    color: #fff;
    line-height: 30px;
  }

  .straplines ul {
    list-style: none;
    padding: 0 0 0 20px;
    margin: 0;
  }

  .cs .straplines {
    color: #B7355F;

    h4 {
      color: #B7355F;
    }

    ul {
      border-left: 1px solid #B7355F;
    }
  }

  .as .straplines {
    color: #00a7e5;
    margin: 20px 0;

    h4 {
      color: #00a7e5;
    }

    ul {
      border-left: 1px solid #00a7e5;
    }
  }

  .ap .straplines {
    color: #004F59;
    margin: 20px 0;

    h4 {
      color: #004F59;
    }

    ul {
      border-left: 1px solid #004F59;
    }
  }


  .es .straplines {
    color: #f8981d;
    margin: 20px 0;

    h4 {
      color: #f8981d;
    }

    ul {
      border-left: 1px solid #f8981d;
    }
  }

  .home-buttons-bottom {
    margin-top: 20px;
    float: left;

    .span4 {
      text-align: center;
    }

    .span4 a {
      color: #fff;
      text-align: center;
      height: 115px;
      width: 80px;
      margin: 0 8px;
      background: url(/assets/imgphase3/homes-buttons.gif) no-repeat;
      display: block;
    }

  }

  .cs .home-buttons-bottom {
    .span4 a.email {
      background-position: left top;
    }

    .span4 a.webinar {
      background-position: -101px 0;
    }

    .span4 a.package {
      background-position: -202px 0;
    }

    .span4 a.email:hover {
      background-position: -303px top;
    }

    .span4 a.webinar:hover {
      background-position: -404px 0;
    }

    .span4 a.package:hover {
      background-position: -505px 0;
    }
  }

  .es .home-buttons-bottom {
    .span4 a.email {
      background-position: left -155px;
    }

    .span4 a.webinar {
      background-position: -101px -155px;
    }

    .span4 a.package {
      background-position: -202px -155px;
    }

    .span4 a.email:hover {
      background-position: -303px -155px;
    }

    .span4 a.webinar:hover {
      background-position: -404px -155px;
    }

    .span4 a.package:hover {
      background-position: -505px -155px;
    }
  }

  .as .home-buttons-bottom {
    .span4 a.email {
      background-position: left -310px;
    }

    .span4 a.webinar {
      background-position: -101px -310px;
    }

    .span4 a.package {
      background-position: -202px -310px;
    }

    .span4 a.email:hover {
      background-position: -303px -310px;
    }

    .span4 a.webinar:hover {
      background-position: -404px -310px;
    }

    .span4 a.package:hover {
      background-position: -505px -310px;
    }
  }

  .ap .home-buttons-bottom {
    .span4 a.email {
      background-position: left -467px;
    }

    .span4 a.webinar {
      background-position: -101px -467px;
    }

    .span4 a.package {
      background-position: -202px -467px;
    }

    .span4 a.email:hover {
      background-position: -303px -467px;
    }

    .span4 a.webinar:hover {
      background-position: -404px -467px;
    }

    .span4 a.package:hover {
      background-position: -505px -467px;
    }
  }


  .goTest {
    padding: 45px 20px;
    text-align: center;
    font-size: 32px;
    font-family: "MuseoSlab500", 'Rokkitt', "Trebuchet MS";
    /*background: url(/assets/imgphase3/goTest.jpg);    */
    width: 260px;
    /*height:98px;    */
    line-height: 30px;
    font-style: italic;
  }

  .goTest span {
    font-size: 80px;
    color: #ad1a59;
    vertical-align: bottom;
    line-height: 0;
  }

  .goTest span:first-child {
    padding: 0 15px 0 0;
  }

  .goTest span:last-child {
    padding: 0;
  }


  .hg.contact_us.cms-hospitality_guaranteed {
    .container .row-fluid ul {
      list-style: none;
      margin: 0 0 15px 0;
    }
  }




  .cs {
    .benefits-intros {
      h2 {
        margin-bottom: 0px;
      }

      p {
        color: #B7355F;
      }
    }

    .benefits-bullets {
      ul {
        list-style: none;
        margin-left: 0;
      }

      ul li {
        margin-bottom: 15px;
        padding-bottom: 5px;
        padding-left: 20px;
        background: url(/assets/imgphase3/cs-bullet-pin.gif) left top no-repeat;
      }
    }
  }

  .es {
    .benefits-intros {
      h2 {
        margin-bottom: 0px;
      }

      p {
        color: #f8981d;
      }
    }

    .benefits-bullets {
      ul {
        list-style: none;
        margin-left: 0;
      }

      ul li {
        margin-bottom: 15px;
        padding-bottom: 5px;
        padding-left: 20px;
        background: url(/assets/imgphase3/es-bullet-pin.gif) left top no-repeat;
      }
    }
  }

  .as {
    .benefits-intros {
      h2 {
        margin-bottom: 0px;
      }

      p {
        color: #00a7e5;
      }
    }

    .benefits-bullets {
      ul {
        list-style: none;
        margin-left: 0;
      }

      ul li {
        margin-bottom: 15px;
        padding-bottom: 5px;
        padding-left: 20px;
        // background: url(/assets/imgphase3/BlueServaceArrow.gif) left top no-repeat;
        // leaving commented out in case they want to use the image again
      }
    }
  }

  .ap {
    .benefits-intros {
      h2 {
        margin-bottom: 0px;
      }

      p {
        color: #004F59;
      }
    }

    .benefits-bullets {
      ul {
        list-style: none;
        margin-left: 0;
      }

      ul li {
        margin-bottom: 15px;
        padding-bottom: 5px;
        padding-left: 20px;
        // background: url(/assets/imgphase3/ap-bullet-pin.gif) left top no-repeat;
        // leaving commented out in case they want to use the image again

      }
    }
  }

  .cs table.table.table-packages {
    border-top: none !important;

    th {
      white-space: nowrap;
      background: #B7355F !important;
      border-radius: 6px 6px 0 0;
    }

    tr th:nth-child(1) {
      background: #aaa !important;
      border: 1px solid #aaa !important;
      border-radius: 6px 0 0 0;
      display: block;
      margin-top: 20px;
    }

    th,
    td {
      text-align: center;
      vertical-align: middle;
    }

    th:first-child,
    td:first-child {
      text-align: left;
    }

    td span.pinktick {
      background-image: url(/assets/imgphase3/pins.png);
      background-repeat: no-repeat;
      background-position: 0 -22px;
      height: 20px;
      width: 10px;
      display: inline-block;
    }

    tr td:nth-child(n+1) {
      transition: background .3s;
    }

    tr td:nth-child(2) {
      background: rgba(183, 53, 95, .033);
    }

    tr td:nth-child(2).hovered {
      background: rgba(183, 53, 95, .25);
    }

    tr td:nth-child(3) {
      background: rgba(183, 53, 95, .066);
    }

    tr td:nth-child(3).hovered {
      background: rgba(183, 53, 95, .3);
    }

    tr td:nth-child(4) {
      background: rgba(183, 53, 95, .1);
    }

    tr td:nth-child(4).hovered {
      background: rgba(183, 53, 95, .35);
    }

    tr:last-child td {
      background: #aaa;
      color: #fff;

    }
  }

  .es table.table.table-packages {
    border-top: none !important;

    th {
      white-space: nowrap;
      background: #f8981d !important;
      border-radius: 6px 6px 0 0;
      color: #fff;
    }

    tr th:nth-child(1) {
      background: #aaa !important;
      border: 1px solid #aaa !important;
      border-radius: 6px 0 0 0;
      display: block;
      margin-top: 20px;
    }

    th,
    td {
      text-align: center;
      vertical-align: middle;
    }

    th:first-child,
    td:first-child {
      text-align: left;
    }

    td span.pinktick {
      background-image: url(/assets/imgphase3/pins.png);
      background-repeat: no-repeat;
      background-position: 0 -220px;
      height: 20px;
      width: 10px;
      display: inline-block;
    }

    tr td:nth-child(n+1) {
      transition: background .3s;
    }

    tr td:nth-child(2) {
      background: rgba(248, 152, 29, .033);
    }

    tr td:nth-child(2).hovered {
      background: rgba(248, 152, 29, .25);
    }

    tr td:nth-child(3) {
      background: rgba(248, 152, 29, .066);
    }

    tr td:nth-child(3).hovered {
      background: rgba(248, 152, 29, .3);
    }

    tr td:nth-child(4) {
      background: rgba(248, 152, 29, .1);
    }

    tr td:nth-child(4).hovered {
      background: rgba(248, 152, 29, .35);
    }

    tr:last-child td {
      background: #aaa;
      color: #fff;

    }
  }

  .as table.table.table-packages {
    border-top: none !important;

    th {
      white-space: nowrap;
      background: #00a7e5 !important;
      border-radius: 6px 6px 0 0;
      color: #fff;
    }

    tr th:nth-child(1) {
      background: #aaa !important;
      border: 1px solid #aaa !important;
      border-radius: 6px 0 0 0;
      display: block;
      margin-top: 20px;
    }

    th,
    td {
      text-align: center;
      vertical-align: middle;
    }

    th:first-child,
    td:first-child {
      text-align: left;
    }

    td span.pinktick {
      background-image: url(/assets/imgphase3/pins.png);
      background-repeat: no-repeat;
      background-position: 0 -48px;
      height: 20px;
      width: 10px;
      display: inline-block;
    }

    tr td:nth-child(n+1) {
      transition: background .3s;
    }

    tr td:nth-child(2) {
      background: rgba(0, 167, 229, .033);
    }

    tr td:nth-child(2).hovered {
      background: rgba(0, 167, 229, .25);
    }

    tr td:nth-child(3) {
      background: rgba(0, 167, 229, .066);
    }

    tr td:nth-child(3).hovered {
      background: rgba(0, 167, 229, .3);
    }

    tr td:nth-child(4) {
      background: rgba(0, 167, 229, .1);
    }

    tr td:nth-child(4).hovered {
      background: rgba(0, 167, 229, .35);
    }

    tr:last-child td {
      background: #aaa;
      color: #fff;

    }
  }

  .ap table.table.table-packages {
    border-top: none !important;

    th {
      white-space: nowrap;
      background: #004F59 !important;
      border-radius: 6px 6px 0 0;
      color: #fff;
    }

    tr th:nth-child(1) {
      background: #aaa !important;
      border: 1px solid #aaa !important;
      border-radius: 6px 0 0 0;
      display: block;
      margin-top: 20px;
    }

    th,
    td {
      text-align: center;
      vertical-align: middle;
    }

    td {
      color: #004F59;
      font-weight: bold;
    }

    td:first-child {
      font-weight: normal;
      color: #807f83;
    }

    th:first-child,
    td:first-child {
      text-align: left;
    }

    td span.pinktick {
      background-image: url(/assets/imgphase3/pins.png);
      background-repeat: no-repeat;
      background-position: 0 -74px;
      height: 20px;
      width: 10px;
      display: inline-block;
    }

    tr td:nth-child(n+1) {
      transition: background .3s;
    }

    tr td:nth-child(2) {
      background: rgba(118, 167, 50, .033);
    }

    tr td:nth-child(2).hovered {
      background: rgba(118, 167, 50, .25);
    }

    tr td:nth-child(3) {
      background: rgba(118, 167, 50, .066);
    }

    tr td:nth-child(3).hovered {
      background: rgba(118, 167, 50, .3);
    }

    tr td:nth-child(4) {
      background: rgba(118, 167, 50, .1);
    }

    tr td:nth-child(4).hovered {
      background: rgba(118, 167, 50, .35);
    }

    tr:last-child td {
      background: #aaa;
      color: #fff;

    }
  }



  // Payment

  .events-ext_bookings.payment .modal-body {
    #payment-form .payment-info {
      margin-botton: 10px;

      h4 {
        display: block;
        background: #000;
        color: #fff;
        padding: 4px 7px;
      }

      table.table {
        border-bottom: 1px solid #ddd;
        font-size: 12px;
        line-height: 17px;
        margin-bottom: 15px;
      }

      th {
        text-align: left;
        padding: 1px 3px;
        color: #000;
        background: #eee;
      }

      td {
        font-weight: normal;
        padding: 1px 3px;
      }

      td.price,
      th.price {
        text-align: right;
      }
    }

    #payment-form label.radio {
      display: inline-block;
      margin-right: 20px;
      line-height: 25px;
      margin-bottom: 0;

      input {
        margin-left: -18px;
      }
    }
  }

  .events-ext_bookings.payment .modal-footer {
    text-align: left;

    .cc-logos {
      max-height: 20px;
      margin-right: 3px;
      display: inline;
      background: transparent;
    }
  }

  .events-ext_bookings.payment h3 {
    span {
      padding-top: 10px;
      display: block;
      line-height: 20px;
    }
  }

  .padlock {
    max-height: 30px;
    margin-right: 5px;
    float: left;
  }








  table.table td.status {
    text-align: center;

    span {
      margin: 1px;
      text-align: center;
      display: inline-block;
      width: 30px;
      height: 30px;
      padding: 0px;
      line-height: 31px;
    }

    span.nostatus {
      color: #fff;
      background-color: #ccc;
    }

    span.act {
      color: #fff;
      background-color: #cf1046;
    }
  }

  div#acc-modal.chase-modal .modal-body {
    position: relative;
    max-height: 425px;
  }

  #acc-modal.chase-modal {
    margin-left: -450px;
    width: 900px !important;
  }

  #acc-modal.chase-modal div.chase-list-left {
    max-height: 375px;
    position: relative;
    overflow-y: auto;
  }

  #acc-modal.chase-modal div.chase-list-right {
    max-height: 375px;
  }

  div#myModal.modal.bigger .modal_body {
    min-height: 900px;
  }

  div#override-modal-tall .modal-body {
    height: 450px;
  }

  form.simple_form.form-horizontal div.control-group.boolean.image_publish div.controls {
    margin-left: 0px;
    margin-top: 10px;
  }

  form.simple_form.form-horizontal div.control-group.boolean.image_publish {
    margin-bottom: 0;

  }

  div#override-modal-big .modal-body {
    position: relative;
    max-height: 435px;
  }

  #override-modal-big {
    margin-left: -450px;
    width: 900px !important;

    .well {
      border: none;
    }
  }

  #override-modal-xbig {
    margin-left: -600px;
    width: 1200px !important;
    margin-top: -5vh;
    height: auto;

    .well {
      border: none;
    }
  }

  div#override-modal-xbig .modal-body {
    position: relative;
    max-height: unset;
  }

  div#override-modal-xbig input.full_width {
    width: 96% !important;
  }

  div#override-modal-xbig div.well {
    padding-top: 3px;
    padding-bottom: 3px;
    margin-top: 3px;
    margin-bottom: 3px;
  }

  div#override-modal-xbig div.row-fluid {
    padding-top: 1px;
    padding-bottom: 1px;
  }

  div#override-modal-med .modal-body {
    position: relative;
    max-height: 300px;
  }

  #override-modal-med {
    margin-left: -450px;
    width: 900px !important;
  }

  #override-modal-med.modal.hide.fade.in div.modal-body div.row-fluid div.control-group div.controls textarea {
    height: 250px;
    width: 850px;
  }







  .adult_accommodations {
    .adaccom {
      margin-bottom: 50px;
    }

    .partner {
      text-align: right;

      img {
        max-height: 90px;
      }
    }

    .intro {
      text-align: center;
      margin: 30px 0px;
      padding: 25px;

      h1 {
        font-size: 30px;
        line-height: 46px;
      }

      p {
        font-size: 19px;
        line-height: 24px;
      }
    }

    .form-horizontal {
      .control-label {
        width: 250px;
      }

      div.controls {
        margin-left: 270px;
      }
    }

    .table {
      .small {
        font-weight: 400;
      }

      td:last-child {
        text-align: left !important;
      }

      td {
        input {
          float: left;
          margin-right: 10px;
        }

        label {
          float: left;
          width: auto;
          margin-bottom: 0;
        }
      }

      th {

        color: #eee;
        background: #000;
      }
    }

  }

  ul.inline-links {
    display: block;

    li.inline-link {
      display: inline-block;
      margin-bottom: 10px;
    }
  }

  div.task-error {
    color: red;
    width: 150px;
    height: auto;
    font-size: 11px;
    padding: 2px;
  }

  .hg .well a.conf-date-link {
    color: #807F83;
    border-bottom: 1px solid grey !important;
    margin-bottom: 2px;
  }

  .hg .well a.conf-date-link:hover {
    text-decoration: none;

    div span.label,
    div span.data_value {
      background-color: darkgrey !important;
      color: #fff;
    }

    div {
      background-color: darkgrey !important;
      color: #fff;
    }

  }





  .hg .well a.conf-date-link-red {
    color: #fff;
    border-bottom: 1px solid grey !important;
    margin-bottom: 2px;

    div span.label,
    div span.data_value {
      background-color: #fcaeae !important;
      color: #fff;
    }

    div {
      background-color: #fcaeae !important;
      color: #fff;
    }
  }

  .hg .well a.conf-date-link-red:hover {
    text-decoration: none;

    div span.label,
    div span.data_value {
      background-color: #b56565 !important;
      color: #fff;
    }

    div {
      background-color: #b56565 !important;
      color: #fff;
    }

  }

  ;


  /* Map button */
  #map_canvas {
    .search-button-container {
      z-index: 1 !important;
      position: absolute;
      left: 114px !important;
      top: 10px !important;

      .btn-success {
        direction: ltr;
        overflow: hidden;
        text-align: center;
        position: relative;
        color: #fff !important;
        font-family: Roboto, Arial, sans-serif;
        -webkit-user-select: none;
        font-size: 11px;
        padding: 7px 8px;
        border: none;
        border-radius: 2px;
        -webkit-background-clip: padding-box;
        box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px;
        text-shadow: none !important;
        min-width: 40px;
        background: #ad1a59;
        font-weight: 500;
      }

      .btn-success:hover {
        background: #801f3e;
      }
    }
  }

  select#rfq_learners {
    color: #a59da3;

    option {
      color: #5c5c5c;
    }
  }

  div.well.cluster {
    nav ul li {
      margin-bottom: 10px;
    }

    nav ul {
      list-style: none;
    }
  }

  div.well div#pwd-info {

    position: absolute;
    background-color: white;
    opacity: 1;
    width: 320px;
    padding: 5px;
    border: 2px solid #49175B;
    border-radius: 1em;
    z-index: 50;
  }

  div.well div#pwd-conf-info {
    position: absolute;
    background-color: white;
    opacity: 1;
    width: 320px;
    padding: 5px;
    border: 2px solid #49175B;
    border-radius: 1em;
  }

  div.well div#pwd-info ul {
    list-style: none;
  }

  div.well div#pwd-info ul li.invalid {
    color: #D12E02;
  }

  div.well div#pwd-info ul li.valid {
    color: #1BB703;
  }

  div.well div#pwd-conf-indicator {
    //top and left will be set by js through options
    position: absolute;
    display: block;

  }

  .modal.fade.in.lower {
    top: 22%;
  }

  .stops-logos {
    max-width: 938px;
  }

  .bg-green {
    background-color: #DFF0D8;
  }

  ul.ultable {
    display: inline-block;
    width: 100%;
    margin-left: 0px;
    margin-bottom: 0px;
    min-height: 50px;
    height: auto;
    border-bottom: 1px solid grey;

    li {
      display: inline-block;
      width: 100px;
      height: auto;
      min-height: 50px;
      margin: 0px;
      padding: 5px 5px;
      text-align: center;
      vertical-align: middle;
    }
  }

  ul.ultable.hdr {
    font-weight: bold;
    font-size: 13px;
  }

  ul.ultable li.id {
    width: 37px;
  }

  ul.ultable li.email {
    width: 190px;
  }

  ul.ultable li.sel {
    width: 90px;
  }

  ul.ultable li.stat {
    width: 180px;

    .inv-status-l {
      display: inline-block;
      width: 90px;
      min-height: 48px;
    }

    .inv-status-r {
      display: inline-block;
      width: 70px;
      vertical-align: middle;
      min-height: 48px;
    }
  }


  #adu-det-modal .modal-body.inv-grp {
    .control-group {
      width: auto;

      label.control-label {
        margin-left: 20px;
        width: 120px;
      }

      .controls input,
      {
      margin-left: -40px;
    }

    .controls textarea {
      width: 508px;
      margin-left: -20px;
    }

    .controls select {
      width: 232px;
      margin-left: -30px;
    }
  }

  }

  .ui-autocomplete {
    z-index: 2147483647;
  }

  tr.amber td {
    background-color: #EFF4a4;
  }

  tr.green td {
    background-color: #C6F9B6;
  }

  #development-ribbon .ribbon {
    z-index: 999;
    pointer-events: none;
  }

  #development-ribbon .ribbon-holder {
    z-index: 999;
    pointer-events: none;
    position: fixed !important;
  }

  h5.red {
    color: red;
  }

  h5.green {
    color: green;
  }

  table.table td.strike-thro a {
    text-decoration: line-through
  }

  span.cancelled {
    color: red;
  }

  /*
  .span6 .well .radio label {
    width:400px;
    font-size:15px;
  }
  .well.opt-in .radio label {
    font-size:15px;
  }


  .span6 .well .radio input[type="radio"]{
    margin-left:5px;
    margin-right:10px;
    margin-top: 2px;
    vertical-align: middle;
  }

  .well.opt-in .radio input[type="radio"]{
    margin-top: 2px;
    vertical-align: middle;
  }
   */

  #editprofiles .span6 #contact-permission .control-group {
    display: block;
    width: 100%;
    float: left;

    .controls {
      display: block;
      width: 100%;
      float: left;

      .radio {
        position: relative;
        display: block;
        padding-left: 1.25rem;
        width: 100%;
        float: left;

        label {
          margin-bottom: 0;
          width: auto;
          font-size: 13px;
          margin: 0 15px 0 0;
        }

        input[type=radio] {
          box-sizing: border-box;
          padding: 0;
          position: absolute;
          margin-top: 2px;
          margin-left: -1.25rem;
          vertical-align: middle
        }
      }
    }
  }


  form#new_user #contact-permission {
    .controls {
      display: block;
      width: 100%;
      float: left;
      margin: 0;

      .radio {
        position: relative;
        display: block;
        padding-left: 1.25rem;
        width: 100%;
        float: left;

        label {
          margin-bottom: 0;
          width: auto;
          font-size: 13px;
          margin: 0 15px 0 0;
        }

        input[type=radio] {
          box-sizing: border-box;
          padding: 0;
          position: absolute;
          margin-top: 2px;
          margin-left: -1.25rem;
          vertical-align: middle;
          min-height: auto;
        }
      }
    }

  }

  .modal .modal-body .invalid-feedback {
    border: 1px solid red;
    width: 220px;
    margin-left: 150px;
    color: darkred;
    background-color: pink;
    padding: 2px;
    border-radius: 4px;
  }

  .admin-tasks_dashboards .datepicker {
    z-index: 500 !important;
  }

  .chip-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    vertical-align: middle;
  }

  .chip {
    color: white;
    margin-right: 4rem;
    margin-top: 0.2rem;
  }

  .chip-safe {
    background-color: green;
  }

  .chip-danger {
    background-color: red;
  }

  #servace-nav {
    display: inline-flex;
    width: 100%;
  }

  #servace-nav .secondary-nav-container{
    justify-content: space-evenly;
    align-items: center;
  }

  #bg .container{
    width: 95% !important;
    margin-left: 2rem;
    margin-right: -2rem;
  }


  .bg-dark {
    --bs-bg-opacity: 1 !important;
    background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

}

.bg-dark {
    --bs-bg-opacity: 1 !important;
    background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

.nav-tabs.maintabs {
  li {
    a {
      background-image: none;  // Remove old pin background
    }
    
    &.active a {
      background-image: none;  // Remove old pin background
    }
  }
}
