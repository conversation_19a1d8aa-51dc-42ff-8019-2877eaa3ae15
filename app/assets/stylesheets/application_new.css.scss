/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS and SCSS file within this directory, lib/assets/stylesheets, vendor/assets/stylesheets,
 * or vendor/assets/stylesheets of plugins, if any, can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the top of the
 * compiled file, but it's generally better to create a new file per style scope.
 * THIS MUST BE EXCLUDED FROM THE LOADING OF THE OTHER CSS FILES
 *
 *= require_self
 *= require font-awesome
 *= require development_ribbon
*/

/****   maintabs   */
.maintabs {
  margin: 10px 0 10px 0;
}

.maintabs li {
  margin-bottom: -2px;
}

.maintabs li a {
  background-image: url(/assets/imgphase3/pins.png);
  background-repeat: no-repeat;
  padding-left: 24px;
  line-height: 18px;
  *line-height: 16px !important;
}

.maintabs li a strong,
.maintabs li.active a:hover strong,
.maintabs li.active a:hover .dot {
  color: #242021;
}

.maintabs li a:hover {
  // background-position: 7px -96px;
  // background-position: 7px -170px;
  border-bottom: none;
}

// .maintabs li a:hover strong,
// .maintabs li a:hover .dot {
//   color: #fff;
// }

// .cs .nav-tabs li.active a {
//   color: #333;
// }

.cs .nav-tabs li a,
.cs .nav-tabs li.active {
  color: darkgray;
}

.nav-tabs li a:hover {
  background: #b7355f;
  border: 1px solid #99254a;
  color: #fff;
}

.nav-tabs li.active a:hover {
  // background: none;
  border: 1px solid #ddd;
  border-bottom-color: #f9f9f9;
}

.ap-nav-tabs li a:hover {
  background: #004F59;
  // this was causing sizing to break when nav tabs were hovered
  // border: 1px solid #63951d;
}

li.hg a,
li.hg.active a:hover {
  background-position: 7px -150px;
}

li.hg a:hover {
  background-color: #5b3768;
  border-color: #472653;
  background-position: 7px -220px;
}

li.os a,
li.os.active a:hover {
  background-position: 7px 9px;
}

li.os a .dot,
li.os.active a:hover .dot {
  color: #5b3768;
}

li.os a:hover {
  background-color: #5b3768;
  border-color: #472653;
}

li.cs a,
li.cs.active a:hover {
  background-position: 7px -17px;
}

li.cs a .dot,
li.cs.active a:hover .dot {
  color: #b7355f;
}

li.cs a:hover {
  background-color: #b7355f;
  border-color: #99254a;
}

li.as a,
li.as.active a:hover {
  background-position: 7px -43px;
}

li.as a .dot,
li.as.active a:hover .dot {
  color: #00a7e5;
}

li.as a:hover {
  background-color: #00a7e5;
  border-color: #0087b9;
}

li.ap a,
li.ap.active a:hover {
  background-position: 7px -69px;
}

li.ap a .dot,
li.ap.active a:hover .dot {
  color: #004F59;
}

li.ap a:hover,
li.ap a:hover {
  background-color: #004F59;
  border-color: #63951d;
}

li.es a,
li.es.active a:hover {
  background-position: 7px -216px;
}

li.es a .dot {
  color: #f8981d;
}

li.es a:hover .dot {
  color: #fff;
}

li.es a:hover {
  background-color: #f8981d;
  border-color: #d67801;
  background-position: 7px -24px;
}

li.ac a,
li.ac.active a:hover {
  background-position: 7px -216px;
}

li.ac a .dot {
  color: #E84F1C;
}

li.ac a:hover .dot {
  color: #fff;
}

li.ac a:hover {
  background-color: #E84F1C;
  border-color: #E84F1C;
  background-position: 7px -24px;
}
