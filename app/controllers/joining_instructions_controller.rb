class JoiningInstructionsController < ApplicationController
    include ActiveStorage::SetCurrent
    before_action :set_site
    layout "apprentice_dashboard"

    def index
        @rfq_task_id = params[:rfq_task_id].to_i
        @type = params[:type]
        # this is just here to render the initial html page that hosts the vue component, data is retrieved later
        respond_to do |format|
            format.html # show.html.erb
        end
    end

    def get_data
        if params[:id]
            @current_ji = JoiningInstruction.includes(:sections).find(params[:id])
        else
            @current_ji = JoiningInstruction.includes(:sections).where(rfq_task_id: params[:rfq_task_id].to_i, ji_type: params[:type], template: false).last
        end

        if @current_ji.blank?
            @current_ji = create_ji
        end
        # calls get_ji.json.jbuilder
    end

    def jis_on_rfq
        jis = JoiningInstruction.includes(:acc_bookings).where(rfq_task_id: params[:rfq_task_id], ji_type: params[:type])
        deletable_ji_ids = jis.where(acc_bookings: {joining_instruction_id: jis.map(&:id)}).map(&:id)
        jis.each do |ji|
            if deletable_ji_ids.include?(ji.id)
                ji.deletable = false
            end
        end
        render json: { jis: jis }
    end

    def ji_templates
        rfq_task_id = params[:rfq_task_id].to_i
        task = RfqTask.find(rfq_task_id)
        programme_id = task.rfq_request_id.present? ? task.rfq_request.rfq_programme.id : task.rfq_response.rfq_location.rfq_request.rfq_programme.id
        jis = JoiningInstruction.where(rfq_programme_id: programme_id, ji_type: params[:type], template: true)
        jis = jis.select('id, title')
        render json: { jis: jis }
    end

    def update
        @current_ji = JoiningInstruction.includes(:sections).find(params[:id])
        if params[:approve] == "true"
            @current_ji.approved_at = DateTime.now
            @current_ji.approved_by_id = @current_user.id
            complete_task
        end
        if @current_ji.update(joining_instruction_params)
            render json: { success: true}
        else
            render json: { error: "Error Saving JI" }, status: 400
        end
    end

    def save_logos
        @current_ji = JoiningInstruction.find(params[:id])
        if params[:tag] == "left"
            @current_ji.left_logo.attach(params[:file])
            url = @current_ji.left_logo.url
        elsif params[:tag] == "right"
            @current_ji.right_logo.attach(params[:file])
            url = @current_ji.right_logo.url
        end
        if url.present?
            render json: { logo_url: url }
        else
            render json: { error: "Unable to upload", status: 500}
        end
    end

    def remove_logo
        @current_ji = JoiningInstruction.find(params[:id])
        if params[:tag] == "left"
            @current_ji.left_logo.purge
        elsif params[:tag] == "right"
            @current_ji.right_logo.purge
        end
    end

    def save_attachments
        @current_ji = JoiningInstruction.find(params[:id])
        @current_ji.attachments.attach(params[:file])
    end

    def remove_attachment
        @current_ji = JoiningInstruction.find(params[:id])
        @current_ji.attachments.where(id: params[:file_id]).purge
    end

    def remove_approval
        @current_ji = JoiningInstruction.find(params[:id])
        @current_ji.approved_at = nil;
        @current_ji.approved_by_id = nil;
        @current_ji.save!

        @current_ji.rfq_task.reset!
    end

    def go_back
        rfq_task = RfqTask.find(params[:rfq_task_id].to_i)
        if @current_user.is_a_client?
            if rfq_task&.rfq_request&.virtual?
                redirect_to client_programme_path(rfq_task.rfq_request.rfq_locations.first)
            else
                redirect_to client_programme_rfq_joining_instructions_path(rfq_task.parent.rfq_location, :rfq_task_id => rfq_task.id)
            end
        elsif
            if rfq_task&.rfq_request&.virtual?
                redirect_to admin_rfq_request_rfq_tasks_path(rfq_task.rfq_request)
            else
                redirect_to admin_rfq_response_rfq_task_rfq_joining_instructions_path(rfq_task.parent, rfq_task)
            end
        end
    end

    # called from post method
    def create_new_ji
        ji = create_ji
        render json: { ji: ji }
    end

    def send_preview
        @current_ji = JoiningInstruction.includes(:sections).find(params[:id])
        if @current_ji.ji_type == JoiningInstruction.ji_types['virtual']
            AccBookingMailer.delay.send_ji_preview(@current_ji, @current_user, true, false, 'Manager Virtual JI')
            AccBookingMailer.delay.send_ji_preview(@current_ji, @current_user, false, false, 'Learner Virtual JI')
        else
            AccBookingMailer.delay.send_ji_preview(@current_ji, @current_user, true, false, 'Manager Residential JI')
            AccBookingMailer.delay.send_ji_preview(@current_ji, @current_user, true, true, 'Manager Subsistence JI')
            AccBookingMailer.delay.send_ji_preview(@current_ji, @current_user, false, false, 'Learner Residential JI')
            AccBookingMailer.delay.send_ji_preview(@current_ji, @current_user, false, true, 'Learner Subsistence JI')
        end
    end

    def copy_ji
        ji = JoiningInstruction.includes(:sections).find(params[:id])
        rfq_task = RfqTask.find(params[:rfq_task_id])
        ji_copy = ji.deep_clone include: :sections
        if ji_copy.is_a?(JoiningInstruction) && ji.left_logo.attached?
            attachment = ji.left_logo
            ji_copy.left_logo.attach \
              :io           => StringIO.new(attachment.download),
              :filename     => attachment.filename,
              :content_type => attachment.content_type
        end

        if ji_copy.is_a?(JoiningInstruction) && ji.right_logo.attached?
            attachment = ji.right_logo
            ji_copy.right_logo.attach \
              :io           => StringIO.new(attachment.download),
              :filename     => attachment.filename,
              :content_type => attachment.content_type
        end

        if ji_copy.is_a?(JoiningInstruction) && ji.attachments.attached?
            all_attachments_arr = ji.attachments.map do |attachment|
                {
                    :io           => StringIO.new(attachment.download),
                    :filename     => attachment.filename,
                    :content_type => attachment.content_type
                }
            end
            ji_copy.attachments.attach(all_attachments_arr) # attach all at once
        end
        rfq_location = ji.ji_type == JoiningInstruction.ji_types['virtual'] ? rfq_task.rfq_request.rfq_locations.first : rfq_task.rfq_response.rfq_location
        ji_copy.organisation_id = rfq_location.rfq_request.rfq_programme.client_id
        ji_copy.title = ji.title + ' (Copy)'
        ji_copy.rfq_task_id = params[:ji_turning_into] == "JI" ? rfq_task.id : nil
        # ji_copy.ji_type = JoiningInstruction.ji_types[params[:ji_turning_into].to_sym]
        ji_copy.template = params[:ji_turning_into] == "Template" ? true : false
        ji_copy.rfq_programme_id = rfq_location.rfq_request.rfq_programme.id
        ji_copy.approved_by_id = nil
        ji_copy.approved_at = nil

        # This is here to account for attr accessors on sections
        ji_copy.sections.each do |section|
            section.errors = []
            section.table = nil
        end
        ji_copy.save!
        render json: { ji: {id: ji_copy.id, title: ji_copy.title} }
    end

    def create_ji
        ji = JoiningInstruction.create(
            ji_type: params[:type].to_i,
            organisation_id: @current_user.contact.organisation.id,
            rfq_task_id: params[:rfq_task_id].to_i,
            template: false
        )
        return ji
    end

    def deleteJI
        ji = JoiningInstruction.find(params[:id]).destroy
    end

    def get_date_in_advance
        date = AccBooking.new.add_working_days(Date.today, Date.today + params["days_to_add"].to_i.days)
        render json: { date: date }
    end

    private

    def set_site
        if current_user.is_a_client?
            @acc_client_mode = true
        elsif current_user.is_an_administrator?
            @acc_admin_mode = true
        elsif current_user.is_a_hotel_user?
            @acc_supplier_mode = true
        end
        @body_class = "ap"
        session[:site_name] = "ap"
    end

    def complete_task
        @current_ji.rfq_task.accepted_client_at = Time.zone.now
        @current_ji.rfq_task.accepted_client_by = maybe_masquerader.contact.full_name
        @current_ji.rfq_task.save
        @current_ji.rfq_task.complete!
    end

    def joining_instruction_params
        params.require(:joining_instruction).permit(:id, :title, :organisation_id, :rfq_task_id, :ji_type, :rfq_programme_id, :deletable, :no_of_days_in_advance_to_send, :no_of_days_in_advance_to_send_business_days, :template, logos: [], attachments: [],
        sections_attributes: [:id, :title, :content, :manager, :learner, :residential, :subsistence, :position, :_destroy, :table, :faking_data_id, :template, errors: []])
    end
  end
