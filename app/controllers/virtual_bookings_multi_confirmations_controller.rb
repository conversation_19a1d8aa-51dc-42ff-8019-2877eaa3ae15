class VirtualBookingsMultiConfirmationsController < ActionController::Base
  cache_sweeper :user_stamp_sweeper

  before_action :load_bookings
  layout "stripe_payments_base"

  def show
    @conf_booking = @booking.acc_booking_header.rfq_location.accepted_rfq_booking_conf
    # if @booking.cancelled? || @booking.confirmed?
    #   redirect_to already_processed_acc_booking_confirmation_path(:id => @booking.booking_token)
    # end
  end

  def update
    #TODO check if cancelling, if so send a bulk cancellation email. also check for confirmations
    @booking.assign_attributes( safe_params )
    @booking.check_cancel_confirmation = true unless params[:commit] =~ /card/
    @learner = @person.try(:rfq_learner)
    @rfq_location = @booking.acc_booking_header.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @conf_booking = @rfq_location.accepted_rfq_booking_conf
    @person = @booking.acc_booking_person
    @booking_header = @booking.acc_booking_header

    unless @booking.valid?
      render :show and return
    end

    updated_bookings = []

    cancelled_bookings = []

    @bookings_not_cancelled.each do |booking|
      if booking.update(safe_params)
        if params[:acc_booking][:cancellation_wish]
          cancelled_bookings << booking.id
        else
          updated_bookings << booking.id
        end
      else
        @booking.errors.add(:base, "A booking could not be updated,please check it was not already cancelled or removed: " + booking.acc_selected_module.title)
      end
    end

    if updated_bookings.present?
      # Create method to send to both of these in mailer
      AccBookingMailer.delay.send_virtual_multi_confirmation(updated_bookings)
      AccBookingMailer.delay.send_virtual_multi_confirmation(updated_bookings, true)
    end

    if cancelled_bookings.present?
       # Create method to send to both of these in mailer
      AccBookingMailer.delay.send_bulk_cancellation(cancelled_bookings)
      AccBookingMailer.delay.send_bulk_cancellation(cancelled_bookings, true)
      render :cancelled
    end
  end

  def booking_redirector
    #TODO old virtual, deprecated
    if @booking.present?
      abh = @booking.acc_booking_header
      vm = abh.online_resource
      if @booking.link_clicked_at.blank? && abh.virtual_module_date.beginning_of_day == Date.today.beginning_of_day
        @booking.link_clicked_at = Time.zone.now
        @booking.save(:validate => false)
        send_email
      end
      redirect_to vm.resource_link
    else
      #render custom not found
    end
  end

  def booking_redirector_new
    if @booking.present?
      selected_module = @booking.acc_selected_module
      vm = selected_module.virtual_module
      if @booking.link_clicked_at.blank? && selected_module.flexi_module_start.beginning_of_day == Date.today.beginning_of_day
        @booking.link_clicked_at = Time.zone.now
        @booking.save(:validate => false)
        send_email
      end
      redirect_to vm.resource_link
    else
      render :booking_redirector
    end
  end

  def send_email
    VirtualJiMailer.delay.send_feedback_link(@booking.id)
  end

  def load_bookings
    @booking = AccBooking.where(:booking_token => params[:id]).first
    if @booking.blank?
      redirect_to token_not_found_acc_booking_confirmations_path
      return
    end

    get_bookings(@booking)
  end

  def get_bookings(booking)
    @bookings = booking.acc_booking_header.acc_bookings.where(acc_booking_person_id: booking.acc_booking_person_id)
    @bookings_not_cancelled = @bookings.where("cancelled_at is null AND deleted_at is null")

    @booking_modules = []
    @bookings_not_cancelled.each do |bk|
      @booking_modules << bk.acc_selected_module
    end
    @all_confirmed = @bookings.count{|bk| bk.confirmed_at.present?} == @bookings.count && @bookings.count > 0
    @all_cancelled = @bookings.count{|bk| bk.cancelled_at.present?} == @bookings.count && @bookings.count > 0
  end

  def safe_params
    params.require(:acc_booking).permit(:temp_learner_name, :rfq_response_room_id,
                                        { :acc_week_blocks_attributes => [:id, :'_destroy', :start_date, :sun, :mon, :tue, :wed, :thu, :fri, :sat] },
                                        :payment_method, :subsistence_only, :special_requirements, :rooming_preference, :cancellation_wish, :cancellation_reason, :cancelled_by, :confirmed_by, :confirmed_flag, :first_checkin, :last_check_out, :hg_note, :client_note, :complimentary, :cost_code, :train_details_req, :from_station,
                                        { :acc_additional_people_attributes => [:id, :'_destroy', :forename, :surname, :email, :telephone, :staff_member] }, :acc_booking_header_id, :no_guest_email, :room_modifier,
                                        { :rfq_confirmation_answers_attributes => [:id, :'_destroy', :answer, :rfq_confirmation_question_id] }, :single_as_twin, :single_reason, :single_reason_req, :accommodation_requirement_id, :validate_accomm, :room_type, :clash_ignored_by)
  end

end

