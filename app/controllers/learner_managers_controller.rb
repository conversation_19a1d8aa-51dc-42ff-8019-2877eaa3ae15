class LearnerManagersController < AccBookingsBaseController
  layout 'quasarify_layout'
  before_action :set_site, only: [:index, :dashboard, :invite, :data]
  before_action :set_dashboard_data, only: [:dashboard, :data]

  def index
    respond_to do |format|
      format.html
      format.json do
        learner_managers = LearnerManager.all

        # Filtering by table columns only
        learner_managers = learner_managers.where('forename ILIKE ?', "%#{params[:forename]}%") if params[:forename].present?
        learner_managers = learner_managers.where('surname ILIKE ?', "%#{params[:surname]}%") if params[:surname].present?
        learner_managers = learner_managers.where('email ILIKE ?', "%#{params[:email]}%") if params[:email].present?
        learner_managers = learner_managers.where(organisation_id: params[:organisation_id]) if params[:organisation_id].present?
        learner_managers = learner_managers.where('telephone ILIKE ?', "%#{params[:telephone]}%") if params[:telephone].present?
        # Add programme filter
        if params[:programme_id].present?
          learner_managers = learner_managers.joins(rfq_learners: :rfq_programme)
                                             .where(rfq_learners: { rfq_programme_id: params[:programme_id] })
                                             .distinct
        end
        # Invited filter (now a real column)
        if params[:invited].present?
          learner_managers = learner_managers.where(invited: params[:invited].to_s == "true")
        end
        # Joined filter (now a real column)
        if params[:joined].present?
          learner_managers = learner_managers.where(signed_up: params[:joined].to_s == "true")
        end

        # Pagination
        page = params[:page].to_i > 0 ? params[:page].to_i : 1
        per_page = params[:per_page].to_i > 0 ? params[:per_page].to_i : 20

        # If select returns an array, convert to ActiveRecord::Relation for pagination
        if learner_managers.is_a?(Array)
          total_count = learner_managers.size
          paged = learner_managers.slice((page - 1) * per_page, per_page) || []
        else
          total_count = learner_managers.count
          paged = learner_managers.order(created_at: :desc).offset((page - 1) * per_page).limit(per_page)
        end

        total_pages = (total_count / per_page.to_f).ceil

        render json: {
          learner_managers: paged.map { |lm|
            lm.as_json(
              only: [:id, :forename, :surname, :email, :organisation_id, :telephone, :created_at]
            ).merge(
              organisation_name: lm.try(:organisation_name),
              invited: lm.invited?,
              signed_up: lm.signed_up?,
              programme_name: lm.programme_names # comma-separated string
            )
          },
          total_count: total_count,
          total_pages: total_pages,
          page: page
        }
      end
    end
  end

  def dashboard
    # Only render HTML, data is set by before_action
    respond_to do |format|
      format.html # renders dashboard.html.erb as before
    end
  end

  def data
    # Only render JSON, data is set by before_action
    render json: @dashboard_data
  end

  def invite(manager_id = nil)
    manager_id ||= params[:manager_id]
    manager = LearnerManager.find(manager_id)

    contact = Contact.where(email: manager.email).first_or_initialize
    if contact.new_record?
      contact.first_name = manager.forename
      contact.surname = manager.surname
      contact.email = manager.email
      contact.telephone = manager.telephone
      contact.organisation_id = manager.organisation_id
      contact.save!
    end

    organisation_name = params[:organisation_name] || manager.try(:organisation)&.try(:name)

    contact.create_servace_app_user_and_invite(current_user, 'LEARNER_MANAGER', organisation_name: organisation_name)

    # Set invited flag to true
    manager.update(invited: true)

    render json: { success: true }
  end

  private

  def set_site
    @learner_manager_mode = true
    @admin_mode = true
    @body_class = "lm"
    session[:site_name] = "lm"
  end

  def set_dashboard_data
    learner_manager = LearnerManager.find_by(email: current_user.email)
    unless learner_manager
      if action_name == 'dashboard'
        flash[:alert] = "You are not a registered Learner Manager. Please contact support."
        redirect_to root_path and return
      else
        render json: { error: "Not a registered Learner Manager" }, status: :forbidden and return
      end
    end

    learners = learner_manager.rfq_learners.order(created_at: :desc)
    
    # Set default date range: start of current month to 6 weeks in future
    # Accept both date_from/date_to (from Vue component) and start_date/end_date (fallback)
    default_start_date = (params[:date_from] || params[:start_date])&.to_date || Date.current.beginning_of_month
    default_end_date = (params[:date_to] || params[:end_date])&.to_date || (Date.current.beginning_of_month + 6.weeks)
    
    # Filter bookings by the check-in date range - this ensures we only show bookings 
    # where the first check-in date falls within the selected date range
    bookings = learner_manager.learner_bookings
                         #    .where(first_checkin: default_start_date..default_end_date)

    @dashboard_data = {
      learners: learners.as_json(
        only: [:id, :forename, :surname, :email, :programme, :type, :status, :confirmed_flag, :epa_booking, :virtual_flag, :joining_instruction_id]
      ),
      bookings: bookings.map do |booking|
        learner = booking.acc_booking_person&.rfq_learner
        programme = booking.acc_booking_header&.rfq_location&.rfq_request&.rfq_programme&.name rescue nil
        type =
          if booking.virtual_flag?
            "Virtual"
          elsif booking.epa_booking?
            "EPA"
          elsif booking.subsistence_only?
            "Subsistence"
          else
            "Residential"
          end

        {
          id: booking.id,
          learner: learner ? "#{learner.forename} #{learner.surname}" : nil,
          programme: programme,
          type: type,
          status: booking.confirmed_flag ? "Confirmed" : "Unconfirmed",
          confirmed_flag: booking.confirmed_flag,
          epa_booking: booking.epa_booking,
          virtual_flag: booking.virtual_flag,
          joining_instruction_id: find_joining_instruction_id_for_booking(booking),
          confirm_url: confirm_url_for_booking(booking.booking_token),
          vat_summary_pdf_url: "/acc_bookings/#{booking.id}/vat_summary_pdf.pdf",
          check_in_date: booking.first_checkin&.strftime('%d/%m/%Y'),
          check_out_date: booking.last_check_out&.strftime('%d/%m/%Y'),
          first_checkin: booking.first_checkin,
          last_check_out: booking.last_check_out,
          first_checkin_formatted: booking.first_checkin&.strftime('%d/%m/%Y'),
          last_checkout_formatted: booking.last_check_out&.strftime('%d/%m/%Y')
        }
      end,
      date_range: {
        start_date: default_start_date,
        end_date: default_end_date,
        from: default_start_date,
        to: default_end_date
      }
    }
  end

  # Helper to find the correct joining_instruction_id for a booking
  def find_joining_instruction_id_for_booking(booking)
    # Try direct association first
    return booking.joining_instruction_id if booking.respond_to?(:joining_instruction_id) && booking.joining_instruction_id.present?

    # Try to dig through related models (example: via rfq_task or similar)
    if booking.respond_to?(:acc_booking_header) && booking.acc_booking_header.present?
      rfq_task = booking.acc_booking_header.try(:rfq_task)
      if rfq_task && rfq_task.respond_to?(:joining_instruction_id) && rfq_task.joining_instruction_id.present?
        return rfq_task.joining_instruction_id
      end
      # If rfq_task has many joining_instructions, pick the most recent or first
      if rfq_task && rfq_task.respond_to?(:joining_instructions) && rfq_task.joining_instructions.any?
        return rfq_task.joining_instructions.order(created_at: :desc).first.id
      end
    end

    # Try via rfq_location/rfq_request/rfq_task
    if booking.respond_to?(:acc_booking_header) && booking.acc_booking_header.present?
      rfq_location = booking.acc_booking_header.try(:rfq_location)
      rfq_request = rfq_location.try(:rfq_request) if rfq_location
      rfq_task = rfq_request.try(:rfq_task) if rfq_request
      if rfq_task && rfq_task.respond_to?(:joining_instruction_id) && rfq_task.joining_instruction_id.present?
        return rfq_task.joining_instruction_id
      end
      if rfq_task && rfq_task.respond_to?(:joining_instructions) && rfq_task.joining_instructions.any?
        return rfq_task.joining_instructions.order(created_at: :desc).first.id
      end
    end

    # Fallback: nil if not found
    nil
  end

  # Helper to get the correct confirm URL for a booking
  def confirm_url_for_booking(booking)
    acc_booking_confirmation_path(id: booking)
  end
end