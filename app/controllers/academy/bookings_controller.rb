class Academy::BookingsController < AcademyBaseController

  include Pagy::Backend

  layout 'quasarify_layout'
  before_action :authenticate_client_id, only: [:show, :update_dates, :hotel_confirm_booking, :hotel_decline_booking, :cancel_booking, :resend_proforma, :resend_stripe_receipt, :resend_confirmation, :get_available_rooms]
  before_action :set_site

  def index
    @initial_filters = params[:q].present? ? params[:q] : nil
    respond_to do |format|
      format.html
      format.json do
        # Get data from method
        get_bookings
        @pagy, @bookings = pagy(@bookings_base.order(id: :desc), items: 20)
      end
    end
  end

  def show
    @booking = Booking.find(params[:id])
    @booking_payments = @booking.booking_payments.order(created_at: :desc)
    @booking_attendees = @booking.booking_attendees.joins(:rfq_response_room).select("booking_attendees.*, CONCAT(rfq_response_rooms.room_type, ' - ', rfq_response_rooms.pkg_type) as room_name").order(:surname, :forename)
    @rfq_location = @booking.rfq_location
    @rfq_request = @rfq_location.rfq_request
    @rfq_programme = @rfq_request.rfq_programme
    @hotel = @booking.hotel
  end

  def update_dates
    begin
      @booking = Booking.find(params[:id])
      raise "Booking not found" if @booking.blank?
      raise "Check-in date not provided" if params[:check_in].blank?
      raise "Check-out date not provided" if params[:check_out].blank?
       if @booking.update(check_in: params[:check_in], check_out: params[:check_out])
        AcademyHubMailer.delay.send_booking_confirmation(@booking.id, true)
        render json: {success: true, message: "Booking dates updated successfully"}
      else
        render json: {error: @booking.errors.full_messages.join(', ')}, status: 422 and return
       end
    rescue => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def populate_filters
    programmes = RfqProgramme.where(id: getAllowedProgrammeIds())
    hotels = Hotel.distinct.joins(rfq_proposed_hotels: {rfq_response: {rfq_location: :rfq_request}}).where('rfq_requests.rfq_programme_id in (?) and rfq_responses.accepted_at is not null', programmes.pluck(:id))
    render json: { programmes: programmes.select(:id, :name).order(:name), hotels: hotels.select(:id, :name).order(:name) }
  end

  def export
    backdrop = setup_export
    Backdrop.delay.booking_export(backdrop.id, current_user.id)
    current_user.update_column(:backdrop_creating, true) #so can display a file for download on any page
    backdrop.reload

    render json: {backdrop_id: backdrop.id, progress: backdrop.progress}
  end

  def export_hotel_invoice_data
    backdrop = setup_export
    Backdrop.delay.export_hotel_invoice_data(backdrop.id, current_user.id)
    current_user.update_column(:backdrop_creating, true) #so can display a csv file for download on any page
    backdrop.reload

    render json: {backdrop_id: backdrop.id, progress: backdrop.progress}
  end

  def poll_for_export
    backdrop = Backdrop.find(params[:id])

    if backdrop.present?
      if backdrop.present? && backdrop.progress.to_i >= 100
         current_user.update_column(:backdrop_creating, false)
         render json: {
            error: "Sorry could not find the file, it may be out of date and you should try again to create it",
            status: 400
        }, status: 400 and return
      end

      if backdrop.file.present?
        backdrop.update_column(:progress, 100)
      else
        backdrop.update_column(:progress, (backdrop.progress.to_i + 1))
      end
    end

    url = ""
    file_name = ""

    if backdrop.file.present?
      file_name = backdrop.file&.name
      url = backdrop.file&.remote_url(expires: 10.minutes.from_now)
    end

    render json: {progress: backdrop.progress, fileUrl: url, fileName:file_name}
  end

  def hotel_confirm_booking
    begin
      @booking = Booking.find(params[:id])
      @payments = @booking.booking_payments.pending
      raise "Reservation number not provided" if params[:reservation_number].blank?
      @payments.each do |payment|
        Stripe::PaymentIntent.capture(payment.stripe_charge_id,{}, {api_key: ENV['STRIPE_ACADEMY_SECRET']}) if payment.stripe_charge_id.present?
      end
      if @booking.booking_attendees.update_all(
        confirmed_at: DateTime.now,
        confirmed_by_id: current_user.id,
        hotel_confirmed_at: DateTime.now,
        hotel_confirmed_by_id: current_user.id
      ) && @booking.update(
        confirmed_at: DateTime.now,
        confirmed_by_id: current_user.id,
        hotel_confirmed_at: DateTime.now,
        hotel_confirmed_by_id: current_user.id,
        reservation_number: params[:reservation_number]
      )
        @booking.booking_attendees.each_with_index do |attendee, index| 
          attendee.update(reservation_number: (params[:reservation_number] + "/" + (index + 1).to_s)) if attendee.reservation_number.blank?
          attendee.save
        end
        AcademyHubMailer.delay.send_stripe_receipt(@booking.id) if @payment.present? && @payment.stripe_charge_id.present? && @booking.payment_method == 'Card'
        AcademyHubMailer.delay.send_hotel_proforma(@booking.id) if @booking.payment_method == 'BACS / Credit Card (Paid to Hotel)'
        AcademyHubMailer.delay.send_booking_confirmation(@booking.id)
        render json: {success: true, message: "Booking confirmed successfully"}
      else
        render json: {error: @booking.errors.full_messages.join(', ')}, status: 422 and return
      end
    rescue Stripe::CardError => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def hotel_decline_booking
    begin
      @booking = Booking.find(params[:id])
      raise "Booking not found" if @booking.blank?
      raise "Booking already confirmed" if @booking.confirmed_at.present?
      raise "Booking already cancelled" if @booking.cancelled_at.present?
      raise "Booking already declined" if @booking.hotel_declined_at.present?
      raise "Decline reason not provided" if params[:hotel_decline_reason].blank?
      cancel_booking()
      @booking.update(
          hotel_declined_at: DateTime.now,
          hotel_declined_by_id: current_user.id,
          hotel_decline_reason: params[:hotel_decline_reason]
        )
      @booking.booking_attendees.update_all(
        hotel_declined_at: DateTime.now,
        hotel_declined_by_id: current_user.id
      )
      AcademyHubMailer.delay.send_booking_declined(@booking.id)
    rescue Stripe::CardError => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def cancel_booking
    begin 
      @booking = Booking.find(params[:id]) if @booking.blank?
      @payment = @booking&.booking_payments&.first if @payment.blank?
      if @booking.confirmed_at.present?
        if @booking.eligible_for_refund? && @payment.present?
          # amount = (@payment.amount - AccBooking.new.extract_stripe_plus_vat_from_cost(@payment.amount))
          AcademyStripeCardPayment.new.refund_all_payments(@booking)
        end
      else #if payment isn't finished yet
        if @payment.present? && @payment.stripe_charge_id.present?
          Stripe::PaymentIntent.cancel(@payment.stripe_charge_id, {}, {api_key: ENV['STRIPE_ACADEMY_SECRET']})
        end
      end
      reason = params[:cancellation_reason].present? ? params[:cancellation_reason] : "Cancelled by hotel"
      if @booking.booking_attendees.update_all(
        cancelled_at: DateTime.now,
        cancelled_by_id: current_user.id
      ) && @booking.update(
        cancelled_at: DateTime.now,
        cancelled_by_id: current_user.id,
        cancellation_reason: reason
      )
        AcademyHubMailer.delay.send_booking_cancelled(@booking.id)
        render json: {success: true, message: "Booking cancelled successfully"}
      else
        render json: {error: @booking.errors.full_messages.join(', ')}, status: 422 and return
      end
    rescue Stripe::CardError => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def resend_proforma
    begin
      @booking = Booking.find(params[:id])
      raise "Booking not found" if @booking.blank?

      AcademyHubMailer.delay.send_hotel_proforma(@booking.id)
      render json: {success: true, message: "Proforma email sent successfully"}
    rescue => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def resend_stripe_receipt
    begin
      @booking = Booking.find(params[:id])
      raise "Booking not found" if @booking.blank?
      raise "Booking payment not found" if @booking.booking_payments.blank?

      AcademyHubMailer.delay.send_stripe_receipt(@booking.id)
      render json: {success: true, message: "Stripe receipt email sent successfully"}
    rescue => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def resend_confirmation
    begin
      @booking = Booking.find(params[:id])
      raise "Booking not found" if @booking.blank?
      raise "Booking already cancelled" if @booking.cancelled_at.present?

      AcademyHubMailer.delay.send_booking_confirmation(@booking.id)
      render json: {success: true, message: "Confirmation email sent successfully"}
    rescue => e
      render json: {error: e.message}, status: 422 and return
    end
  end

  def get_available_rooms
    begin
      @booking = Booking.find(params[:id])
      raise "Booking not found" if @booking.blank?
      rooms = @booking.get_accepted_response.get_academy_rooms_for_select
      render json: {rooms: rooms}
    rescue => e
      render json: {error: e.message}, status: 422 and return
    end
  end


  private


  def setup_export
    booking_params = params.except(:controller, :action).permit!

    booking_params = booking_params.to_h

    if booking_params.empty?
      booking_params = { :checkin_start => (Date.today - 1.month).to_s, :checkin_end => Date.today.to_s}
    end

    if current_user.is_a_supplier?
      # This would be the supplier user id and isn't needed for the export
      booking_params.delete(:client_id)
    end

    backdrop = Backdrop.create(:query => booking_params, :user => current_user)

    backdrop
  end

  def get_bookings
    @bookings_base = Booking.distinct.
    joins(rfq_location: {:rfq_request => :rfq_programme}).
    left_joins(:hotel).
    left_joins(:booking_payments)
    # Secures bookings data for non-admin users
    unless current_user.is_an_administrator?
      if current_user.is_a_client?
        allowed_programmes = getAllowedProgrammeIds()
        @bookings_base = @bookings_base.where('rfq_programmes.id IN (?)', allowed_programmes)
        @bookings_base = @bookings_base.where('rfq_programmes.client_id = ?', current_user.contact.parent_id)
      end

      if current_user.is_a_supplier?
        @possible_hotel_ids = @current_user.all_hotels.collect { |x| x.id }
        @bookings_base = @bookings_base.where('bookings.hotel_id IN (?)', @possible_hotel_ids)
      end
    end

    # filtering
    filters = params[:filters]
    if filters.present?
      @bookings_base = @bookings_base.where('bookings.id = ?', filters[:booking_id]) if filters[:booking_id].present?
      @bookings_base = @bookings_base.where('bookings.reservation_number = ?', filters[:reservation_number]) if filters[:reservation_number].present?
      @bookings_base = @bookings_base.where('bookings.booker_surname ILIKE ?', "%#{filters[:booker_surname]}%") if filters[:booker_surname].present?
      @bookings_base = @bookings_base.joins(:booking_attendees).where('booking_attendees.surname ILIKE ?', "%#{filters[:attendee_surname]}%").distinct if filters[:attendee_surname].present?
      @bookings_base = @bookings_base.joins(rfq_location: :rfq_request).where('rfq_requests.rfq_programme_id = ?', filters[:programme_id]) if filters[:programme_id].present?
      @bookings_base = @bookings_base.where('bookings.hotel_id = ?', filters[:hotel_id]) if filters[:hotel_id].present?
      if filters[:status].present?
        case filters[:status]
        when 'confirmed'
          @bookings_base = @bookings_base.where('bookings.hotel_confirmed_at IS NOT NULL AND bookings.cancelled_at IS NULL')
        when 'declined'
          @bookings_base = @bookings_base.where('bookings.hotel_declined_at IS NOT NULL')
        when 'cancelled'
          @bookings_base = @bookings_base.where('bookings.cancelled_at IS NOT NULL')
        when 'unconfirmed'
          @bookings_base = @bookings_base.where('bookings.hotel_confirmed_at IS NULL AND bookings.hotel_declined_at IS NULL AND bookings.cancelled_at IS NULL')
        end
      end
      if filters[:check_in].present? && filters[:check_out].present?
        @bookings_base = @bookings_base.where('bookings.check_in >= ? AND bookings.check_out <= ?', filters[:check_in], filters[:check_out])
        # @bookings_base = @bookings_base.where('check_in BETWEEN ? AND ?', filters[:check_in], filters[:check_out])
      elsif filters[:check_in].present? && filters[:check_out].blank?
        @bookings_base = @bookings_base.where('bookings.check_in >= ?', filters[:check_in]) if filters[:check_in].present?
      elsif filters[:check_in].blank? && filters[:check_out].present?
        @bookings_base = @bookings_base.where('bookings.check_out <= ?', filters[:check_out]) if filters[:check_out].present?
      end
    end
    @bookings_base
  end

  def getAllowedProgrammeIds()
    if current_user.is_a_client?
      RfqProgramme.allowed_programmes_for(current_user.contact, nil, 'Adult')
    elsif current_user.is_a_supplier?
      hotel_ids = current_user.all_hotels.pluck(:id)
      RfqProgramme.distinct.joins(rfq_requests: {rfq_responses: :rfq_proposed_hotel}).where("rfq_proposed_hotels.hotel_id IN (?) and rfq_responses.accepted_at is not null", hotel_ids).pluck(:id)
    elsif current_user.is_an_administrator?
      RfqProgramme.where("rfq_type = 'Adult'").pluck(:id)
    end
  end

  def check_access
    unless user_signed_in?
      flash[:error] = "Sorry No access to Bookings"
      redirect_to "/"
      return false
    end

    # if current_user.is_a_client? && !current_user.app_enabled?
    #   flash[:error] = "Sorry No access"
    #   redirect_to "/"
    #   return false
    # end
  end

  def authenticate_client_id
    @booking = Booking.find_by(id: params[:id])
    if @booking.blank?
      render json: {error: "Booking not found"}, status: 404 and return
    end

    if current_user.is_a_client? && @booking.client_id != current_user.contact.parent_id
      redirect_to request.referer || "/"
      flash[:error] = "You do not have access to this booking"
      # render json: {error: "You do not have access to this booking"}, status: 403 and return
    end

    if current_user.is_a_supplier? && current_user.all_hotels.pluck(:id).exclude?(@booking.hotel_id)
      redirect_to request.referer || "/"
      flash[:error] = "You do not have access to this booking"
    end
  end

end
