class Academy::<PERSON>ing<PERSON>ttendeesController < AccBookingsBaseController
  layout 'quasarify_layout'
  before_action :set_site

  def update
    begin
      attendee = BookingAttendee.find(params[:id])
      if attendee.update(safe_params)
        render json: { message: "Booking attendee updated successfully." }, status: :ok
      else
        render json: { error: attendee.errors.full_messages.join(", ") }, status: :unprocessable_entity
      end
    rescue => error
      render json: { error: error.message }, status: :unprocessable_entity
    end
  end

  def cancel_attendee
    begin 
      attendee = BookingAttendee.find(params[:id])
      if attendee.update(cancelled_at: DateTime.now, cancelled_by_id: current_user.id)
        render json: { message: "Booking attendee cancelled successfully." }, status: :ok
      else
        render json: { error: attendee.errors.full_messages.join(", ") }, status: :unprocessable_entity
      end
    rescue => error
      render json: { error: error.message }, status: :unprocessable_entity
    end
  end

  def safe_params
    params.require(:booking_attendee).permit(:forename, :surname, :email, :telephone, :rfq_response_room_id, :company_name)
  end

  def check_access
    unless user_signed_in?
      flash[:error] = "Sorry No access to Book<PERSON>"
      redirect_to "/"
      return false
    end

    if current_user.is_a_client? && !current_user.app_enabled?
      flash[:error] = "Sorry No access"
      redirect_to "/"
      return false
    end
  end

  def set_site
    @hub = 'academy'
  end

end
