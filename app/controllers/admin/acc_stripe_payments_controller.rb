class Admin::AccStripePaymentsController < Admin::ApplicationController

  def api_admin_refund
      get_booking
      # convert inputted value to pence
      params[:refundAmount] = ((params[:refundAmount].to_f) *100).to_i
      # find original charge
      originalCharge = @booking.booking_payments.where(stripe_charge_id: params[:acc_stripe_payment_id])
      if params[:refundAmount] <= 0
        render json: {
          error: "Could not refund",
          status: 400
        }
      end
      begin
        # amount: optional, default is entire charge
        refund = Stripe::Refund.create({
          charge: params[:acc_stripe_payment_id],
          amount: params[:refundAmount]
        })

      rescue StandardError => e
         message = e.message
         render json: {
            error: "Could not refund",
            status: 400
          }
        return
      end

      payment = @booking.booking_payments.create(
        amount: -(params[:refundAmount]),
        :refund_reason =>  params[:refundReason],
        stripe_refund_id: refund.id,
        :cp_id => originalCharge.first.id,
        status: 1,
        :company_reg_number =>  @booking&.hotel&.company_reg_number,
        :company_vat_number =>  @booking&.hotel&.company_vat_number,
        :company_reg_address => @booking&.hotel&.company_reg_address,
        :stripe_connect_payment => StripeCardPayment.new.use_connected_account(booking) != {}
      )
      # @booking.assign_attributes(:refund_requested => false, :refunded => false, :total_cost_manager => new_cost)
      @booking.save(:validate => false)

      paymentReturnDetails = {
        id: payment.id,
        created_at: payment.created_at,
        status: payment.status,
        stripe_refund_id: payment.stripe_refund_id,
        amount: payment.amount
      }
      render :json => paymentReturnDetails
    # end
  end

  def refund_booking
    return if !@current_user.is_a_servace_accountant?
    booking = AccBooking.find(params[:booking_id])
    payment = booking.booking_payments.where(stripe_charge_id: params[:stripe_charge_id]).first
    pence_amount = params[:refund_amount].to_f.to_pence # convert to pence
    begin
      StripeCardPayment.new.delay.refund_payment(payment, booking, pence_amount, params[:refund_reason])
    rescue => e
      render json: {message: e.message}, status: 500
    end
  end

  def stripe_user_id(booking)
    booking.hotel.stripe_detail.stripe_user_id
  end

  def get_booking
    @booking = AccBooking.find(params[:bookingId])
  end
end
