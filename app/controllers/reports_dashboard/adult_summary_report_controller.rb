class ReportsDashboard::AdultSummaryReportController < AcademyBaseController
  layout 'quasarify_layout'

  before_action :check_access, only: [:index, :report_data]

  def index
  end

  def report_data
    # Initialize the presenter with the required parameters
    @start_date = Time.zone.parse(params[:startDate]).to_date
    @end_date = Time.zone.parse(params[:endDate]).to_date
    dashboard = AdultDashboard.new(@start_date, @end_date, current_user)
    
    # Fetch and group the bookings data using the presenter
    bookings_data = dashboard.get_bookings
    hotels = bookings_data.joins(:hotel).select('hotels.id as id, hotels.name as name').uniq

    bookings_data = bookings_data.where(hotel_id: params[:hotelIds]) if params[:hotelIds].present?
    bookings_data = dashboard.filter_by_dates(bookings_data)
    grouped_data = dashboard.group_by_programme(bookings_data)
    
    overallTotalCost = grouped_data.sum(&:total_cost)
    overallTotalNights = grouped_data.sum(&:total_nights)
    overallAverageRoomCost = if overallTotalCost == 0 || overallTotalNights == 0
      0
    else
      overallTotalCost / overallTotalNights
    end

    render json: {grouped_data: grouped_data, overall_total_cost: overallTotalCost, overall_total_nights: overallTotalNights, overall_average_room_cost: overallAverageRoomCost, hotels: hotels}

  end

  private

  def check_access
    unless current_user.is_an_administrator?
      @no_programmes = RfqProgramme.allowed_programmes_for(current_user.contact, current_user.organisation).size == 0
      redirect_to root_path, alert: 'Access denied' if @no_programmes
    end
  end
end