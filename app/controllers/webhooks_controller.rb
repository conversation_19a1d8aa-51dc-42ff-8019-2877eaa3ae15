class WebhooksController < ApplicationController
  skip_before_action :authenticate_user!
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_basically

  before_action :authentice_apprenticestop_event, only: [:receive]


  def receive
    event = nil
    begin
      payload = request.body.read
      parsed_payload = JSON.parse(payload)
      if parsed_payload['data'].blank? || parsed_payload['data']['object'].blank? || parsed_payload['data']['object']['metadata'].blank?
        render json: { message: "If this payload is not in the correct format, this event is not for us" }, status: 200 and return
      end
      booking_type = parsed_payload['data']['object']['metadata']['booking_type']
      # switching webhook keys is done here to allow for multiple stripe accounts''
      secret_key = if isApprenticeStop?(booking_type)
        if payload['account'].present? 
          ENV['STRIPE_WEBHOOK_SECRET_CONNECT']
        else
          ENV['STRIPE_WEBHOOK_SECRET']
        end
      elsif booking_type == 'academy_hub'
        ENV['STRIPE_WEBHOOK_SECRET_ACADEMY']
      else
        render json: { message: "Event ignored because key was not found" }, status: 200 and return
      end
      event = Stripe::Webhook.construct_event(payload, request.env['HTTP_STRIPE_SIGNATURE'], secret_key.to_s)
    rescue JSON::ParserError => e
      # Invalid payload
      status 400
      return
    rescue Stripe::SignatureVerificationError => e
      # Invalid signature
      status 400
      return
    end

    handle_event(event)

  end

  private

  def handle_event(event)
    begin
      case event.type
      when 'payment_intent.created'
        handle_payment_created(event.data.object)
      when 'payment_intent.processing'
        handle_payment_processing(event.data.object)
      when 'payment_intent.succeeded'
        handle_payment_succeeded(event.data.object)
      when 'payment_intent.payment_failed'
        handle_payment_failed(event.data.object)
      when 'payment_intent.amount_capturable_updated'
        handle_payment_amount_capturable_updated(event.data.object)
      when 'charge.updated'
        handle_charge_updated(event.data.object)
      else
        # raise "Unhandled event type: #{event.type}"
      end
      render json: { message: 'Webhook received successfully' }, status: :ok

    rescue StandardError => e
      Rollbar.error(e, 'Webhook Apprentice Error')
      render json: { error: e.message }, status: :unprocessable_entity and return
    end
  end

  def handle_payment_created(payment_intent)

  end

  def handle_payment_processing(payment_intent)
    booking_id = payment_intent.metadata.try(:booking_id)
    # Ensures only bookings are processed
    return true if booking_id.blank?

    booking = AccBooking.find(booking_id)

    connected_account_data = StripeCardPayment.new.use_connected_account(booking)
    payment = booking.booking_payments.where(stripe_charge_id: payment_intent.id)
    payment = booking.booking_payments.build(stripe_charge_id: payment_intent.id) if payment.blank?
    payment_method_details = Stripe::PaymentMethod.retrieve(payment_intent.payment_method, connected_account_data)
    # balance_transaction = Stripe::BalanceTransaction.retrieve(Stripe::Charge.retrieve(payment_intent.latest_charge, connected_account_data).balance_transaction, connected_account_data)
    payment.update(
      :amount => payment_intent.amount.to_f,
      :hotel_id => (booking.subsistence_only? ? nil : booking.hotel_id ),
      :status => StripePayment.statuses[:pending],
      :company_reg_number => booking.hotel&.company_reg_number,
      :company_vat_number => booking.hotel&.company_vat_number,
      :company_reg_address => booking.hotel&.company_reg_address,
      :payment_type => StripePayment.payment_types[payment_intent.metadata.payment_type.to_sym],
      :stripe_connect_payment => connected_account_data != {},
      :payment_method_id => payment_method_details.id,
      # :stripe_fee => balance_transaction.fee,
      # :servace_stripe_fee => booking.extract_stripe_from_cost(payment_intent.amount)
    )
  end

  def handle_payment_succeeded(payment_intent)
    booking_id = payment_intent.metadata.try(:booking_id)
    booking_type = payment_intent.metadata.try(:booking_type)
    # Ensures only bookings are processed
    return true if booking_id.blank?
    if isApprenticeStop?(booking_type)
      booking = AccBooking.find(booking_id)
      connected_account_data = StripeCardPayment.new.use_connected_account(booking)
    elsif booking_type == 'academy_hub'
      booking = Booking.find(booking_id)
      connected_account_data = {api_key: ENV['STRIPE_ACADEMY_SECRET']}
    end
    payment = booking.booking_payments.where(stripe_charge_id: payment_intent.id).first
    payment = booking.booking_payments.build(stripe_charge_id: payment_intent.id) if payment.blank?
    payment_method_details = Stripe::PaymentMethod.retrieve(payment_intent.payment_method, connected_account_data)
    balance_transaction_fee = payment.getBalanceTransaction(payment_intent, connected_account_data)&.fee || 0

    # Keep this for future reference
    # bacs_reference = if payment_intent.metadata.payment_type == 'bacs_debit'
    #                     charge = Stripe::Charge.retrieve({ id: payment_intent.latest_charge, expand: ['payment_method_details.bacs_debit']}, connected_account_data).payment_method_details&.bacs_debit
    #                     mandate = Stripe::Mandate.retrieve({ id: charge.mandate, expand: ['payment_method_details.bacs_debit']}, connected_account_data)
    #                     mandate.payment_method_details&.bacs_debit&.reference
    #                   end
    # payment.bacs_debit_reference = bacs_reference if bacs_reference.present?
    payment.update(
      :amount => payment_intent.amount.to_f,
      :hotel_id => booking&.hotel_id,
      :status => StripePayment.statuses[:paid],
      :company_reg_number => booking.hotel&.company_reg_number,
      :company_vat_number => booking.hotel&.company_vat_number,
      :company_reg_address => booking.hotel&.company_reg_address,
      :payment_type => StripePayment.payment_types[payment_intent.metadata.payment_type.to_sym],
      :stripe_connect_payment => (isApprenticeStop?(booking_type) && connected_account_data != {}),
      :payment_method_id => payment_method_details.id,
      :stripe_fee => balance_transaction_fee,
      :servace_stripe_fee => AccBooking.new.extract_stripe_plus_vat_from_cost(payment_intent.amount)
    )

    if isApprenticeStop?(booking_type)
      if !booking.confirmed_flag
        booking.update(
          stripe_fee_rate_additional: StripeFee.last.stripe_add,
          stripe_fee_rate_percentage: StripeFee.last.stripe_rate,
          payment_method: payment_method_details.type,
          confirmed_flag: true,
          confirmed_at: Time.zone.now,
          confirmed_by: Stripe::Customer.retrieve({id: payment_intent.customer}, connected_account_data).name
        )

        booking.set_servace_fees!
        AccBookingMailer.delay.send_booking_confirmation(booking.id) unless booking.cancelled?
      end
    
      payment = nil if connected_account_data.empty? # clear the payment data so it send a full summary email if payment to servace
      AccBookingMailer.delay.booking_payment_receipt(booking.id, payment&.id, booking.confirmed_by)
    elsif booking_type == 'academy_hub'
      AcademyHubMailer.delay.send_booking_confirmation(booking.id) unless booking.cancelled?
    end

  end

  def handle_payment_failed(payment_intent)
    booking_id = payment_intent.metadata.try(:booking_id)
    # Ensures only bookings are processed
    return true if booking_id.blank?

    booking = AccBooking.find(booking_id)

    if !booking.confirmed_flag && booking.booking_payments.any?
      failed_reason = payment_intent.last_payment_error.present? ? payment_intent.last_payment_error.message : 'Unknown'
      booking.booking_payments.first.update(
        status: StripePayment.statuses[:failed],
        failed_reason: failed_reason
      )
      AccBookingMailer.delay.send_payment_failed(booking.id)
    end
    # TODO maybe send an email to the purchaser to let them know the payment failed? (But no need if it is card payment)
  end

  def handle_payment_amount_capturable_updated(payment_intent)
    begin
      booking = Booking.find(payment_intent.metadata.booking_id)
      # connected_account_data = StripeCardPayment.new.use_connected_account(booking)
      payment = booking.booking_payments.where(stripe_charge_id: payment_intent.id).first
      payment = booking.booking_payments.build(stripe_charge_id: payment_intent.id) if payment.blank?
      payment.update(
        :amount => payment_intent.amount.to_f,
        :hotel_id => (booking.hotel_id.present? ? booking.hotel_id : nil),
        :status => StripePayment.statuses[:pending],
        :company_reg_number => booking.hotel&.company_reg_number,
        :company_vat_number => booking.hotel&.company_vat_number,
        :company_reg_address => booking.hotel&.company_reg_address,
        :stripe_connect_payment => false,
        :payment_method_id => payment_intent.payment_method,
        # :payment_type => StripePayment.payment_types[payment_intent.metadata.payment_type.to_sym],
        # :stripe_fee => balance_transaction_fee,
        :servace_stripe_fee => booking.calculate_stripe_with_vat(payment_intent.amount)
      )
      AcademyHubMailer.delay.send_hotel_booking_request(booking.id) unless booking.cancelled_at.present?
    rescue StandardError => e
      Rollbar.error(e, 'Webhook Apprentice Error')
      Rails.logger.error("Error handling payment amount capturable updated: #{e.message}")
    end
  end

  def handle_charge_updated(charge)
    payment = StripePayment.find_by(stripe_charge_id: charge.payment_intent)
    booking_id = charge.metadata.try(:booking_id)
    booking_type = charge.metadata.try(:booking_type)
    if isApprenticeStop?(booking_type)
      booking = AccBooking.find(booking_id)
      connected_account_data = StripeCardPayment.new.use_connected_account(booking)
    elsif booking_type == 'academy_hub'
      booking = Booking.find(booking_id)
      connected_account_data = {api_key: ENV['STRIPE_ACADEMY_SECRET']}
    end
    unless payment
      Rails.logger.warn "No booking payment found for charge #{charge.id} with payment_intent #{charge.payment_intent}"
      return
    end
    balance_transaction_fee = Stripe::BalanceTransaction.retrieve(charge.balance_transaction, connected_account_data)&.fee || 0
    payment.update(stripe_fee: balance_transaction_fee) if balance_transaction_fee.present?
  end

  def authentice_apprenticestop_event
    # Authenticate the webhook
    payload = JSON.parse(request.body.read, symbolize_names: true)
    event = payload[:data][:object]
    # if it is production and stripe agrees, or it's not production so we don't care
    acceptable = (Rails.env.production? && payload[:livemode]) || !Rails.env.production?
    is_charge_or_customer = ['payment_intent', 'charge', 'customer'].any? { |item| event[:object].include?(item) }

    acceptable_booking_type_keys = ['apprentice_stop', 'Apprenticestop', 'academy_hub']
    # only check for metadata if it is a charge or payment_intent
    if !is_charge_or_customer || !acceptable
      if (event[:metadata].blank? || event[:metadata][:booking_type].blank? || acceptable_booking_type_keys.exclude?(event[:metadata][:booking_type]))
        render json: { message: "Event ignored because not relevant to this app" }, status: 200 and return
      end
    end
  end

  def isApprenticeStop?(booking_type)
    acceptable_booking_type_keys = ['apprentice_stop', 'Apprenticestop']
    acceptable_booking_type_keys.include?(booking_type)
  end

end
