class StripeController < ApplicationController
  skip_before_action :authenticate_user!, :only => :get_stripe_details

  # include ActionController::Live
  Mime::Type.register "text/event-stream", :stream

  # Create a manage Stripe account for yourself.
  # Only works on the currently logged in user.
  # See app/services/stripe_managed.rb for details.
  # def managed
  #   connector = StripeManaged.new( current_user )
  #   account = connector.create_account!(
  #       params[:country], params[:tos] == 'on', request.remote_ip
  #   )
  #
  #   if account
  #     flash[:notice] = "Managed Stripe account created! <a target='_blank' rel='platform-account' href='https://dashboard.stripe.com/test/applications/users/#{account.id}'>View in dashboard &raquo;</a>"
  #   else
  #     flash[:error] = "Unable to create Stripe account!"
  #   end
  #   redirect_to user_path( current_user )
  # end

  # Create a standalone Stripe account for yourself.
  # Only works on the currently logged in user.
  # See app/services/stripe_unmanaged.rb for details.
  def standalone
    connector = StripeStandalone.new(current_user)
    account = connector.create_account!(params[:country] || 'GB')

    if account
      @stripe_message = "Standalone Stripe account created! <a target='_blank' rel='platform-account' href='https://dashboard.stripe.com/test/applications/users/#{account.id}'>View in dashboard &raquo;</a>"
    else
      @stripe_message = "Unable to create Stripe account!"
    end
  end


  # Connect yourself to a Stripe account.
  # Only works on the currently logged in user.
  # See app/services/stripe_oauth.rb for #oauth_url details.
  def oauth
    connector = StripeOauth.new(current_user)
    url, error = connector.oauth_url(redirect_uri: stripe_confirm_url)

    if url.nil?
      flash[:error] = error
      redirect_to stripe_connected_path
    else
      redirect_to url
    end
  end

  # Confirm a connection to a Stripe account.
  # Only works on the currently logged in user.
  # See app/services/stripe_connect.rb for #verify! details.
  def confirm
    confirm_inner

    redirect_url = root_url + "supplier/acc_stripe"

    redirect_to redirect_url
  end

  # Deauthorize the application from accessing
  # the connected Stripe account.
  # Only works on the currently logged in user.
  def deauthorize
    # TODO change redirect routes

    connector = StripeOauth.new(current_user)
    if connector.deauthorize
      hotel = current_user.contact.parent
      HgMailer.delay.notify_hotel_stripe_disconnected(hotel.id, hotel.stripe_live.pluck(:id))
      StripeDisconnectedJob.perform_later(hotel.id)
    else
       flash[:error] = "Account could not be disconnected from Stripe. (It may be disconnected already)"
    end

    redirect_to root_url + "supplier/acc_stripe"
  end

  def stripe_rates
    if current_user && (current_user.is_an_administrator? || current_user.is_a_supplier?)
      connect = params[:connect] == "true"
      fee = connect ? StripeFee.connect_fee : StripeFee.servace_fee
      render json: fee.to_json
     else
      render json: {status: 400}
    end
  end


  # used for Academy hub only currently
  def get_stripe_details
    amount = (params[:amount]).to_f
    amount =  (amount + AccBooking.new.calculate_stripe_with_vat(amount)).to_pence.to_i
    stripe_options = {
      currency: 'GBP',
      capture_method: 'manual',
      amount: amount,
      automatic_payment_methods: {enabled: true, allow_redirects: :never},
      customer: AcademyStripeCardPayment.new.get_customer_id(params[:booking_id]),
      setup_future_usage: 'off_session',
      metadata: {
        payment_type: 'card',
        booking_id: params[:booking_id],
        booking_type: 'academy_hub' # academy_hub
      }
    }
    payment_intent = Stripe::PaymentIntent.create(stripe_options, {api_key: ENV['STRIPE_ACADEMY_SECRET']})
    render json: {public_key: ENV['STRIPE_ACADEMY_PUB'], payment_intent: payment_intent}
  end

  # def stripe_checkout
  #   session = Stripe::Checkout::Session.create({
  #     line_items: [{
  #       # Provide the exact Price ID (e.g. pr_1234) of the product you want to sell
  #       price_data.currency: 'gbp',
  #       price: 100,
  #       quantity: 1,
  #     }],
  #     mode: 'payment',
  #     success_url: 'www.google.com',
  #     cancel_url: 'www.google.com',
  #   })
  #   redirect session.url, 303
  # end

  private

  def confirm_inner
    connector = StripeOauth.new(current_user)
    var = if params[:code]
            # If we got a 'code' parameter. Then the
            # connection was completed by the user.
            connector.verify!(params[:code])

          elsif params[:error]
            # If we have an 'error' parameter, it's because the
            # user denied the connection request. Other errors
            # are handled at #oauth_url generation time.
            flash[:error] = "Authorization request denied."
          end
    var
  end

end
