class AccBookingsController < AccBookingsBaseController

  before_action :load_booking, :build_booking_dates, :except => [:index, :new, :live_programmes, :bulk_confirm, :find_from_email, :get_rfqs, :bulk_create_bookings, :send_monthly_vat_summaries_to_managers, :refresh_bookings_results]
  before_action :cant_edit_cancelled, :only => [:edit, :update, :confirm_booking]
  before_action :admin_only, :only => [:resolve_booking, :find_from_email]

  include Pagy::Backend

  def index
    if current_user.is_an_administrator?
      audience = :hg
    else
      audience = :client
    end

    @tags_selected = params[:acc_booking_search][:tags]&.reject(&:empty?) if params[:acc_booking_search].present?

    if params[:acc_booking_search].blank? || params[:acc_booking_search].empty? || params[:acc_booking_search].values.all? { |v| v.blank? || v == "0" || v == "" || v == "All"}
      params[:acc_booking_search] = { :checkin_start => (Date.today - 1.month).to_s, :checkin_end => Date.today.to_s}
    end

    x = pre_process_dates
    if x == false
      redirect_to acc_bookings_path and return
    end


    if params[:commit] == "Export to Excel"
      @backdrop = Backdrop.create(:query => params[:acc_booking_search], :user => current_user)
      Backdrop.delay.booking_export(@backdrop.id, current_user.id)
      current_user.update_column(:backdrop_creating, true) #so can display a file for download on any page
      @backdrop.reload
    end


    @booking_search = AccBookingSearch.new(params[:acc_booking_search])
    searcher = AccBookingsSearchAndExport.new(@booking_search, audience, current_user)
    base = searcher.scoped

    @possible_room_types = searcher.possible_rooms
    @possible_business_units = []
    @possible_business_units = searcher.possible_business_units if @booking_search.client_eq.present?
    @possible_programmes = searcher.possible_programmes
    @possible_groups = searcher.possible_groups
    @possible_hotels = searcher.possible_hotels
    @all_live_programmes = searcher.live_programmes
    @possible_tags = searcher.possible_tags


    # TODO this block is only for new, so once we switch, it can be removed
    if current_user.is_an_administrator?
      @orgs_with_live_bookings = searcher.live_orgs
      @curr_org = @orgs_with_live_bookings.first
      live_base = RfqLocation.joins(:rfq_request => { :rfq_programme => :client }).
        includes(:rfq_request => { :rfq_programme => :client }, :accepted_response => { :rfq_proposed_hotel => :hotel }).
        where("rfq_requests.lost_at is null").
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_locations.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice")
      if @curr_org.present?
        @live_programmes = live_base.where("organisations.id = ? ", @curr_org.id).distinct.order(:id)
        @live_programmes = @live_programmes.map { |loc| [loc.rfq_title, loc.id] }
      else
        @live_programmes = []
      end

      @live_virtual = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_requests.mode = ?", 'Virtual').
        where("rfq_requests.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice")

      @live_virtual = @live_virtual.map { |loc| [loc.rfq_title, loc.id] }
    else
      if @possible_programmes.nil? || @possible_programmes.empty?
        flash[:notice] = "Sorry, you do not have any programmes to see so you cannot view any bookings"
        redirect_to rfq_client_dashboard_path and return
      end

      pp_ids = @possible_programmes.pluck(:id)

      @live_programmes = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_locations.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice").
        where("rfq_programmes.id in (?)", pp_ids).
        map { |loc| [loc.rfq_title, loc.id] }

      @live_virtual = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_requests.mode = ?", 'Virtual').
        where("rfq_requests.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice").
        where("rfq_programmes.id in (?)", pp_ids).
        map { |loc| [loc.rfq_title, loc.id] }

    end

      # if current_user.is_an_administrator?
      #   @refunds = AccBooking.joins(:acc_booking_person, :booking_payments).
      #   where(acc_bookings: {refunded: false}, acc_booking_people: { person_type: 'LEA' }, booking_payments: {stripe_connect_payment: false, stripe_refund_id: nil})

      #   @refunds = @refunds.where(refund_required: true).or(@refunds.where(refund_requested: true))
      # end

    respond_to do |format|
      format.html {
        @pagy, @bookings = pagy(base, items: 25)
        @hotels = Hotel.where("id in (?)", @bookings.collect { |x| x.hotel_id }.uniq).includes(:primary_location, :reservations_contact)
      }
      format.js {
        @bookings = base
        @group_code = params[:acc_booking_search][:group_code]
      }
    end
  end

  def new
    # TODO temp fix for new bookings until new booking flow is in place
    if current_user.is_an_administrator?
      @orgs_with_live_bookings = Organisation.joins(:rfq_programmes => { :rfq_requests => :rfq_locations }).
      where("rfq_programmes.rfq_type = ?", "Apprentice").
      where("rfq_requests.lost_at is null").
      where("rfq_requests.end_date >= ?", Date.today).
      where("(rfq_locations.released_at is not null or rfq_requests.released_at is not null)").
      distinct.order("name ASC")

      @curr_org = @orgs_with_live_bookings.first
      live_base = RfqLocation.joins(:rfq_request => { :rfq_programme => :client }).
        includes(:rfq_request => { :rfq_programme => :client }, :accepted_response => { :rfq_proposed_hotel => :hotel }).
        where("rfq_requests.lost_at is null").
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_locations.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice")
      if @curr_org.present?
        @live_programmes = live_base.where("organisations.id = ? ", @curr_org.id).distinct.order(:id)
        @live_programmes = @live_programmes.map { |loc| [loc.rfq_title, loc.id] }
      else
        @live_programmes = []
      end

      @live_virtual = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_requests.mode = ?", 'Virtual').
        where("rfq_requests.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice")

      @live_virtual = @live_virtual.map { |loc| [loc.rfq_title, loc.id] }
    else
      requests = RfqRequest.joins(:rfq_locations).where("(rfq_locations.released_at is not null or  rfq_requests.released_at is not null)").where(:rfq_programme_id => RfqProgramme.allowed_programmes_for(@current_user.contact, nil, "Apprentice"))
      @possible_programmes = RfqProgramme.where(:id => requests.map(&:rfq_programme_id).uniq)
      @possible_programmes.order("name ASC")

      pp_ids = @possible_programmes.pluck(:id)

      @live_programmes = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_locations.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice").
        where("rfq_programmes.id in (?)", pp_ids).
        map { |loc| [loc.rfq_title, loc.id] }

      @live_virtual = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_requests.end_date >= ?", Date.today).
        where("rfq_requests.mode = ?", 'Virtual').
        where("rfq_requests.released_at is not null").
        where("rfq_programmes.rfq_type = ?", "Apprentice").
        where("rfq_programmes.id in (?)", pp_ids).
        map { |loc| [loc.rfq_title, loc.id] }
    end
  end

  def live_programmes
    #return false unless current_user.is_an_administrator?
    if current_user.is_a_client?
      @organisation = current_user.contact.parent
      @contact_id = current_user.contact.id
    elsif params[:acc_booking].present? && params[:acc_booking][:organisation_id].present?
      @organisation = Organisation.find params[:acc_booking][:organisation_id]
    elsif params[:organisation_id].present?
      @organisation = Organisation.find params[:organisation_id]
    end

    if params[:programme_id].present?
      if @organisation.present?
        @programme = RfqProgramme.where(:id => @organisation.live_rfq_locations(params[:year]).pluck("rfq_programmes.id").uniq).
          find(params[:programme_id])
      elsif current_user.is_an_administrator?
        @programme = RfqProgramme.find(params[:programme_id])
        @organisation = @programme.client if @programme.present?
      end
    end

    if params[:hotel_id].present?
      @selected_hotel = Hotel.find(params[:hotel_id])
    end

    if params[:acc_booking].present?
      @for_creating = true
    else
      @for_creating = false
    end
    if params[:year].present?
      yr = params[:year].to_i
    else
      yr = Date.today.year
    end

    if @organisation.present? && @programme.present?
      @live_progs = @organisation.live_programmes(params[:year], @contact_id)
      @live_locs = @organisation.live_rfq_locations(params[:year]).where(rfq_programmes: {id: @programme.id})
      @live_vms = @organisation.live_virtual_locations(params[:year])
      @group_codes = [[]] + @programme.group_codes_for_select
      @bus_units = @programme.rfq_business_units.order(:name)
    elsif @organisation.present?
      @live_progs = @organisation.live_programmes(params[:year], @contact_id)
      @live_locs = @organisation.live_rfq_locations(params[:year]).where(rfq_programmes: {id: @organisation.live_programmes(yr, @contact_id).map(&:id)})
      @live_vms = @organisation.live_virtual_locations(params[:year])
      @group_codes = [[]] + @organisation.group_codes_for_select
      @bus_units = @organisation.rfq_business_units.order(:name)
    else

      @live_progs = RfqProgramme.joins(:rfq_requests => :rfq_locations).
        where(rfq_programmes: {rfq_type: 'Apprentice'}).
        where("(rfq_locations.released_at is not null or rfq_requests.released_at is not null)").
        where("date_part('year', rfq_requests.end_date) >= :yr and date_part('year',rfq_requests.start_date) <= :yr", :yr => yr)
      @live_locs = RfqLocation.joins(:rfq_request => :rfq_programme).
        where("rfq_locations.released_at is not null").
        where("date_part('year', rfq_requests.end_date) >= :yr and date_part('year',rfq_requests.start_date) <= :yr", :yr => yr).
        where("rfq_requests.lost_at is null")
      @group_codes = [[]]
      @bus_units = [[]]
    end

    @possible_tags = if @organisation.present?
      booking_search = AccBookingSearch.new({client_eq: @organisation.try(:id)})
      searcher = AccBookingsSearchAndExport.new(booking_search, :hg, current_user)
      searcher.possible_tags
    else
      []
    end


  # This is close to working, but returns fewer results than the original query, need to find out why
  if @live_locs.present?
    accepted_response_ids = @live_locs.map(&:accepted_response_id)
    @hotels = Hotel.joins(rfq_proposed_hotels: :rfq_response).where(rfq_responses: {id: accepted_response_ids}).select('hotels.id')
  end
    # This is the old rubbish performing code, it's been replaced by a nice db query above
    # if @live_locs.present?
    #   hotel_ids = @live_locs.map { |l| l.accepted_responses.map { |ph| ph.hotel } }.flatten.uniq.map(&:id)
    #   @hotels = Hotel.where(id: hotel_ids)
    # end

    respond_to do |format|
      format.js {}
    end
  end

  def show
    if @booking.acc_booking_header.multi_modules_selected
      redirect_to "#{bookings_virtual_multi_module_index_path}/#bookingdetails/#{@booking.id}"
      return
    end
    @using_connect = @booking.acc_booking_header.rfq_location.rfq_request.use_stripe_connect
    @has_credit_account = RfqBuAccount.where(rfq_response_id: @booking.acc_booking_header.rfq_location.accepted_response.id, rfq_business_unit_id: @booking.acc_booking_person&.get_business_unit&.id).any?

    @booking_cost = if @booking.booking_payments.any?
      @booking.total_paid
    elsif @using_connect
      @booking.total_cost_manager
    else
      @booking.room_rate_plus_fees
    end
  end

  def show_manager

  end

  def edit
    @booking_dates = if @booking.subsistence_only
      @booking.training_dates
    else
      @booking.booking_dates
    end
    @confirming = false
    @initial_amount_of_nights = @booking.total_nights
    if (!@booking.acc_booking_header.rfq_location.rfq_request.card_pay && !@booking.card_pay?) || @booking.acc_booking_person.get_business_unit&.override_card_pay
      case @booking.acc_booking_person.person_type
      when "TRA"
        @payment_types = @booking.possible_train_payment_types
        @possible_rooms = []
        @rfq_response.rfq_response_rooms.avail_to_adults.each do |r|
          @possible_rooms << [r.name_for_select, r.id]
        end
      when "AAB"
        @payment_types = AccBooking.adult_booking_pay_types(@booking.acc_booking_header.rfq_location.rfq_request)
      else
        @booking.reset_confirmation_answers! unless @booking.confirmed_at.present?
        @payment_types = AccBooking::PAYMENT_TYPES

        @has_credit_account = RfqBuAccount.where(rfq_response_id: @booking.acc_booking_header.rfq_location.accepted_response.id, rfq_business_unit_id: @booking.acc_booking_person.get_person.rfq_business_unit.id).any?
        @payment_types << "Credit Account" if @has_credit_account

        @payment_type = @payments_type | [@booking.payment_method] if @booking_payment_method.present?
      end
    end
  end

  def confirm_booking
    if params[:pay_via_credit].present?
      @booking.payment_method = "credit account"
      if !@booking.booking_payments.any? #to stop reloads creating extra payments
        @booking.booking_payments.build(amount: @booking.room_rate_plus_fees(false) * 100, payment_type: 2, status: 0)
        @booking.confirmed_at = Time.now
        @booking.confirmed_by = current_user.name
        @booking.save
        AccBookingMailer.delay.send_booking_confirmation(@booking.id)
      end
      render :show and return
    end
    if @booking.acc_booking_person.person_type == "TRA"
      @payment_types = @booking.possible_train_payment_types
      @possible_rooms = @rfq_response.rfq_response_rooms.avail_to_adults
    else
      @booking.reset_confirmation_answers! unless @booking.confirmed_at.present?
      @payment_types = AccBooking::PAYMENT_TYPES
    end
    @confirming = true
    if @booking.acc_booking_person.person_type == "LEA"
      @booking.special_requirements = @booking.acc_booking_person.rfq_learner.special_requirements if @booking.special_requirements.blank?
      @booking.rooming_preference = @booking.acc_booking_person.rfq_learner.rooming_preference if @booking.rooming_preference.blank?
    end
    if !@booking.has_hotel_confirmed_if_required
      @confirming = false
      @booking.errors.add(:base, "Cannot confirm booking until hotel has confirmed room availability")
    end
    reload_booking_dates
    render :edit and return
  end

  def pre_update_payment_confirmation
    payment = StripeCardPayment.new
    @use_connected_account = (!payment.use_connected_account(@booking).empty?).to_s
    @intent = payment.create_payment_intent(@booking, @amount, currency) if @intent.blank?
  end

  def update
    begin
      revert_hotel_confirmation = false #only for trainer bookings
      @booking.set_before_change_as_text
      @booking.single_reason_req = true
      check_conf_answers!

      # Is this wanting to start as only client 
      if (@booking.hotel_confirmed_at.present? && @booking.late_booking?) && @current_user.is_a_client?
        if params[:acc_booking][:subsistence_only] == "0"
          if @booking.acc_booking_person.person_type == "LEA"
            if params[:acc_booking][:rfq_response_room_id].to_i != @booking.rfq_response_room_id
              @booking.errors.add(:base, "Room cannot be amended - as the hotel has confirmed. Please contact ServAce if you wish to amend")
            end
          elsif @booking.acc_booking_person.person_type == "TRA"
            #  if this is nil it's still valid for the comparison
            room_name = RfqResponseRoom.find_by(id: params[:acc_booking][:rfq_response_room_id]).try(:room_type)
            if params[:acc_booking][:complimentary] == "1"
              room_name = "Double for Sole"
            end
            # if db booking values aren't the same as incoming param values mark as amended
            param_complimentary = nil
            if params[:acc_booking][:complimentary] == "0"
              param_complimentary = false
            elsif params[:acc_booking][:complimentary] == "1"
              param_complimentary = true
            end
            if @booking.complimentary != param_complimentary || @booking.room_type != room_name
              revert_hotel_confirmation = true
            end
          end
        end
      end

      if @booking.errors.any?
        reload_booking_dates
        render :edit and return
      end

      if params[:acc_booking][:subsistence_only] == "1"
        @booking.hotel_id = nil
      end

      if !@booking.card_pay? && !@booking.confirmed? && params[:confirming].present?
        @booking.confirmed_flag = true
        @booking.confirmed_at = Time.zone.now
        @booking.confirmed_by = maybe_masquerader.contact.full_name
      end

      if @booking.acc_booking_person.person_type == "TRA"
        @booking.validate_trainer_info = true
        check_for_clashes(@booking)
        if @clashes.present?
          @payment_types = @booking.possible_train_payment_types
          @possible_rooms = @rfq_response.rfq_response_rooms.avail_to_adults
          reload_booking_dates
          render :edit and return
        end
      end

      # the validation for this is currently disabled so we might as well just comment the call out
      # check_for_duplicate_bookings()

      @booking_cost_before_save = @booking.room_rate_plus_fees(false)
      @using_connect = @booking&.booking_payments&.first&.stripe_connect_payment
      if @booking.update!(safe_params)
        @booking.reset_stays(true)
        @booking.reload
        amount = calculate_amount()
        if amount.positive? && @booking.card_pay? && @booking.booking_payments.any?
          if @booking.booking_payments.charged.first.payment_type == StripePayment.payment_types[:credit_account]
            pence_amount = amount.to_pence
            @booking.booking_payments.build(amount: pence_amount, payment_type: 2, status: 0)
            @booking.save
          else
            currency = @booking.acc_booking_header.rfq_location.accepted_response.currency.code
            StripeCardPayment.new.additional_booking_charge(@booking, amount, currency)
          end
        elsif amount.negative?
          if @booking.card_pay? && @booking.booking_payments.any? && @booking.eligible_for_refund?
            if @booking.booking_payments.charged.first.payment_type == StripePayment.payment_types[:credit_account]
              @booking.booking_payments.build(amount: amount.to_pence, payment_type: 2, status: 1)
              @booking.save
            else
              # get the closest payment to the refund amount, we need to use -amount because amount is negative
              pence_amount = -amount.to_pence
              payment = @booking.booking_payments.charged.where('amount > ?', pence_amount ).order(:amount).first
              StripeCardPayment.new.refund_payment(payment, @booking, pence_amount, 'Booking: ' + @booking.id.to_s + ' edited')
            end
            # or do manual stuff
          elsif @using_connect
            @booking.update(:refund_requested => true)
          end
        end

        if @booking.acc_booking_person.person_type == "TRA" && revert_hotel_confirmation
          @booking.mark_as_amended_by_contact(current_user.contact, true)
        else
          @booking.mark_as_amended_by_contact(current_user.contact, false)
        end
        AccBookingMailer.delay.booking_amended(@booking.id)
        flash[:notice] = "Updated Booking"
        if @booking.confirmed? && !@booking.has_been_amended? #if amended then the mark as amended sends the confirmation
          if params[:commit] == "Update Booking And Confirm"
            if @booking.acc_booking_person.person_type == 'LEA' || @booking.acc_booking_person.person_type == 'AAB'
              AccBookingMailer.delay.send_booking_confirmation(@booking.id)
              AccBookingMailer.delay.send_ji_to_learner(@booking.id) if @booking.can_send_ji_to_lrnr?
            elsif @booking.acc_booking_person.person_type == 'TRA'
              AccBookingMailer.delay.send_hotel_trainer_booking(@booking.id, "amendment")
              AccBookingMailer.delay.send_trainer_booking(@booking.id, "amendment", :guest)
              AccBookingMailer.delay.send_trainer_booking(@booking.id, "amendment", :booker)
            end
          end
        end
        redirect_to acc_booking_path(@booking) and return
      else
        if (!@booking.acc_booking_header.rfq_location.rfq_request.card_pay && !@booking.card_pay?) || @booking.acc_booking_person.get_person.rfq_business_unit.override_card_pay
          if @booking.acc_booking_person.person_type == "TRA"
            @payment_types = @booking.possible_train_payment_types
            @possible_rooms = @rfq_response.rfq_response_rooms.avail_to_adults
          else
            @payment_types = AccBooking::PAYMENT_TYPES
            @payment_type = @payments_type | [@booking.payment_method] if @booking_payment_method.present?
          end
        end
        @confirming = params[:confirming]
        @booking.clear_confirming!
        reload_booking_dates
        render :edit and return
      end
    rescue => error
      reload_booking_dates
      flash[:error] = error&.message
      render :edit and return
    end
  end

  def audits
    raise "You do not have access to this page" if !current_user.is_an_administrator?
    @audits = @booking.audits.reverse
  end

  def update_rfq_trainer
    @booking_header = AccBookingHeader.find(params[:id])
    trainer_id = params[:acc_booking_header][:rfq_trainer_id]
    ActiveRecord::Base.transaction do
      @booking_header.update(:rfq_trainer_id => trainer_id)
      @booking_header.acc_bookings.each do |booking|
        booking.update(:rfq_trainer_id => trainer_id)
      end
    end
    render json: { success: true }
  end

  def cancel_booking_form
    if @booking.under_adjustment?
      @msg = "Sorry, you cannot cancel this booking at this time - there is a Non Arrival to process or it is Early Arrival. Please visit the Non / Early Arrivals."
    end

    unless @booking.subsistence_only?
      if current_user.is_an_administrator?
        if (@booking.first_checkin + 5.days) < Date.today
          @msg = "Sorry, you cannot cancel this booking, it is already underway"
        end
      else
        if @booking.first_checkin < Date.today
          @msg = "Sorry, you cannot cancel this booking, it is already underway"
        end
      end
    end
  end

  def cancel_booking
    # 1 == true in this context
    if params[:acc_booking][:cancellation_wish] == "1"
      if !params[:acc_booking][:cancellation_reason].empty?
        reason = params[:acc_booking][:cancellation_reason_other].present? ? params[:acc_booking][:cancellation_reason_other] : params[:acc_booking][:cancellation_reason]
        @using_connect = @booking.acc_booking_header.rfq_location.rfq_request.use_stripe_connect
        # see js view for save
        @booking.cancellation_wish = true
        @booking.cancellation_reason = reason
        @booking.cancelled_by = maybe_masquerader.contact.full_name
        @booking.cancelled_at = Time.zone.now
        if @booking.acc_booking_person.person_type == "TRA"
          @booking.hotel_confirmed_at = nil
        end
        if @booking.save
          refundable = @booking.eligible_for_refund? || params[:acc_booking][:refund_override] == "true"
          if @booking.card_pay? && @booking.booking_payments.any? && refundable
            if @booking&.booking_payments&.charged&.first&.payment_type == StripePayment.payment_types[:credit_account]
              @booking.booking_payments.build(amount: -@booking.total_paid * 100, payment_type: 2, status: 1)
              @booking.save
            else
              StripeCardPayment.new.refund_all_payments(@booking)
            end
          elsif @using_connect
            @booking.update(:refund_requested => true);
          end
          @booking.update(total_cost_manager: 0)
          AccBookingMailer.delay.cancel_booking_hotel_card_pay(@booking.id) if @booking.can_hotel_recieve_cancellation_email?
        end
      else
        @booking.errors.add(:cancellation_reason, "Please ensure you have entered a reason for cancellation.")
      end
    else
      @booking.errors.add(:cancellation_wish, "Please ensure you have confirmed the cancellation.")
    end
    if @booking.acc_booking_person.person_type == "LEA"
      NotificationSender.send_notification(title: 'Course updates', content: "Your booking on #{@booking.first_checkin.strftime("%A the #{@booking.first_checkin.day.ordinalize} of %B")} has been cancelled", users: [@booking.acc_booking_person.rfq_learner.id], urgent: false, booking: @booking.id)
    end

    respond_to do |format|
      format.html { redirect_to acc_booking_path(@booking) }
      format.js { }
    end
  end

  def undo_booking_confirmation
    @booking.update(confirmed_flag: false, confirmed_at: nil, confirmed_by: nil)
    redirect_back(fallback_location: root_path)
  end

  def undo_booking_cancellation
    @booking.cancellation_wish = false
    @booking.cancellation_reason = nil
    @booking.cancelled_by = nil
    @booking.cancelled_at = nil
    @booking.save
    redirect_back(fallback_location: root_path)
  end

  def bulk_confirm
    allowed_programmes = RfqProgramme.allowed_programmes_for(current_user.contact)
    @bookings = AccBooking.joins(:acc_booking_header => { :rfq_location => { :rfq_request => :rfq_programme } }).
      where("rfq_programmes.id in (?)", allowed_programmes).where("acc_bookings.id in (?)", params[:group_code][:booking_ids].split(","))
    @bookings = AccBooking.bulk_confirm!(@bookings, current_user)

    @group_code = params[:group_code][:code]
  end

  def spec_req
    respond_to do |format|
      format.js {}
    end
  end

  def spec_req_save
    @booking.update(safe_params)
    @booking.reload
    respond_to do |format|
      format.js {}
    end
  end

  def find_from_email
    params[:find_by] ||= {}

    @found = { :learners => [], :trainers => [], :cs => [] }

    base = AccBooking.joins(:acc_booking_header).order("acc_bookings.id desc")
    if params[:commit].present?
      @em = params[:find_by][:email].try(:downcase)
      flash.now[:error] = "Sorry email is blank - cannot search, please input an email" and return if (@em.blank? && params[:commit].present?)
      @found[:learners] = base.joins(:acc_booking_person => :rfq_learner).where("lower(rfq_learners.email) =  :email or
              lower(rfq_learners.manager_email) = :email", :email => @em).to_a
      @found[:trainers] = base.joins(:acc_booking_person => :rfq_trainer).where("lower(rfq_trainers.email) = :email", :email => @em).to_a
      @found[:cs] = base.joins(:acc_booking_person).where("acc_booking_people.person_type in (?)", ["AAB", "AGB", "PGB"]).
        where("lower(acc_booking_people.email) = :email or lower(acc_booking_people.booker_email) = :email ", :email => @em).to_a
      flash.now[:notice] = "Sorry no-one found with that email" if @found[:learners].size == 0 && @found[:trainers].size == 0 && @found[:cs].size == 0
    end
  end

  def resolve_booking
    if @booking.hotel_declined_at.present? && @booking.resolved_at.blank?
      if params[:resolution_note].present?
        note = Note.new
        note.noteable_id = @booking.id
        note.noteable_type = "AccBooking"
        note.note_subject = Note.note_subjects[:BookingResolved]
        note.note_text = params[:resolution_note]
        note.user_id = current_user.id
        note.user_type = Note.user_types[:Admin]
        note.save
      end
      @booking.resolve!(current_user)
    end
    if @booking.acc_booking_header.conference.present?
      respond_to do |format|
        format.json {}
        format.html {
        flash[:notice] = "Hotel declined conference booking has now been resolved!"
        redirect_to admin_conference_group_bookings_path(@booking.acc_booking_header.conference)
        }
      end
    else
      respond_to do |format|
        format.json {}
        format.html {
          flash[:notice] = "Hotel declined trainer booking has now been resolved!"
          redirect_to acc_booking_path(@booking)
        }
      end

    end
  end

  def send_ji_now
    if @booking.can_send_ji?
      flash[:info] = "JI Queued to be sent. Timestamp will be updated once the email has been sent."
      AccBookingMailer.delay.send_joining_instructions(@booking.id)
      audit = @booking.audits.new
      audit.comment = "Joining Instructions manually sent by #{@current_user.name}"
      audit.user = maybe_masquerader
      audit.save
      #NB mailer method creates the chase record as can be called from other places
      redirect_to acc_booking_path(@booking)
    else
      flash[:error] = @booking.errors.full_messages.to_sentence
      redirect_to acc_booking_path(@booking)
    end
  end

  def send_conf_now
    if @booking.confirmed? || @booking.cancelled?
      flash[:info] = "Confirmation Queued to be sent. Timestamp will be updated once the email has been sent."
      if @booking.acc_booking_person.person_type == 'LEA' || @booking.acc_booking_person.person_type == 'AAB'
        AccBookingMailer.delay.send_booking_confirmation(@booking.id)
        if @booking.booking_payments.any?
          payment_id = @booking&.booking_payments&.first&.stripe_connect_payment ? @booking&.booking_payments&.charged.first.stripe_charge_id : nil
          AccBookingMailer.delay.booking_payment_receipt(@booking.id, payment_id)
        end

        # This was asked to be removed on 05/02/21
        #AccBookingMailer.delay.send_ji_to_learner(@booking.id) if @booking.can_send_ji_to_lrnr?
      elsif @booking.acc_booking_person.person_type == 'TRA'
        AccBookingMailer.delay.send_trainer_booking(@booking.id, 'confirmation', :guest)
        AccBookingMailer.delay.send_trainer_booking(@booking.id, 'confirmation', :booker)
      end
      redirect_to acc_booking_path(@booking)
    else
      flash[:error] = "Cant send Confirmation as booking has not been confirmed/cancelled"
      redirect_to acc_booking_path(@booking)
    end
  end

  def load_booking
    unless current_user.can_access_booking?(params[:id])
      render json: { error: "Sorry, you do not have permission to access this booking" }, status: 403
      return
    end

    @booking = AccBooking.find(params[:id])

    unless @booking.acc_booking_header.conference.present? || @booking.virtual_flag?
      @rfq_location = @booking.acc_booking_header.rfq_location
      @rfq_response = @rfq_location.accepted_response
      @possible_rooms = @rfq_response.rooms_for_select if @rfq_response.present?
      @programme = @rfq_location.rfq_request.rfq_programme
    end
    if @booking.virtual_flag?
      @rfq_location = @booking.acc_booking_header.rfq_location
      @programme = @rfq_location.rfq_request.rfq_programme
    end
  end

  def cant_edit_cancelled
    if @booking.cancelled?
      flash[:errror] = "Sorry, that booking has been cancelled"
      redirect_to acc_booking_path(@booking)
    end
  end

  def check_conf_answers!
    return unless (params[:confirming] || params[:acc_booking][:cancellation_wish] == "0" || @booking.confirmed?)
    if params[:acc_booking] && params[:acc_booking][:rfq_confirmation_answers_attributes].present?
      params[:acc_booking][:rfq_confirmation_answers_attributes].each do |_, v|
        ans = @booking.rfq_confirmation_answers.find v[:id]
        if ans.try(:rfq_confirmation_question).try(:mandatory?) ||
          (ans.rfq_confirmation_question.blank? && ans.mandatory?)
          @booking.errors.add(:base, "#{ans.question} must be completed.") if v[:answer].blank?
        end
      end
    end
  end

  def pre_process_dates
    st_date = params[:acc_booking_search][:checkin_start]
    nd_date = params[:acc_booking_search][:checkin_end]

    if st_date.present?
      begin
        params[:acc_booking_search][:checkin_start] = Date.parse(st_date).to_s
      rescue
        flash[:error] = "Sorry unable to recognise date for checkin start: #{st_date}, please use 'day-month3letters-fullyear' e.g. '20-jul-2015' if the date picker is not working"
        return false
      end
    end
    if nd_date.present?
      begin
        params[:acc_booking_search][:checkin_end] = Date.parse(nd_date).to_s
      rescue
        flash[:error] = "Sorry unable to recognise date for checkin end: #{nd_date}, please use 'day-month3letters-fullyear' e.g. '20-jul-2015' if the date picker is not working"
        return false
      end
    end
  end

  def resend_stripe_receipt
    ActiveRecord::Base.transaction do
      booking = AccBooking.find(params[:id])
      booking_payment_id = params[:booking_payment_id]
      if params[:vat].present?
        if (Date.today > booking.first_checkin.to_date.end_of_month) || params[:bypass_date].present?
          AccBookingMailer.delay.vat_summary(booking.acc_booking_person.rfq_learner.manager_email, [booking.id])
          render json: { success: true}
        else
          raise "Sorry, you can only send the VAT summary after the end of the month"
        end
      else
        AccBookingMailer.delay.booking_payment_receipt(booking.id, booking_payment_id, current_user.name)
        render json: { success: true}
      end

    end
  rescue => error
    render json: { error: error.message }, status: 400
  end

  def calculate_amount()
    amount = 0
    new_amount = @booking.room_rate_plus_fees(false)
    amount = (new_amount - @booking_cost_before_save)
    if amount != 0 && amount.positive? && !@using_connect
      amount += @booking.calculate_stripe_with_vat(amount)
    end
    return amount
  end

  def confirm_booking_for_hotel
    begin
      booking = AccBooking.find(params[:id])
      if booking.hotel_declined_at.present?
        flash[:notice] = 'Sorry the trainer booking has been declined and therefore cannot be confirmed. Please contact ServAce if you wish to now progress this booking' and return
      else
        booking.hotel_confirm(@current_user)
        if booking.errors.any?
          flash[:error] = booking.errors.full_messages.to_sentence
          redirect_to acc_booking_path(booking) and return
        end
        flash[:notice] = "You have successfully confirmed the booking"
        redirect_to acc_booking_path(booking)
      end
    rescue => error
      flash[:error] = error.message
      redirect_to acc_booking_path(booking)
    end
	end

  def mark_as_refunded
    booking = AccBooking.find(params[:id])
    booking.refund_completed
    respond_to do |format|
      format.html {
        redirect_back(fallback_location: root_path)
      }
      format.json {
        render json: { success: true}
      }
    end
  end

  def get_payments_json
    @stripe_payments = @booking.booking_payments.order(created_at: :desc)
    if @stripe_payments
      render json: {payments: @stripe_payments.as_json(:include => :customer_payments), booking: @booking.to_json(methods: :get_block_days_count)}
    else
      render json: {status: 'failed'}, status: 400
    end
  end

  def get_rfqs
    rfqs = RfqRequest.joins(:rfq_locations).where.not(name: nil).
                      where.not(rfq_locations: {released_at: nil}).select(:id, :name)
    render json: {rfqs: rfqs}
  end

  def bulk_create_bookings
    booking_headers = []
    rfq = RfqRequest.find(params[:form][:rfq])
    params[:form][:numberToCreate].times do
      booking_headers << AccBookingHeader.new(
        rfq_location_id: rfq.rfq_locations.first.id,
        booker_id: current_user.id,
        confirm_token: rand(36**6).to_s(36),
        payment_method: rfq.payment_method,
        non_block_mode: false,
        joining_instruction_id: rfq.rfq_tasks.where(code: 'join_instr').first,
        ji_type: 0
      )
    end
    AccBookingHeader.insert_all(booking_headers)
  end

  def send_payment_failed_email()
    AccBookingMailer.delay.send_payment_failed(params[:id])
  end

  def send_monthly_vat_summaries_to_managers
    AccBooking.new.send_monthly_vat_summaries_to_managers()
    redirect_back(fallback_location: root_path)
    flash[:alert] = "VAT summaries have been sent to all managers"
  end

  def auto_pay_booking_for_tests
    @booking.auto_pay_booking_for_tests
    redirect_back(fallback_location: root_path)
  end

  def refresh_bookings_results
    unless Rails.env.production?
			BookingsResult.refresh
		end
    redirect_back(fallback_location: root_path)
  end

  private

  def check_for_duplicate_bookings
    my_stays = @booking.calculate_stays

    potential_dupes = AccBookingStay.joins(:acc_booking => :acc_booking_person).
      where("acc_booking_people.rfq_learner_id = ?", @booking.acc_booking_person.rfq_learner_id).
      where.not("acc_booking_stays.acc_booking_id = ?", @booking.id).
      where("acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null")

    duplicate_ids = []

    my_stays.each do |stay|
      duplicate_ids = duplicate_ids + potential_dupes.overlaps(stay[0], stay[1]).pluck("acc_bookings.id")
    end

    if duplicate_ids.any?
      # @booking.errors.add(:base, "Overlaps with existing booking(s) #{duplicate_ids.to_sentence}")
      # return true
    end

    false
  end

  def reload_booking_dates
    @booking_dates = if @booking.subsistence_only
      @booking.training_dates
    else
      @booking.booking_dates
    end
  end

  def build_booking_dates
    @booking.build_booking_dates
  end

  def safe_params
    params.require(:acc_booking).permit(:subsistence_only, :rfq_response_room_id, :single_reason, :payment_method,
                                        :special_requirements, :rooming_preference, :hg_note, :client_note, :single_as_twin, :confirming, :parking_required, :first_checkin, :last_check_out,
                                        rfq_confirmation_answers_attributes: [:id, :answer, :rfq_confirmation_question_id, :_destroy],
                                        :booking_dates_attributes => [:id, :'_destroy', :start_date, :sun, :sun_datetime, :mon, :mon_datetime,
                                        :tue, :tue_datetime, :wed, :wed_datetime, :thu, :thu_datetime, :fri, :fri_datetime, :sat, :sat_datetime, :week_type])
  end

end
