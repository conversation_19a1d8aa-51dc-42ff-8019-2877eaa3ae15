class StripeReconciliationReportsController < AccBookingsBaseController
  # before_action :set_site  
  layout 'quasarify_layout'
  def index
    respond_to(&:html)
  end

  def generate_data
    months_payment_data = get_stripe_report_months();
    months_payment_data =  months_payment_data.group(("DATE_TRUNC('month', report_date)"))
    months_payment_data = months_payment_data.select("MIN(id) as id, MAX(report_date) as date,
            sum(total_paid) as total_paid,
            sum(total_subsistence_fees) as total_subsistence_fees, 
            sum(total_room_cost) as total_room_cost, 
            sum(total_servace_commission) as total_servace_commission, 
            sum(total_stripe_fees) as total_stripe_fees, 
            sum(total_servace_stripe_fees) as total_servace_stripe_fees, 
            sum(total_due_hotel) as total_due_hotel,
            sum(total_stripe_discrepancy) as total_stripe_discrepancy"
          ).order('date')
    render json: { months_payment_data: months_payment_data }
  end

  def get_filter_data
    hotels_for_select = StripeReportMonth.distinct.select('hotel_id as id, hotel_name').order('hotel_name')
    clients_for_select = StripeReportBookingData.distinct.select('organisation_id as id, organisation_name').order('organisation_name')
    render json: { hotels_for_select: hotels_for_select, clients_for_select: clients_for_select }
  end

  def get_hotel_monthly_data
    monthly_hotels = get_stripe_report_months()
    monthly_hotels = monthly_hotels.where(hotel_id: params[:filters][:selected_hotels].values) if params[:filters][:selected_hotels].present?
    render json: { hotels: monthly_hotels }
  end

  def get_month_payment_data
    payments = StripeReportBookingData.where(stripe_report_month_id: params[:stripe_report_month_id])
    render json: {payments: payments}
  end

  def reconcile_payments
    StripeReportBookingData.where(stripe_report_month_id: params[:stripe_report_month_id]).update_all(reconciled: true)
  end

  def invoice_payments
    @bookings = StripeReportMonth.find(params[:stripe_report_month_id]).acc_bookings
    if params[:recipient] == 'hotel'
      send_data StripeReconciliationMonthlyInvoicePdf.new(@bookings, @bookings.first.hotel).print_data.render,
                filename: 'months_summary.pdf', type: 'application/pdf', disposition: :attachment
    elsif params[:recipient] == 'client'
      begin
        bookings_by_client = @bookings.group_by do |booking|
          booking.acc_booking_header.rfq_location.rfq_request.rfq_programme.client
        end
        filename = 'client_zip.zip'
        temp_file = Tempfile.new(filename)
        Zip::OutputStream.open(temp_file) { |zos| }
        Zip::File.open(temp_file.path, Zip::File::CREATE) do |zip|
          bookings_by_client.each_with_index do |client_bookings, _index|
            name = "CLIENT_#{client_bookings[0].name}_#{@bookings.first.first_checkin.strftime('%B')}"
            temp_pdf = Tempfile.new("#{name}.pdf")
            temp_pdf.binmode
            begin
              pdf_content = StripeReconciliationMonthlyInvoicePdf.new(client_bookings[1], client_bookings[0]).print_data.render
              temp_pdf.write pdf_content
              temp_pdf.flush
              zip.add("#{name}.pdf", temp_pdf.path)
            ensure
              temp_pdf.close
            end
          end
        end
        zip_data = File.read(temp_file.path)
        send_data(zip_data, type: 'application/zip', filename: filename)
      ensure
        temp_file.close
        temp_file.unlink
      end
    end
  end

  def download_csv
    # bookings = StripeReport.where(month: params[:filter_params][:selected_months].values).collect(&:acc_bookings).flatten
    report_months = get_stripe_report_months();
    booking_report_data = StripeReportBookingData.where(stripe_report_month_id: report_months.pluck(:id))
    csv_headers = 'Booking ID, Hotel Name, Client Name, Learner Name, Checkin Date, Checkout Date, Room Cost, Stripe Fees, Servace Commission, Servace Stripe Fees, Subsistence Fees, Total Paid, Due to Hotel, Stripe Discrepancy'
    csv_data = []
    booking_report_data.each do |booking|
      row = []
      row << booking.acc_booking_id
      row << booking&.hotel&.name&.tr(',', ' ') || 'Subsistence Only'
      row << booking.organisation.name.tr(',', ' ')
      row << booking.acc_booking.acc_booking_person.get_person.full_name_nice
      row << booking.acc_booking.first_checkin
      row << booking.acc_booking.last_check_out
      row << booking.acc_booking.calculate_room_cost
      row << booking.total_stripe_fees.to_f / 100
      row << booking.total_servace_commission.to_f / 100
      row << booking.total_servace_stripe_fees.to_f / 100
      row << booking.total_subsistence_fees.to_f / 100
      row << booking.total_paid.to_f / 100
      row << booking.total_due_hotel.to_f / 100
      row << booking.total_stripe_discrepancy.to_f / 100
      csv_data << row
    end
    render json: { csvHeaders: csv_headers, csvData: csv_data }
  end

  def credit_account_sql_query 
    bookings = BookingsResult.joins(:booking_payments).where(book_payment_method: 'credit account', subsistence_only: false).
    group(:id, :hotel_id, :hotel_name, :client_name, :first_checkin, :last_check_out, :l_forename, :l_surname, :credit_account_payment_method).
    select("bookings_results.id, bookings_results.hotel_id, bookings_results.hotel_name,
      sum(stripe_payments.amount / 100) AS credit_amount,
      bookings_results.client_name, CONCAT(l_forename,' ',l_surname) as learner_name,
      bookings_results.first_checkin, bookings_results.last_check_out, bookings_results.credit_account_payment_method as payment_method")
    if params[:filters].present?
      bookings = bookings.where('extract(year from last_check_out) = ? and extract(month from last_check_out) in (?)', params[:filters][:year], params[:filters][:selected_months].values)
      bookings = bookings.where(hotel_id: params[:filters][:selected_hotels].values) if params[:filters][:selected_hotels].present?
      bookings = bookings.where(client_id: params[:filters][:selected_clients].values) if params[:filters][:selected_clients].present?
    end
    return bookings
  end

  def credit_report_data
    credit_account_bookings_by_hotel = []
    bookings = credit_account_sql_query
    bookings.group_by(&:hotel_id).each do |id, bookings|
      credit_account_bookings_by_hotel << {key: id, hotel_name: bookings.first.hotel_name, bookings: bookings, credit_amount:  bookings.sum(&:credit_amount)}
    end
    render json: { credit_account_bookings: credit_account_bookings_by_hotel }
  end

  def credit_report_download_csv
    bookings = credit_account_sql_query
    csv_headers = 'Booking ID, Hotel Name, Credit Amount, Client Name, Learner Name, First Checkin, Last Check Out', 'Payment Method'
    csv_data = []
    bookings.each do |booking|
      row = []
      row << booking.id
      row << booking&.hotel_name&.tr(',', ' ') || 'Subsistence Only'
      row << booking.credit_amount.to_f
      row << booking.client_name.tr(',', ' ')
      row << booking.learner_name
      row << booking.first_checkin
      row << booking.last_check_out
      row << booking&.payment_method&.humanize || 'Not Set'
      csv_data << row
    end
    render json: { csvHeaders: csv_headers, csvData: csv_data }
  end

  def regenerate_reconciliation_report_current_year
    job_params = {filter_params: {year: Date.today.year}}
    StripeReconciliationJob.perform_later(:generate_data_rake, job_params)
  end

  def get_stripe_report_months
    data = StripeReportMonth.where('extract(year from report_date) = ? and extract(month from report_date) in (?)',  params[:filters][:year], params[:filters][:selected_months].values)
    data = data.where(hotel_id: params[:filters][:selected_hotels].values) if params[:filters][:selected_hotels].present?
    data = data.distinct.joins(:stripe_report_booking_datas).where('stripe_report_booking_data.organisation_id = ?', params[:filters][:selected_clients].values) if params[:filters][:selected_clients].present?
    data
  end
end
