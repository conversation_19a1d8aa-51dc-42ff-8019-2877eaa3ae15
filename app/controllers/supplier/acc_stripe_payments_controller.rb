class Supplier::AccStripePaymentsController < Supplier::ApplicationController

    layout 'stripe_payments_base'

    before_action :get_booking

    def index
        respond_to do |format|
            format.html
            format.json do
              get_stripe_payments
            end
          end
    end

    def show
    end

    def create
      if maybe_masquerader.is_an_administrator?
        render json: {error: "cannot act as here"}, status: 401 and return
      end
      new_payment = StripeCardPayment.new
      amount =  @booking.total_cost_manager - @booking.total_paid_manager
      currency = @booking.acc_booking_header.rfq_location.accepted_response.currency.code
      paid = new_payment.additional_booking_charge(@booking, params[:charge], currency)

      if paid[:error]
        render json: {error: paid[:error]}, status: 400
      else
        @booking.save(:validate => false)
        @booking.reload
        payment = paid[:ok]
        render json: {payment: payment.to_json(:include => :customer_payments), mgr_paid: @booking.total_paid_manager.to_s, mgr_cost: @booking.total_cost_manager.to_s, total_refunded: @booking.total_stripe_refund.to_s }
        send_email(payment)
      end

    end

    def full_refund
      if maybe_masquerader.is_an_administrator? && !Rails.env.development?
        render json: {error: "cannot act as here"}, status: 401 and return
      end
      message = ""
      @booking.booking_payments.charged.each do |stripe_payment|
            begin
                refund = Stripe::Refund.create({
                    charge: stripe_payment.stripe_charge_id
                })
                payment = @booking.booking_payments.create(
                  amount: -stripe_payment.refundable,
                  stripe_refund_id: refund.id,
                  :cp_id => stripe_payment.id,
                  status: 1,
                  :company_reg_number =>  @booking&.hotel&.company_reg_number,
                  :company_vat_number =>  @booking&.hotel&.company_vat_number,
                  :company_reg_address => @booking&.hotel&.company_reg_address,
                  :stripe_connect_payment => StripeCardPayment.new.use_connected_account(booking) != {}
                )
                send_email(payment)
            rescue StandardError => e
               message += e.message + " "
            end
      end

        if message.blank?
            @booking.assign_attributes(:refund_required => false, :refunded => true, :total_cost_manager => 0)
            @booking.save(:validate => false)
            @booking.reload
            stripe_payments = @booking.booking_payments.charged.joins("left join stripe_payments customer_payments on stripe_payments.cp_id = stripe_payments.id").order("created_at ASC")
            render json: {payments: stripe_payments.to_json(:include => :customer_payments), ref_state: 'refunded', total_refunded: @booking.total_stripe_refund.to_s, total_paid: @booking.total_paid_manager.to_s, refund_msg: helpers.refund_messages_for(@booking)}
        else
            render json: {error: message}, status: 400
        end
    end

    def refund
      if (maybe_masquerader.is_an_administrator? && !Rails.env.development?)
        render json: {error: "cannot act as here"}, status: 401 and return
      end
      booking = AccBooking.find(params[:booking_id])
      begin
        payment = booking.booking_payments.find_by( stripe_charge_id: params[:stripe_charge_id])
        pence_amount = params[:refund_amount].to_pence # convert to pence
        StripeCardPayment.new.delay.refund_payment(payment, booking, pence_amount, params[:refund_reason])
      rescue => e
        render json: {message: e.message}, status: 500
      end

    end

    def no_refund
      if maybe_masquerader.is_an_administrator?
        render json: {error: "cannot act as here"}, status: 401 and return
      end
      unless @booking.refund_requested?
        render json: {error: "cannot set this to no refund   - it is not an optional refund booking"} and return
      end
      if params[:payment][:reason].blank?
          render json: {error: "no reason provided - please proved a reason and try again"} and return
      end
      @booking.update_columns(:refund_requested => false, :no_refund => true, :no_refund_reason => params[:payment][:reason])
      render json: {status: 200}
    end


    def get_stripe_payments
      @stripe_payments = @booking.booking_payments
      if @stripe_payments
        render json: {payments: @stripe_payments.as_json(:include => :customer_payments), booking: @booking.to_json(methods: :get_block_days_count)}
      else
        render json: {status: 'failed'}, status: 400
      end
    end

    def send_email(payment)
      AccBookingMailer.delay.booking_payment_receipt(@booking.id, payment.id) if payment.present?
    end

    private


    def stripe_user_id(booking)
      hotel = booking.get_booking_hotel
      return hotel.stripe_detail.stripe_user_id
    end

    def get_booking
      unless current_user.can_access_booking?(params[:id])
        render json: { error: "Sorry, you do not have permission to access this booking" }, status: 403
        return
      end
    
      @booking = AccBooking.find(params[:acc_booking_id])
    end
end
