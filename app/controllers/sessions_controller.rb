class SessionsController < Devise::SessionsController

  layout "integrated_layout"

  skip_before_action :set_current_org
  skip_before_action :verify_authenticity_token, only: :create, :if => :check_auth
  skip_before_action :verify_authenticity_token, only: :destroy

  # TODO try below with CORS
  before_action do |controller|
    :set_loyalty_session_var unless controller.request.format.json?
    :set_site unless controller.request.format.json?
  end

  def create
    respond_to do |format|
      format.html {
        super
      }
      format.json {
        resource = User.find_for_database_authentication(email: params[:user][:email])
        if resource && resource.valid_password?(params[:user][:password]) && resource.locked_at.blank?
          sign_in :user, resource
          render :json => {:success => true, :uid => resource.id}
        elsif resource.locked_at.present?
          render json: {message: "For security reasons this account has now been locked. To re-open your account please call Servace on 0344 822 3227."}, status: 401
        else
          render json: {message: "Failed Login"}, status: 401
        end
      }
    end
  end

  def after_sign_in_path_for(resource)
    stored_location_for(resource) ||
        if params[:ev_token].present?
          if params[:ev_url].present?
            params[:ev_url] + "?eventstop_login=1"
          else
            EVENTSTOP_HOME + "?eventstop_login=1"
          end
        elsif resource.is_a_client?
          os_myos_path
        elsif resource.is_a_supplier?
          os_myos_path
        elsif resource.is_an_administrator?
          admin_dashboard_path
        else
          "/"
        end
  end

  # Override to allow ajax signout DELETE /resource/sign_out
  def destroy
    signed_out = (Devise.sign_out_all_scopes ? sign_out : sign_out(resource_name))
    set_flash_message! :notice, :signed_out if signed_out
    yield if block_given?

    respond_to do |format|
      format.all {head :no_content}
      format.html {redirect_to after_sign_out_path_for(resource_name)}
      format.json {return render :json => {:success => true, :message => "Signed Out"}}
    end
  end

  def after_sign_out_path_for(resource)
    if params[:ev_exit].present?
      EVENTSTOP_HOME + "/logout"
    else
      super
    end
  end

  def stored_location_for(resource)
    if (r = session[:user_return_to])
      session[:user_return_to] = nil
      r
    else
      super
    end
  end

  private

  def determine_redirect_location(resource)
    session[:venue_search].present? ? conference_stop_conference_path(:venue_search => session.delete(:venue_search)) : redirect_location(resource_name, resource)
  end

  def set_loyalty_session_var
    session[:loyalty] = params[:loyalty] unless session[:loyalty].present?
  end

  def set_site
    @site = Site.find_or_create_by(name: "hg_onestop")
    @site_name = "hg_onestop"
    @body_class = "os"
    session[:site_name] = "os"
  end

  def check_auth
    ev_token = params[:ev_token] || ""
    request.referrer == EVENTSTOP_HOME && OauthApplication.where(uid: ev_token).count == 1
  end

  def verify_signed_out_user
    if all_signed_out?
      set_flash_message! :notice, :already_signed_out

      respond_to do |format|
        format.all {head :no_content}
        format.html {redirect_to after_sign_out_path_for(resource_name)}
        format.json {return render :json => {:success => true, :message => "Already signed out"}}
      end
    end
  end

end
