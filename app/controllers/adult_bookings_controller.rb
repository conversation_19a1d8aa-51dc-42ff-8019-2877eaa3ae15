class AdultBookingsController < ActionController::Base
  # this is the controller for the new adult booking flow in quasar
  layout 'quasarify_layout'
  include ActiveStorage::Blob::Analyzable
  include ActiveStorage::SetCurrent # this sets the current environent so active record works in development
  before_action :load_location, :only => [:index]
  before_action :set_site

  def index
    @start_date = @rfq_location.rfq_request.start_date
    @end_date = @rfq_location.rfq_request.end_date
    @multiple_bookings = @rfq_location.rfq_request.multi_booking
    @detail = @rfq_location.adult_accomm_detail
    @detail = AdultAccommDetail.create(rfq_location_id: params[:rfq_location_id], welcome_msg:'<h2>Welcome to the booking portal for ' + @rfq_location.rfq_request.rfq_programme.name) if @detail.nil?
  end

  def edit
    redirect_to not_found_adult_accommodations_path if current_user.nil?
    @rfq_location = RfqLocation.find(params[:rfq_location_id])
    @start_date = @rfq_location.rfq_request.start_date
    @end_date = @rfq_location.rfq_request.end_date
    @multiple_bookings = @rfq_location.rfq_request.multi_booking
    @detail = @rfq_location.adult_accomm_detail
    @detail = AdultAccommDetail.create(rfq_location_id: params[:rfq_location_id], welcome_msg:'<h2>Welcome to the booking portal for ' + @rfq_location.rfq_request.rfq_programme.name) if @detail.nil?
  end

  def get_rfq_locations
    @rfq_location = RfqLocation.find(params[:rfq_location_id])
    # had to return the name as label because if I called it name it was calling a method in the RfqLocation model and blowing up
    @rfq_locations = RfqLocation.joins(:rfq_request).where('rfq_requests.rfq_programme_id = ? and rfq_locations.released_at is not null', @rfq_location.rfq_request.rfq_programme_id)
    render json: {rfq_locations: @rfq_locations.select('rfq_locations.id, rfq_requests.name as label')}

  end

  def get_proposed_hotels
    hotels = []
    responses = RfqLocation.find(params[:rfq_location_id]).accepted_responses.where('adult_released_at is not null')
    responses = responses.left_joins(rfq_proposed_hotel: :hotel)
    responses.uniq.each do |response|
      # ensure chosen dates are not outside hotel block dates
      block_programme = response.rfq_tasks.find_by(code: 'block_programme')
      block_start = block_programme.accepted_document.start_date.strftime('%Y/%m/%d')
      block_end = block_programme.accepted_document.end_date.strftime('%Y/%m/%d')
      if !params[:date][:from].between?(block_start, block_end) && !params[:date][:to].between?(block_start, block_end)
        responses = responses.where.not(rfq_proposed_hotel_id: response.rfq_proposed_hotel_id)
      end

      # ensure chosen dates are not within hotel blackout dates
      blackout_dates = response.hotel.hotel_blackout_dates.adult_blackout_dates
      if blackout_dates.any?
        # so you should be allowed to check in on the end_date of the blackout date
        # you should be allowed to check out on the start_date of a blackout date
        # you should not be allowed to overlap a blackout date
        overlap_dates = blackout_dates.where("start_date < ? AND end_date > ?", params[:date][:to], params[:date][:from])
        if overlap_dates.any?
          responses = responses.where.not(rfq_proposed_hotel_id: response.rfq_proposed_hotel_id)
        end
      end
    end
    responses.uniq.sort_by(&:position).each do |response|
      hotel = {}
      hotel[:id] = response.rfq_proposed_hotel_id
      hotel[:rfq_response_id] = response.id
      hotel[:name] = response.hotel.name
      hotel[:booking_text] = response.rfq_proposed_hotel.booking_text
      hotel[:display_image] = response.hotel&.display_image&.url || "https://placehold.co/600x400?text=No+Image+Available"
      hotel[:rooms] = response.adult_price_hash
      hotel[:rooms].each do |room|
        room[1][:rooms_remaining] -= AccBooking.where("hotel_id = ? and rfq_response_room_id = ? and first_checkin BETWEEN ? and ?", response.hotel.id, room[1][:exc_trans][:room_id], params[:date][:from], params[:date][:to]).count
      end
      hotels << hotel
    end

    render json: {hotels: hotels}
  end

  def get_all_hotels
    responses = RfqLocation.find(params[:rfq_location_id]).accepted_responses.where('adult_released_at is not null')
    responses = responses.left_joins(rfq_proposed_hotel: :hotel)
    hotels = []
    responses.uniq.sort_by(&:position).each do |response|
      hotel = {}
      hotel[:id] = response.rfq_proposed_hotel_id
      hotel[:rfq_response_id] = response.id
      hotel[:name] = response.hotel.name
      hotel[:booking_text] = response.rfq_proposed_hotel.booking_text
      hotel[:display_image] = response.hotel.display_image.url
      hotel[:rooms] = response.adult_price_hash
      # hotel[:rooms].each do |room|
      #   room[1][:rooms_remaining] -= AccBooking.where("hotel_id = ? and rfq_response_room_id = ? and first_checkin BETWEEN ? and ?", response.hotel.id, room[1][:exc_trans][:room_id], params[:date][:from], params[:date][:to]).count
      # end

      hotels << hotel
    end
    render json: {hotels: hotels}
  end

  def get_business_units
    business_units = RfqBusinessUnit.left_joins(:rfq_bu_accounts).joins(rfq_programme: {rfq_requests: :rfq_locations}).where('rfq_locations.id = ?', params[:rfq_location_id]).distinct
    business_units = business_units.select('rfq_business_units.id, rfq_business_units.name, rfq_business_units.address1,
                                            rfq_business_units.address2, rfq_business_units.address3, rfq_business_units.town,
                                            rfq_business_units.county, rfq_business_units.postcode, rfq_bu_accounts.acc_number')
                                            .order('rfq_business_units.name').uniq
    bu_account_numbers = RfqBuAccount.joins(:rfq_business_unit).where('rfq_business_units.id in (?) and rfq_bu_accounts.acc_number is not null',  business_units.pluck(:id)).select('rfq_bu_accounts.id, rfq_bu_accounts.rfq_business_unit_id, rfq_bu_accounts.rfq_response_id')
    render json: {business_units: business_units, bu_account_numbers: bu_account_numbers}
  end

  def get_payment_options
    @rfq_location = RfqLocation.find(params[:rfq_location_id])
    render json: {payment_options: payment_options}
  end

  def get_academy_payment_options
    @rfq_location = RfqLocation.find(params[:rfq_location_id])
    payment_options = Booking.academy_booking_pay_types(@rfq_location.rfq_request)
    render json: {payment_options: payment_options}
  end

  def get_partner_logo
    url = RfqLocation.find(params[:rfq_location_id]).adult_accomm_detail.partner_logo.url
    render json: {url: url}
  end

  def create_partner_logo
    detail = RfqLocation.find(params[:rfq_location_id]).adult_accomm_detail
    detail.partner_logo.attach(params[params.keys[0]])
    url = detail.partner_logo.url
    render json: {url: url}
  end

  def delete_partner_logo
    RfqLocation.find(params[:rfq_location_id]).adult_accomm_detail.partner_logo.purge
    render json: {status: 'success'}
  end

  def get_welcome_image
    url = RfqLocation.find(params[:rfq_location_id]).adult_accomm_detail.welcome_image.url
    render json: {url: url}
  end

  def create_welcome_image
    detail = RfqLocation.find(params[:rfq_location_id]).adult_accomm_detail
    detail.welcome_image.attach(params[params.keys[0]])
    url = detail.welcome_image.url
    render json: {url: url}
  end

  def delete_welcome_image
    RfqLocation.find(params[:rfq_location_id]).adult_accomm_detail.welcome_image.purge
    render json: {status: 'success'}
  end

  def get_hotel_display_image
    hotel = RfqProposedHotel.find(params[:rfq_proposed_hotel_id]).hotel
    url = hotel.display_image.url
    render json: {url: url}
  end

  def create_hotel_display_image
    hotel = RfqProposedHotel.find(params[:rfq_proposed_hotel_id]).hotel
    hotel.display_image.attach(params[params.keys[0]])
    url = hotel.display_image.url
    render json: {url: url}
  end

  def delete_hotel_display_image
    hotel = RfqProposedHotel.find(params[:rfq_proposed_hotel_id]).hotel
    hotel.display_image.purge
    render json: {status: 'success'}
  end

  def create_bookings
    ActiveRecord::Base.transaction do
      if params[:guests].map {|guest| guest[:email].strip.downcase }.uniq.length != params[:guests].length
        # params.clear
        raise 'Duplicate emails detected' 
      end
      location = RfqLocation.find(params[:rfq_location_id])
      client_id = location.rfq_programme.client_id
      proposed_hotel = RfqProposedHotel.find(params[:rfq_proposed_hotel_id])
      response = proposed_hotel.rfq_response
      hotel = proposed_hotel.hotel
      total_nights = ((params[:date][:to].to_date - params[:date][:from].to_date).to_i) - 1
      room = RfqResponseRoom.find(params[:room_id])
      per_attendee_cost = (total_nights * room.price_inc_vat.to_pence)
      total_cost = params[:total_cost].to_pence
      if params[:booker][:payment_method] == 'Card'
        total_cost += Booking.new.calculate_stripe_with_vat(total_cost)
        per_attendee_cost += Booking.new.calculate_stripe_with_vat(per_attendee_cost)
      end
      booking = location.bookings.new(booking_type: Booking.booking_types[:academy_hub], hotel_id: hotel.id,
                                      payment_method: params[:booker][:payment_method],
                                      check_in: params[:date][:from], check_out: params[:date][:to],
                                      total_nights: params[:total_nights], total_cost: total_cost,
                                      booker_forename: params[:booker][:forename], booker_surname: params[:booker][:surname], 
                                      booker_email: params[:booker][:email], booker_telephone: params[:booker][:telephone],
                                      booker_rfq_business_unit_id: params[:booker][:rfq_business_unit_id],
                                      booker_company_name: params[:booker][:booker_company_name], booker_company_address_1: params[:booker][:booker_company_address_1],
                                      booker_company_address_2: params[:booker][:booker_company_address_2], booker_company_postcode: params[:booker][:booker_company_postcode],
                                      booker_company_town: params[:booker][:booker_company_town], booker_company_county: params[:booker][:booker_company_county],
                                      special_requirements: params[:booker][:special_requirements], client_id: client_id, tcs_acceptance: params[:booker][:tcs_acceptance])
      booking.booker_id = current_user.id if current_user
      if params[:booker][:payment_method] == 'Business Account'
        booking.account_number = params[:booker][:account_number]
        booking.booking_payments.new(hotel_id: hotel.id, status: StripePayment.statuses[:pending], payment_type: StripePayment.payment_types[:credit_account], amount: total_cost, stripe_connect_payment: false)
      end
      params[:guests].each do |guest|
        booking.booking_attendees.new(rfq_response_room: room,
                                        forename: guest[:forename], surname: guest[:surname], email: guest[:email], telephone: guest[:telephone],
                                        payment_method: params[:booker][:payment_method], rfq_business_unit_id: guest[:rfq_business_unit_id],
                                        company_name: guest[:company_name], company_address_1: guest[:company_address_1], company_address_2: guest[:company_address_2],
                                        company_postcode: guest[:company_postcode], company_town: guest[:company_town], company_county: guest[:company_county],
                                        client_id: client_id, total_cost: (total_cost / params[:guests].length), special_requirements: guest[:special_requirements])
      end
      if booking.save!
        AcademyHubMailer.delay.send_hotel_booking_request(booking.id) if booking.payment_method != 'Card' #sends in webhook instead
        render json: {booking: booking}, status: 200
      end
    rescue => error
      render json: {status: 'error', message: error.message}, status: 400
    end
  end

  private

  def load_location
    if params[:code].present?
      unless @location = RfqLocation.where(:location_code => params[:code]).first
        redirect_to not_found_adult_accommodations_path
      end
      @rfq_location = @location
    else
      redirect_to not_found_adult_accommodations_path
    end
  end

  def set_site
    @hub = 'academy'
  end
end
