class AcademyBaseController < ApplicationController

  #before_action :kill_if_production
  # before_action :check_access
  before_action :set_site
  layout "integrated_layout"

  def set_site
    if current_user.is_a_client?
      @academy_client_mode = true
    elsif current_user.is_a_supplier?
      @academy_supplier_mode = true
    else
      @academy_admin_mode = true
    end
    @body_class = "ac"
    session[:site_name] = "ac"
  end
end