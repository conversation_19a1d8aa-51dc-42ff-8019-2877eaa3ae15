require 'net/http'
require 'json'

class AccLearnersController < AccBookingsBaseController
  before_action :load_resources
  before_action :get_learner,
                only: %i[show edit update destroy set_leaving_date leaving left restore bookings set_password invite_learners_manager]

  def set_password

    url = ''

    url.concat(ENV['API_URL'])
    url.concat('learner/')
    url.concat(@learner.id.to_s)
    url.concat('/set-password')

    uri = URI(url)
    req = Net::HTTP::Post.new(uri)
    req.add_field('authorization', ENV['API_KEY'])

    res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: /^https:/i.match?(url) ) do |http|
      http.request(req)
    end

    puts url
    puts res.code

    respond_to do |format|
      case res
      when Net::HTTPSuccess, Net::HTTPRedirection
        format.json { render json: res.body, status: 202 }
      else
        format.json { render json: res.body, status: 422 }
      end
    end
  end

  def invite_filtered_users
    url = ''

    url.concat(ENV['API_URL'])
    url.concat('learner')
    url.concat('/set-password-bulk')

    uri = URI(url)
    req = Net::HTTP::Post.new(uri)
    req.add_field('authorization', ENV['API_KEY'])

    res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: /^https:/i.match?(url) ) do |http|
      body = if params[:learner_id]
        {'user_ids[]' => params[:learner_id]}
      else
       {'user_ids[]' => Rails.cache.read("learners_#{current_user.id}")}
      end
      post_data = URI.encode_www_form(body)
      http.request(req, post_data)
    end

    puts url
    puts res.code

    flash[:notice] = 'Invited filtered learners'

    redirect_to :action => :index
  end

  def invite_learners_manager
    @learner.invite_manager_to_new_servace_application(current_user)
    render json: { success: true}
  end

  def index
    base = RfqLearner.includes(:next_of_kin, :acc_booking_people).where(rfq_programme_id: @programme.id)

    if params[:rfq_learner]
      base = base.where(rfq_business_unit_id: params[:rfq_learner][:rfq_business_unit_id] == "NULL" ? nil : params[:rfq_learner][:rfq_business_unit_id]) if params[:rfq_learner][:rfq_business_unit_id].present?
      base = base.where("group_number ilike ?", '%' + params[:rfq_learner][:group_number]+ '%') if params[:rfq_learner][:group_number].present?

      if params[:rfq_learner][:forename].present? && params[:rfq_learner][:surname].present?
        base = base.where("rfq_learners.forename ilike ? or rfq_learners.surname ilike ?", "%#{params[:rfq_learner][:forename]}%", "%#{params[:rfq_learner][:surname]}%")
      elsif params[:rfq_learner][:forename].present?
        # If only one letter is in the param do it as a starts with search
        base = base.where("rfq_learners.forename ilike ?", params[:rfq_learner][:forename].length == 1 ? params[:rfq_learner][:forename] + '%' : '%' + params[:rfq_learner][:forename] + '%')
      elsif params[:rfq_learner][:surname].present?
        base = base.where("rfq_learners.surname ilike ?", params[:rfq_learner][:surname].length == 1 ? params[:rfq_learner][:surname] + '%' : '%' + params[:rfq_learner][:surname] + '%')
      end

      base = base.where("rfq_learners.email ilike ?", '%' + params[:rfq_learner][:email] + '%') if params[:rfq_learner][:email].present?
      base = base.where("rfq_learners.manager_email ilike ?", '%' + params[:rfq_learner][:manager_email] + '%') if params[:rfq_learner][:manager_email].present?

      if params[:rfq_learner][:learner_emails] == "Yes"
        base = base.where.not(email: nil)
      elsif params[:rfq_learner][:learner_emails] == "No"
        base = base.where(email: [nil, ''])
      end

      if params[:rfq_learner][:notifications_allowed] == "Yes"
        base = base.where(allow_notifications: true)
      elsif params[:rfq_learner][:notifications_allowed] == "No"
        base = base.where(allow_notifications: [nil, '', false])
      end

      base = base.where(start_date: params[:rfq_learner][:start_date].to_date) if params[:rfq_learner][:start_date].present?
      base = base.where("rfq_learners.programme_name ilike ?", '%' + params[:rfq_learner][:programme_name] + '%') if params[:rfq_learner][:programme_name].present?

      if params[:rfq_learner][:levy] == "Yes"
        base = base.where(levy: true)
      elsif  params[:rfq_learner][:levy] == "No"
        base = base.where(levy: [false, nil])
      end

      if params[:rfq_learner][:next_of_kin_present] == "true"
        base = base.where.not(next_of_kin: {id: nil})
      elsif params[:rfq_learner][:next_of_kin] == "false"
        base = base.where(next_of_kin: {id: nil})
      end

      if params[:rfq_learner][:left] == "true"
        base = base.where.not(left_at: nil)
      elsif params[:rfq_learner][:left] == "false"
        base = base.where(left: [false, nil], left_at: nil)
      end

      if params[:rfq_learner][:password] == "Yes"
        base = base.where.not(password: nil)
      elsif params[:rfq_learner][:password] == "No"
        base = base.where(password: nil)
      end

    elsif params[:read_users_cache]
      base = base.where(id: Rails.cache.read("learners_#{current_user.id}"))
    end

    base = client_hidden_clause(base)

    if params[:rfq_learner].present?
      params.permit!
      @learner_search = RfqLearner.new(params[:rfq_learner])
    else
      @learner_search = RfqLearner.new
    end

    # TODO keep an eye on the performance of this cache write, because it's running on every index it may cause lag
    Rails.cache.delete("learners_#{current_user.id}")
    Rails.cache.write("learners_#{current_user.id}", base.map(&:id), expires_in: 2.hours)

    @learners = base.paginate(:page => params[:page] || 1, :per_page => 100)
  end

  def new
    @learner = RfqLearner.new
    @learner.allow_notifications = true
  end

  def create
    @learner = @programme.rfq_learners.new(safe_params)
    @learner.organisation_id = @programme.client_id
    if @learner.manager_details.present?
      @learner.set_manager
    end
    if @learner.save
      redirect_to acc_programme_acc_learners_path(@programme, { :q => { 'surname_cont' => "#{@learner.surname}" } }), notice: "Successfully created learner"
    else
      render :new
    end
  end


  def update

    @learner.attributes = (safe_params)
    if @learner.manager_details.present?
      @learner.set_manager
    end
    if @learner.save
      redirect_to acc_programme_acc_learners_path(@programme), notice: "Successfully updated learner"
    else
      render :edit
    end
  end

  def edit_group
    @learner = RfqLearner.new
  end

  def update_group
    params[:q].reject { |k, v| v.blank? }
    @learners = RfqLearner.joins(:rfq_business_unit => :rfq_programme).
      joins("left outer join lateral  (select count(*) as ct from acc_booking_people p where p.rfq_learner_id = rfq_learners.id)  as people on true").
      where("rfq_programmes.id = ?", @programme.id).
      select("rfq_learners.*, rfq_business_units.name as bu_name, people.ct as people_count ")
    first_lot = @learners.ransack(params[:q]).result

    if params[:rfq_learner][:group_number].present? && first_lot.all.size > 0
      RfqLearner.where(:id => first_lot.all.map(&:id)).update_all(:group_number => params[:rfq_learner][:group_number])
      params[:q][:group_number_cont] = params[:rfq_learner][:group_number]
      @learners = @learners.ransack(params[:q]).result.paginate(:page => 1, :per_page => 100)
    else
      @errors = []
      @errors << "Sorry, group code is blank,  you must enter something to use as the group code. " if params[:rfq_learner][:group_number].blank?
      @errors << "Sorry, no learners found for that group code" if first_lot.size == 0
      @errors = @errors.to_sentence
      @learner = RfqLearner.new
    end
  end

  def destroy

    if @learner.safe_to_destroy?
      @learner.destroy
      flash[:notice] = 'Safely deleted the learner as no bookings for them'
    else
      flash[:notice] = 'Sorry the learner has bookings - they cannot be deleted'
    end
    redirect_to :action => :index
  end

  def bookings

    @bookings = AccBooking.joins(:acc_booking_person => :rfq_learner).
      where("rfq_learners.id = ?", @learner.id)
  end

  def set_leaving_date

    respond_to do |format|
      format.js { }
    end
  end

  def leaving

    params[:rfq_learner][:left_set_by] = @current_user.name
    params[:rfq_learner][:left] = true
    if @learner.update(safe_params)
      if params[:rfq_learner][:cancel_bookings]
        bookings = @learner.left_bookings
        audit_left_bookings!
        RfqLearner.delay.cancel_left_bookings(@learner.id, bookings)
      end
      render json: { success: true}
    else
      render json: { errors: @learner.errors.full_messages }, status: 400
    end
  end

  def left

    if params[:commit] == "Cancel Bookings" && params[:bookings].present? && params.permit!.to_h[:bookings].any?
      RfqLearner.delay.cancel_left_bookings(@learner.id, params[:bookings])
      audit_left_bookings!
    else
      audit_no_cancels!
    end

    respond_to do |format|
      format.js { }
    end
  end

  def audit_left_bookings!
    audit = Audited::Audit.new
    audit.auditable = @learner
    audit.user = current_user
    audit.username = current_user.full_name_or_email
    audit.action = 'Learner left user choosing bookings to cancel'
    audit_string = @learner.left_bookings.map(&:id).to_s
    audit.comment = "Future bookings: #{audit_string} and those being cancelled here: #{params[:bookings].inspect}"
    audit.save!
  end

  def audit_no_cancels!
    audit = Audited::Audit.new
    audit.auditable = @learner
    audit.user = current_user
    audit.username = current_user.full_name_or_email
    audit.action = 'Learner left user chose no bookings to cancel'
    audit_string = @learner.left_bookings.map(&:id).to_s
    audit.comment = "Future bookings: #{audit_string} and none were cancelled}"
    audit.save!
  end

  def restore
    if @learner.restore
      flash[:notice] = 'Learner restored'
    else
      flash[:error] = 'Sorry, learner could not be restored, please contact Servace'
    end
    redirect_to request.referer
  end

  def prep_grp_leave
    params[:filter] ||= {}
    if params[:filter][:group_number_eq].present?
      base = RfqLearner.joins(:rfq_business_unit => :rfq_programme, :acc_booking_people => :acc_bookings).
        joins("left outer join lateral  (select count(*) as ct from acc_bookings b inner join acc_booking_people p  on b.acc_booking_person_id = p.id   where p.rfq_learner_id = rfq_learners.id and b.first_checkin is not null and b.first_checkin >= now() and b.cancelled_at is null)  as books on true").
        where("rfq_programmes.id = ?", @programme.id).
        select("rfq_learners.*, rfq_business_units.name as bu_name, books.ct as book_count ").distinct

      @learners = base.where(:group_number => params[:filter][:group_number_eq])
    else
      @learners = RfqLearner.where(:id => -1)
    end
  end

  def bulk_leave
    base = RfqLearner.joins(:rfq_business_unit => :rfq_programme, :acc_booking_people => :acc_bookings).
      joins("left outer join lateral  (select count(*) as ct from acc_bookings b inner join acc_booking_people p  on b.acc_booking_person_id = p.id   where p.rfq_learner_id = rfq_learners.id and b.first_checkin is not null and b.first_checkin >= now() and b.cancelled_at is null)  as books on true").
      where("rfq_programmes.id = ?", @programme.id).
      where("rfq_learners.left is false").
      select("rfq_learners.*, rfq_business_units.name as bu_name, books.ct as book_count ").distinct

    @learners = base.where(:group_number => params[:group])
    if @learners.any? { |l| l.book_count > 0 }
      flash[:error] = "Sorry, one or more of the group has future bookings you cannot bulk leave until these are resolved"
    else
      RfqLearner.bulk_leave!(@learners, current_user)
    end
    redirect_to prep_grp_leave_acc_programme_acc_learners_path(@programme, :filter => { :group_number_eq => params[:group] })

  end

  def notification_index
    @page = params[:page].to_i
    @prevPage = @page === 1 ? 1 : @page - 1
    @nextPage = @page + 1

    @data = loadNotifications()
  end

  def hide

    @learner.client_hidden = true
    @learner.save
  end

  def unhide
    if params[:lid].present?
      @learner = RfqLearner.where(:id => params[:lid].to_i, :rfq_programme_id => @programme.id).first
    else
      get_learner
    end
    @learner.client_hidden = false
    @learner.save
    render :hide
  end

  def prep_grp_hide
    @learners = @programme.rfq_learners.where("group_number = ? ", params[:code]).
      where("client_hidden is not true")
    if @learners.count == 0
      @error_message = "Sorry, no learners found for that code"
    elsif @learners.any? { |l| !l.left? }
      @error_message = "Sorry, one or more of these learners is not set to left - please ensure they are all left if you wish to hide this group."
    end
  end

  def hide_group
    @learners = @programme.rfq_learners.where("group_number = ? ", params[:group][:code]).
      where("client_hidden is not true")
    @learners.update_all(:client_hidden => true)

  end

  def load_resources
    if current_user.is_an_administrator?
      @programme = RfqProgramme.find params[:acc_programme_id]
    else
      @programme = RfqProgramme.where("id in (?)", RfqProgramme.allowed_programmes_for(current_user.contact)).find params[:acc_programme_id].to_i
    end

    unless @programme.present?
      redirect_to request.referrer :notice => "Sorry did not find the programme / organisation"
    end
  end

  def get_learner
    # Step 1: Find the RFQ Learner by ID
    @learner = RfqLearner.find(params[:id] || params.dig(:rfq_learner, :id))

    # Step 2: Check if the learner has a Business Unit. If not, assign to UNASSIGNED or Create a temporary one.
    unless @learner.rfq_business_unit_id
      # Step 1: Try to find an existing Business Unit with the name "UNASSIGNED" for the same programme
      temp_business_unit = RfqBusinessUnit.find_by(name: "UNASSIGNED", rfq_programme_id: @programme.id)
    
      # Step 2: If not found, create a new one
      unless temp_business_unit
        temp_business_unit = RfqBusinessUnit.create!(
          name: "UNASSIGNED",
          rfq_programme_id: @programme.id,
          postcode: 'AA1 1AA',
          code: SecureRandom.uuid
        )
      end
    
      # Step 3: Assign the Business Unit to the learner
      @learner.update!(rfq_business_unit_id: temp_business_unit.id)
    end

    # Step 4: Rebuild the query with joins if the Business Unit exists
    base = RfqLearner.joins(rfq_business_unit: :rfq_programme)
                     .where(rfq_programmes: { id: @programme.id })
                     .readonly(false)
    base = client_hidden_clause(base)

    # Step 5: Find the learner again to ensure everything is loaded correctly
    @learner = base.find(@learner.id)
  end

  def get_programmes_for_transfer
    client = RfqProgramme.find(params[:acc_programme_id]).client
    programmes = client.rfq_programmes.joins(:rfq_requests).
    where.not(id: params[:acc_programme_id]).
    where('rfq_requests.end_date >= ?', Date.today).
    where(rfq_type: 'Apprentice').distinct
    render json: programmes.select('rfq_programmes.id as value, rfq_programmes.name as label').to_json
  end

  def get_learners_for_transfer
    new_programme = RfqProgramme.find(params[:selected_programme_id])
    include_left = params[:include_left] == 'true'
    destination_programme_learners = new_programme.rfq_learners.where.not(email: nil)
    destination_programme_emails = destination_programme_learners.pluck(:email)
    learners = @programme.rfq_learners.where.not("email LIKE ANY ( array[?] )", destination_programme_emails)

    # filtering
    learners = learners.still_here if !include_left
    learners = learners.where("surname ILIKE ?", "%#{params[:name]}%") if params[:name].present?
    learners = learners.where("email ILIKE ?", "%#{params[:email]}%") if params[:email].present?
    render json: learners.select('id as value', :forename, :surname, :email).to_json
  end

  def transfer_learners
    programme_from = @programme
    programme_to = RfqProgramme.find(params[:programme_id])
    learners_to_transfer = programme_from.rfq_learners.where(id: params[:learner_ids])

    ActiveRecord::Base.transaction do
      learners_to_transfer.group_by(&:rfq_business_unit).each do |bu, learners|
        db_bu = programme_to.rfq_business_units.where(name: programme_to.name + ' - ' + bu.name).first
        new_bu = db_bu.present? ? db_bu : bu.dup
        if db_bu.blank?
          new_bu.code = SecureRandom.uuid
          new_bu.name = programme_to.name + ' - ' + new_bu.name
          new_bu.rfq_programme_id = programme_to.id
          new_bu.save!
        end
        learners.each do |learner|
          if programme_to.rfq_learners.where(email: learner.email).blank?
            new_learner = learner.dup
            new_learner = RfqLearner.new(new_learner.attributes.merge(rfq_business_unit_id: new_bu.id, rfq_programme_id: programme_to.id, left: false, left_at: nil, left_set_by: nil, left_programme: nil))
            new_learner.save!
          end
        end
      end
      learners_to_transfer.update_all(left: true, left_programme: DateTime.now, left_set_by: current_user.name, left_at: DateTime.now)
    end

  rescue ActiveRecord::RecordInvalid => exception
    render json: { errors: exception.record.errors.full_messages }, status: 422
  end

  private

  def client_hidden_clause(base)
    if current_user.is_a_client?
      base = base.where("rfq_learners.client_hidden is not true")
    end
    base
  end

  def loadNotifications()
    url = ''

    url.concat(ENV['API_URL'])
    url.concat('notifications/all')

    url.concat('?programme_id=')
    url.concat(@programme.id.to_s)

    if params.has_key?(:page)
      url.concat('&page=')
      url.concat(params[:page].to_s)
    end

    uri = URI(url)
    req = Net::HTTP::Get.new(uri)
    req.add_field('authorization', ENV['API_KEY'])

    res = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
      http.request(req)
    end

    success = res&.code.to_i.abs.digits[-1] == 2

    if success
      puts url
      puts res.code
      JSON.parse(res.body)['data']
    else
      nil
    end


  end

  def safe_params
    params.require(:rfq_learner).permit(:rfq_response_room_id, :subsistence_only, :left_at, :left_set_by, :manager_details, :forename, :surname, :date_of_birth, :gender, :learner_emails,
                                        :manager, :manager_email, :manager_telephone, :rfq_business_unit_id, :reference, :middlename, :email, :telephone,
                                        :rooming_preference, :special_requirements, :note, :group_number, :adult, :left, :client_hidden,
                                        :start_date, :proposed_end_date, :programme_name, :course_code, :additional_information, :employer_name, :employer_contact_email, :employer_contact_number,
                                        :allow_notifications,
                                        :levy, :single_room_required, :single_room_required_reason, :client_business_unit_id, :client_learner_id,
                                        :password, :remember_token, :email_verified_at, :next_of_kin, :next_of_kin_present, :read_users_cache)
  end

end
