class AccBookingsBaseController < ApplicationController

  #before_action :kill_if_production
  before_action :check_access
  before_action :set_site
  layout "integrated_layout"

  def set_site
    if current_user.is_a_client?
      @acc_client_mode = true
    elsif current_user.is_a_supplier?
      @acc_supplier_mode = true
    else
      @acc_admin_mode = true
    end
    @admin_mode = true
    @body_class = "ap"
    session[:site_name] = "ap"
  end

  def check_access
    unless user_signed_in?
      flash[:error] = "Sorry No access to Bookings"
      redirect_to "/"
      return false
    end
    unless current_user.is_an_administrator? || current_user.is_a_client?
      flash[:error] = "Sorry No access to Bookings"
      redirect_to "/"
      return false
    end

    if current_user.is_a_client? && !current_user.app_enabled
      flash[:error] = "Sorry No access to Bookings"
      redirect_to "/"
      return false
    end
  end

  def admin_only
    unless current_user.is_an_administrator?
      flash[:error] = "Sorry No Access to that action"
      redirect_to "/"
      return false
    end
  end

  def client_only
    unless current_user.is_a_client?
      flash[:error] = "Sorry No Access to that action"
      redirect_to "/"
      return false
    end
  end

  def check_issue_access
    redirect_to rfq_client_dashboard_path, :notice => "Sorry you do not have access to any issues" if (current_user.is_a_client? && current_user.contact.rfq_roles.issue_access.count == 0)
  end

  def check_for_clashes(booking)
    return false unless booking.present? && booking.acc_booking_person.person_type == "TRA" && booking.acc_booking_person.rfq_trainer.present?

    if params[:clash_override] == '1'
      booking.clash_ignored_by = maybe_masquerader.full_name_or_email
    else
      clashes = AccBooking.dupe_trainer_bookings(@programme.client_id, booking.acc_booking_person.rfq_trainer.email, booking.first_checkin, booking.last_check_out, (booking.persisted? ? booking.id : nil))
      if clashes.any?
        @clashes = clashes
      end
    end
  end

  def client_hidden_clause(base)
    if current_user.is_a_client?
      base = base.where("rfq_learners.client_hidden is not true")
    end
    base
  end



  def kill_if_production
    raise "Not production ready" if Rails.env == "production"
  end



end
