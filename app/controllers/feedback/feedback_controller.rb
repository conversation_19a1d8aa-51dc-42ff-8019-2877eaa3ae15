class Feedback::FeedbackController < Feedback::FeedbackAppController

  before_action :get_surveys, only: :index
  before_action :filter_by_client, only: [:index], if: -> { current_user.is_an_administrator? }

  def index
    @role = "Admin"
    if current_user.is_a_client? && @surveys
      @role = "Client"
      @surveys = @surveys.for_role(current_user.contact)
    elsif current_user.is_a_supplier? && @surveys
      @role = "Hotel"
      if current_user.is_a_chain_user?
        @surveys = @surveys.where(hotel_id: current_user.organisation.hotels.ids)
      else
        @surveys = @surveys.where(hotel_id: current_user.organisation.id)
      end
    end

    # Added to prevent n+1 queries
    @notifications_date = Date.new(@year.to_i, @month.to_i) + 1.month
    @surveys = @surveys.includes(:survey_responses, :feedback_notifications)

    @surveys = @surveys.page(params[:page])
  end

  def show
    month = @month = params['month']
    year = @year = params['year']

    @date_selected = Date.new(params[:year].to_i, params[:month].to_i)

    @survey = Survey.find_by_id(params[:id])
    authorize @survey

    @survey_responses = @survey.survey_responses.for_month_and_year(month, year)
    @response_count = @survey_responses.count

    # Comments now are selected for the filtered month
    @hg_comments = @survey.feedback_comments.for_month_and_year(month, year)

    get_feedback_data

    delay, pdf_template = get_pdf_name_and_template(params[:selected_pdf])

    respond_to do |format|
      format.html
      format.pdf do
        render pdf: "feedback/" + params[:selected_pdf],
               template: pdf_template,
               layout: 'pdf',
               disable_smart_shrinking: false,
               dpi: '300',
               margin: {bottom: 10},
               footer: {
                   content: get_footer
               }
      end
    end
  end

  def performance_tab
    @survey = Survey.find_by_id(params[:feedback_id])
    authorize @survey, :show?

    @date_selected = Date.new(params[:year].to_i, params[:month].to_i)

    respond_to do |format|
      format.js
      format.pdf do
        render pdf: 'feedback/hotel_performance',
               template: 'feedback/feedback/pdfs/hotel_performance',
               layout: 'pdf',
               disable_smart_shrinking: false,
               dpi: '300',
               margin: {bottom: 10},
               footer: {
                   content: get_footer
               }
      end
    end
  end

  def training_comments_tab
    get_training_comments
    render pdf: 'feedback/training_comments',
            template: 'feedback/feedback/pdfs/training_comments',
            layout: 'pdf',
            disable_smart_shrinking: false,
            orientation: 'Landscape',
            dpi: '300',
            margin: {bottom: 10},
            footer: {
                content: get_footer
            }
  end

  def hotel_feedback_data
    survey_ids = [params[:feedback_id]]
    # Supersedes what was in show, as moving to vue

    survey = Survey.find_by_id(params[:feedback_id])

    rfq_programme = survey.rfq_programme

    @date_selected = Date.new(params[:year].to_i, params[:month].to_i)

    survey_responses = SurveyResponse.where(survey_id: survey_ids)

    survey_responses = survey_responses.for_month_and_year(params['month'], params['year'])

    unless survey_responses.present?
      render json: {error: 'No data found for the selected filters'}
      return
    end

    @hotel_data = Survey.raw_data(survey_ids, survey_responses, 1)
  end

  def training_feedback_data
    survey_ids, survey_responses, bookings = get_training_data

    question_ids = params[:selectedQuestions].values if params[:selectedQuestions].present?

    @training_data = Survey.raw_data(survey_ids, survey_responses, 0, question_ids) unless current_user.is_a_supplier? || survey_responses.nil? || survey_responses.empty?

    @trainer_response_data = Survey.trainer_response_data(survey_responses, bookings, params[:selectedRFQTrainers])  if survey_responses.present?

    # SEE JSON BUILDER for Details
  end

  def trainer_breakdown_data
    # PARK FOR NOW
    # survey_ids, survey_responses, bookings = get_training_data

    # question_ids = [params[:question_id]]

    # @training_data = Survey.raw_data(survey_ids, survey_responses, 0, question_ids)
  end


  def training_comments_data
    get_training_comments
    @trainers = Trainer.where(questionnaire_id: @survey.id).group(:name).select("ARRAY_AGG(id) as ids, name").order(:name)

    @show_rfq_trainers = @survey.organisation.enable_rfq_trainers_for_feedback?

    @rfq_trainers = if @show_rfq_trainers
                      @survey.rfq_programme.rfq_trainers.current.select("id, concat(forename, ' ', surname) as name").order(:name)
                    else
                      []
                    end
    # SEE JSON BUILDER for Details
  end

  def training_performance_tab
    @survey = Survey.find_by_id(params[:feedback_id])
    authorize @survey, :show?

    @date_selected = Date.new(params[:year].to_i, params[:month].to_i)

    respond_to do |format|
      format.js
      format.pdf do
        render pdf: 'feedback/training_performance',
               template: 'feedback/feedback/pdfs/training_performance',
               layout: 'pdf',
               disable_smart_shrinking: false,
               dpi: '300',
               margin: {bottom: 10},
               footer: {
                   content: get_footer
               }
      end
    end
  end

  def raw_data
    setup_raw_data_survey_data

    respond_to do |format|
      format.html
      format.csv do
        headers['Content-Disposition'] = "attachment; filename=\"raw_data.csv\""
        headers['Content-Type'] ||= 'text/csv'
        send_data @survey_responses.to_csv(false), :filename => 'raw_data.csv'
      end
    end
  end

  def raw_training_data
    setup_raw_data_survey_data(true)

    respond_to do |format|
      format.html
      format.csv do
        headers['Content-Disposition'] = "attachment; filename=\"raw_data.csv\""
        headers['Content-Type'] ||= 'text/csv'
        send_data @survey_responses.to_csv(true), :filename => 'raw_data.csv'
      end
    end
  end


  def feedback_comments_data
    # For new feedbacj comments screen in vue for hotels
    @survey = Survey.find_by_id(params[:feedback_id])

    month = params[:month]
    year = params[:year]

    @date_selected = Date.new(year.to_i, month.to_i)

    survey_responses = SurveyResponse.where(survey_id: @survey.id)

    survey_responses = survey_responses.for_month_and_year(params['month'], params['year'])

    unless survey_responses.present?
      render json: {error: 'No data found for the selected filters'}
      return
    end

    @comments = Answer.includes(:question, :survey_response, :organisation_action, :prize).hotel_comments_for_survey(@survey).where(survey_response: survey_responses)

    respond_to do |format|
      format.json
    end
  end

  private

  def get_training_data
    survey_ids = [params[:feedback_id]]

    # Only need one survey as they will all be for same progranne
    rfq_programme = Survey.find_by_id(params[:feedback_id]).rfq_programme

    if params[:selectedSurveys]
      survey_ids << params[:selectedSurveys].values
      survey_ids.flatten!
    end

    @date_selected = Date.new(params[:year].to_i, params[:month].to_i)

    survey_responses = SurveyResponse.where(survey_id: survey_ids)


    if params[:selectedJIs].present?
      survey_responses = survey_responses.filter_by_joining_instructions(params[:selectedJIs].values)
    end

    if params[:selectedTrainers].present?
      trainer_ids = parse_trainer_ids(params[:selectedTrainers])
      survey_responses = survey_responses.where(trainer_id: trainer_ids)
    end

    if params[:selectedGroupCodes].present?
      survey_responses = survey_responses.joins(:rfq_learner).where('rfq_learners.group_number' => params[:selectedGroupCodes].values)
    end

    unless survey_responses.present?
      render json: {error: 'No data found for the selected filters'}
      return
    end

    @show_rfq_trainers = rfq_programme.client.enable_rfq_trainers_for_feedback?

    bookings = AccBooking.joins(:acc_booking_header => [:selected_trainer, :rfq_location => :rfq_request]).
      where('acc_bookings.cancelled_at is null').
      where('last_check_out BETWEEN ? AND ?', @date_selected.beginning_of_month, @date_selected.end_of_month).
      where("acc_booking_headers.rfq_trainer_id IS NOT NULL").
      where('acc_bookings.cancelled_at is null').
      where('rfq_requests.rfq_programme_id = ?', rfq_programme.id)

    survey_responses = survey_responses.where(booking_id: bookings.pluck(:id))

     # ES needs to be here to allow bottom table to filter
    if params[:selectedRFQTrainers].present?
      survey_responses = survey_responses.joins("INNER JOIN acc_bookings on acc_bookings.id = survey_responses.booking_id
      INNER JOIN acc_booking_headers on acc_bookings.acc_booking_header_id = acc_booking_headers.id").
      where('acc_booking_headers.rfq_trainer_id in (?)', params[:selectedRFQTrainers].values)
    end


    return survey_ids, survey_responses, bookings

  end

  def get_training_comments
    @survey = Survey.find_by_id(params[:feedback_id])
    authorize @survey, :show?
    survey_responses = @survey.survey_responses.for_month_and_year(params['month'], params['year'])

    if params[:trainers].present?
      survey_responses = survey_responses.where(trainer_id: params[:trainers].values)
    end

    if params[:rfq_trainers].present?
      survey_responses = survey_responses.joins("INNER JOIN acc_bookings on acc_bookings.id = survey_responses.booking_id
        INNER JOIN acc_booking_headers on acc_bookings.acc_booking_header_id = acc_booking_headers.id").
        where('acc_booking_headers.rfq_trainer_id in (?)', params[:rfq_trainers].values)
    end

    @training_comments = Answer.includes(:survey_response).trainee_comments_for_survey(@survey).where(survey_response: survey_responses)

    # Excludes comments that have been filtered out for the pdf
    if params[:comments].present?
      @training_comments = @training_comments.where('answers.id in (?)', params[:comments].values)
    end

    @date_selected = Date.new(params[:year].to_i, params[:month].to_i)
  end

  def setup_raw_data_survey_data(is_training_data = false)
    month, year = if params[:date]
                    [params[:date][:month], params[:date][:year]]
                  else
                    [Date.today.last_month.month, Date.today.last_month.year]
                  end

    get_surveys

    @survey_responses = SurveyResponse.joins(:survey).for_month_and_year(month, year).
    includes(answers: :question, survey: [rfq_response: [rfq_proposed_hotel: :hotel]])

    if is_training_data
      @survey_responses = @survey_responses.joins("LEFT JOIN acc_bookings on acc_bookings.id = survey_responses.booking_id
        LEFT JOIN acc_booking_headers on acc_bookings.acc_booking_header_id = acc_booking_headers.id
        LEFT JOIN rfq_trainers on acc_booking_headers.rfq_trainer_id = rfq_trainers.id").
        select("survey_responses.*, concat(rfq_trainers.forename, ' ', rfq_trainers.surname) as rfq_trainers_name")
    end

    client_id = if current_user.is_an_administrator?
                  params[:client_filter]
                else
                  current_user.organisation.id
                end

    if client_id.present?
      @survey_responses = @survey_responses.where('questionnaires.client_id = ?', client_id)
    end

    if current_user.is_a_client? && @surveys
      filtered_survey_ids = @surveys.for_role(current_user.contact).pluck(:id)
      @survey_responses = @survey_responses.where(survey_id: filtered_survey_ids)
    end

  end

  def get_feedback_data
    client_contact = @survey.organisation.contact
    @client_contact_name = client_contact.full_name
    @client_contact_email = client_contact.email

    @hotel_questions = @survey.questions.hotel

    @actions = OrganisationAction.get_valid_actions_for_survey(@survey.id, @month, @year)

    @action_ids = @actions.map(&:id).join(',')

    # @hotel_comments = Answer.includes(:question, :survey_response, :organisation_action, :prize, :versions).hotel_comments_for_survey(@survey).where(survey_response: @survey_responses)
# TODO remove versions until dbs merged
    @hotel_comments = Answer.includes(:question, :survey_response, :organisation_action, :prize).hotel_comments_for_survey(@survey).where(survey_response: @survey_responses)

    # Only prizes for hotels, so no need to filter further
    @prizes = Prize.where(answer: @survey.answers).where(survey_response: @survey_responses)

    @hotel_data = @survey.raw_data(@survey_responses, 1)
  end

  def get_surveys
    @month, @year = if params[:date]
                      [params[:date][:month], params[:date][:year]]
                    else
                      [Date.today.last_month.month, Date.today.last_month.year]
                    end

    @exclude_self_managed = params[:exclude_self]

    @surveys = Survey.responses_only.filter_by_month_year(@month, @year).order(:rfq_response_id)
  end


  def filter_by_client
    if params[:client_filter].present?
      @client = params[:client_filter]
      @surveys = @surveys.filter_by_client(@client)
    end
    if params[:exclude_self]
      client_ids = Organisation.exclude_self_managed.clients.select(:id).map(&:id)
      @surveys = @surveys.where(client_id: client_ids)
    end
  end

  def get_pdf_name_and_template(selected_pdf)
    if selected_pdf == 'hotel_feedback'
      return 2500, 'feedback/feedback/pdfs/hotel_feedback'
    elsif selected_pdf == 'hotel_performance'
      return 0, 'feedback/feedback/pdfs/hotel_performance'
    elsif selected_pdf == 'hotel_comments'
      return 0, 'feedback/feedback/pdfs/hotel_comments'
    elsif selected_pdf == 'actions'
      return 0, 'feedback/feedback/pdfs/actions'
    elsif selected_pdf == 'hotel_view_actions'
      return 0, 'feedback/feedback/pdfs/hotel_view_actions'
    end
  end

  def get_footer
    render_to_string(template: 'feedback/feedback/pdfs/footer', formats: [:pdf])
  end

  def parse_trainer_ids(trainers)
    trainers = trainers.values
    trainer_ids = []
    trainers.each do |trainer|
      trainer.split(',').each do |id|
        trainer_ids << id
      end
    end
    trainer_ids
  end
end
