class Feedback::AiAnalysisController < ApplicationController
  before_action :set_parameters

  def show
    result = find_or_create_result
    render_result(result)
  end

  def poll
    result = find_existing_result
    render_result(result)
  end

  def refresh
    # Start a new analysis job with force_refresh flag
    FeedbackAiAnalysisJob.perform_later(@survey_id, @month, @year, @type, force_refresh: true)
    
    # Return processing status
    render json: { status: 'processing' }
  end

  private

  def set_parameters
    @survey_id = params[:id]
    @month = params[:month]
    @year = params[:year]
    @type = params[:type]&.to_sym || :hotel
  end

  def find_existing_result
    FeedbackAiResult.find_by(
      survey_id: @survey_id,
      month: @month,
      year: @year,
      result_type: @type
    )
  end

  def find_or_create_result
    result = find_existing_result

    unless result
      # Start the background job
      FeedbackAiAnalysisJob.perform_later(@survey_id, @month, @year, @type)
      return nil
    end

    result
  end

  def render_result(result)
    if result.nil?
      render json: { status: 'processing' }
    else
      case result.status
      when 'completed'
        render json: JSON.parse(result.result)
      when 'failed'
        render json: { error: result.error_message }, status: :unprocessable_entity
      else
        render json: { status: 'processing' }
      end
    end
  end
end
