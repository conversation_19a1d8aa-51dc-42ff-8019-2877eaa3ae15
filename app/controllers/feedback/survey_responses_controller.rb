class Feedback::SurveyResponsesController < Feedback::FeedbackAppController

  skip_before_action :authenticate_user!, only: [:do_survey, :completed_survey, :create]
  before_action :set_cache_headers
  layout 'survey', only: [:do_survey, :completed_survey]

  def create
    response_params = params.require(:survey_response).permit(:name, :survey_id, :program_id, :email, :room_number, :trainer_id, :sustenance_only, :feedback_multi_id, answers_attributes: [:id, :question_id, :chosen_option, :comments])
    survey_response = SurveyResponse.new(response_params)

    # Handle custom question responses with support for multi-select
    if params[:custom_question_responses].present?
      custom_responses = {}
      params[:custom_question_responses].each do |question_id, response_value|
        if response_value.is_a?(Array)
          # Multi-select: remove empty strings and store as array
          custom_responses[question_id] = response_value.reject(&:blank?)
        else
          # Single select: store as string
          custom_responses[question_id] = response_value
        end
      end
      survey_response.custom_question_responses = custom_responses
    end

    # Validate required custom questions
    survey = Survey.find(response_params[:survey_id])
    validation_errors = validate_required_custom_questions(survey, survey_response.custom_question_responses)

    if validation_errors.any?
      flash[:error] = validation_errors.join(', ')
      redirect_back fallback_location: root_path
      return
    end

    if survey_response.save
      # Mark survey response as completed
      completion_time = Time.current
      survey_response.update_column(:completed_at, completion_time)

      # Update the associated booking's survey completion timestamp
      if survey_response.booking_id.present?
        survey_response.booking.update_column(:survey_completed_at, completion_time)
      end

      redirect_to feedback_completed_survey_path
    else
      flash[:error] = survey_response.errors.messages[:'answers.chosen_option']
      redirect_back fallback_location: root_path
    end
  end

  def do_survey
    # THIS IS THE ENTRY POINT FOR THE QUESTIONNAIRE LINKS ALL OTHERS TO HAVE SECURITY AS NORMAL
    @survey = Survey.find_by(token: params[:token])

    course = RfqRequest.find_by(id: @survey.rfq_request_id)
    if course && course.end_date < Date.today
      @survey.invalidated = true
    end

    redirect_to '/404.html' unless @survey
  end

  def completed_survey
    # Placeholder for static screen
  end

  private

  def validate_required_custom_questions(survey, custom_question_responses)
    errors = []

    survey.custom_questions.where(required: true).each do |required_question|
      response_value = custom_question_responses&.dig(required_question.id.to_s)

      if required_question.multi_select?
        # For multi-select, check if array is empty or nil
        if response_value.blank? || (response_value.is_a?(Array) && response_value.reject(&:blank?).empty?)
          errors << "#{required_question.title} is required"
        end
      else
        # For single select, check if value is blank
        if response_value.blank?
          errors << "#{required_question.title} is required"
        end
      end
    end

    errors
  end

  def set_cache_headers
    response.headers["Cache-Control"] = "no-cache, no-store, max-age=0, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "Mon, 01 Jan 1990 00:00:00 GMT"
  end

end
