class StripeCardPayment
  require 'securerandom'

  def create_payment_intent(booking, amount, currency)
    connected_account_details = use_connected_account(booking)
    person = booking.acc_booking_person.rfq_learner.present? ? booking.acc_booking_person.rfq_learner : booking.acc_booking_person.rfq_trainer
    stripe_options = {
      currency: currency,
      description: "Apprenticestop Charge for #{person&.first_initial_surname}, #{person.rfq_business_unit.name}, Booking: #{booking.id}.",
      setup_future_usage: 'off_session',
      automatic_payment_methods: {enabled: true},
      amount: (amount * 100).round,
      payment_method_options: {
         bacs_debit: {
           mandate_options: {
             reference_prefix: booking.id.to_s + '_',  # Custom BACS reference (booking_id)
           }
         }
      },
      metadata: {
        booking_id: booking.id,
        payment_type: 'card',
        booking_type: 'apprentice_stop',
        hotel_name: booking&.hotel&.name,
        date_of_stay: "#{booking.first_checkin} - #{booking.last_check_out}",
        client_name: booking.acc_booking_header.rfq_location.rfq_request.rfq_programme.client.name,
        guest_name: person&.first_initial_surname
      }
    }
    stripe_options[:metadata] = stripe_options[:metadata].merge({environment: "#{Rails.env}"}) if !Rails.env.production?
    stripe_options[:description] += " This payment intent was initialised on #{Rails.env}" if !Rails.env.production?
    stripe_options[:customer] = get_customer_id(booking, connected_account_details)


    options_hash = {idempotency_key: SecureRandom.uuid}
    options_hash = options_hash.merge(connected_account_details)
    begin
      Stripe::PaymentIntent.create(stripe_options, options_hash)
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      Rollbar.error("StripeCardPayment.createPaymentIntent issue: #{e.error.code} - #{payment_intent.id}")
      puts payment_intent.id
    end
  end

  def additional_booking_charge(booking, amount, currency)
    amount_in_pence = (amount * 100).to_i
    person = booking.acc_booking_person.rfq_learner.present? ? booking.acc_booking_person.rfq_learner : booking.acc_booking_person.rfq_trainer
    bu = person.rfq_business_unit.name
    description = "Additional ApprenticeStop charge for #{person&.first_initial_surname}, #{bu}, Booking: #{booking.id}"
    description += " This payment intent was initialised on #{Rails.env}" if !Rails.env.production?
    connected_account_details = use_connected_account(booking, booking.booking_payments.charged.first.stripe_connect_payment)
    customer = get_customer_id(booking, connected_account_details)
    payment_method = get_payment_method(booking, connected_account_details)
    metadata = {
      booking_id: booking.id,
      payment_type: 'card',
      booking_type: 'apprentice_stop'
    }
    metadata = metadata.merge({environment: "#{Rails.env}"}) if !Rails.env.production?
    begin
      intent = Stripe::PaymentIntent.create({
        amount: amount_in_pence,
        currency: currency,
        customer: customer,
        description: description,
        payment_method: payment_method,
        metadata: metadata,
        off_session: true,
        confirm: true,
      }, connected_account_details)
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      puts payment_intent.id
    end
  end

  def refund_payment(payment, booking, amount, reason)
    
    return if (amount == 0 || payment.stripe_charge_id.nil?)
    connected_account_details = use_connected_account(booking, payment.stripe_connect_payment)
    refund = Stripe::Refund.create(
      { payment_intent: payment.stripe_charge_id,
        reason: "requested_by_customer",
        amount: amount,
        metadata: {
          booking_type: 'apprentice_hub'
        }
      }, connected_account_details)

    booking_payment = booking.booking_payments.create(
      amount: -refund.amount,
      stripe_refund_id: refund.id,
      refund_reason: reason,
      :cp_id => payment.stripe_charge_id,
      status: 1,
      :company_reg_number =>  booking&.hotel&.company_reg_number,
      :company_vat_number =>  booking&.hotel&.company_vat_number,
      :company_reg_address => booking&.hotel&.company_reg_address,
      :stripe_connect_payment => connected_account_details != {}
    )

    # if fully refunded
    if Stripe::Charge.retrieve({id: refund.charge}, connected_account_details).refunded
      booking.update(refunded: true, refund_requested: false, refund_required: false)
    end
    payment_id = connected_account_details != {} ? booking_payment.id : nil
    AccBookingMailer.delay.booking_payment_receipt(booking.id, payment_id)
  end

  def get_customer_id(booking, connected_account_details = {})
    person = booking.acc_booking_person.rfq_learner.present? ? booking.acc_booking_person.rfq_learner : booking.acc_booking_person.rfq_trainer
    email = person.manager_email
    organisation = "ID: #{person.rfq_programme.client.id}. Name: #{person.rfq_programme.client.name}"
    bu = "ID: #{person.rfq_business_unit.id}. Name: #{person.rfq_business_unit.name}"
    description = "Manager at Organisation #{organisation}, Business Unit #{bu}"
    # should only be one customer per email
    customer = Stripe::Customer.list({email: email}, connected_account_details).first
    if customer.nil?
      customer = Stripe::Customer.create({
        email: email,
        description: description,
        name: person.manager,
        metadata: {
          organisation: organisation,
          business_unit: bu,
        }
      },
      connected_account_details)
    end
    return customer.id
  end

  def get_payment_method(booking, connected_account_details = {})
    # account_id = booking.hotel.stripe_detail.stripe_user_id
    customer = get_customer_id(booking, connected_account_details)
    payment_method = nil
    paymentable_type = booking.class.name
    payment_method = StripePayment.where(paymentable_id: booking.id, paymentable_type: paymentable_type).first.try(:payment_method_id)
    # so in theory this should only be used on the first payment made on a booking, the initial confirmation
    # after that the payment method should be stored in the DB, so it will default to that one.
    # I use last here to get the last one recorded into stripe, which in theory should be the latest one used
    if payment_method.nil? && customer.present?
      payment_method = Stripe::PaymentMethod.list({customer: customer, type: 'card'}, connected_account_details).first.id
      if payment_method.nil?
        return nil
      end
    end
    return payment_method
  end

  def refund_all_payments(booking)
    booking.booking_payments.charged.each do |payment|
      # all payment values should be processed in pence
      charges = Stripe::Charge.list({payment_intent: payment.stripe_charge_id}, StripeCardPayment.new.use_connected_account(booking, payment.stripe_connect_payment))
      total_charged = charges.sum(&:amount)

      total_remaining_charge = (charges.sum { |c| c.amount - c.amount_refunded} )
      remaining_cost_on_booking_excluding_stripe = (total_remaining_charge - booking.extract_stripe_plus_vat_from_cost(total_charged))

      refund_amount = if payment.stripe_connect_payment
        total_remaining_charge
      else
        # only refund room rate + subsistence if payment to servace is enabled, we keep stripe fees and VAT
        remaining_cost_on_booking_excluding_stripe
      end
      # So this is complicated, if a booking has a partial refund already there will be some amount left over on the charge, and we don't
      # want that to be refunded a second time, so this should take the total paid and extract the stripe + vat charge
      # and compare it to the amount of  money left on the payment intent, if they are equal this payment is already fully refunded
      if total_remaining_charge != booking.extract_stripe_plus_vat_from_cost(total_charged)
        refund_payment(payment, booking, (refund_amount), "System refunded due to cancellation")
      end
    end
  end

  def update_payment_type(payment_intent_id, payment_type, booking_id)
    booking = AccBooking.find(booking_id)
    Stripe::PaymentIntent.update(
      payment_intent_id,
      {metadata: {payment_type: payment_type}}, use_connected_account(booking),
    )
  end

  def use_connected_account(booking, payment_connect_status = nil)
    out = {}
    use_connect = booking.acc_booking_header.rfq_location.rfq_request.use_stripe_connect
    use_connect = payment_connect_status if payment_connect_status.present?
    if use_connect
      hotel = booking.hotel || booking.acc_booking_header.rfq_location.accepted_response.hotel
      if hotel.stripe_detail.present?
        if Stripe::Account.retrieve(hotel.stripe_detail.stripe_user_id, api_key: ENV['STRIPE_CONNECT_SECRET']) #check to see if the account still exists on stripe as opposed to just our DB
          account_id = hotel.stripe_detail.stripe_user_id
          out = { stripe_account: account_id, api_key: ENV['STRIPE_CONNECT_SECRET']}
        end
      end
    end
    out
  end
end
