class AcademyStripeCardPayment
  require 'securerandom'

  def additional_booking_charge(booking, amount)
    description = "Additional Academy Hub charge for #{booking.booker_full_name}, Booking: #{booking.id}"
    description += " This payment intent was initialised on #{Rails.env}" if !Rails.env.production?
    customer = get_customer_id(booking)
    payment_method = get_payment_method(booking)
    metadata = {
      booking_id: booking.id,
      payment_type: 'card',
      booking_type: 'academy_hub'
    }
    metadata = metadata.merge({environment: "#{Rails.env}"}) if !Rails.env.production?
    begin
      intent = Stripe::PaymentIntent.create({
        amount: amount.to_i,
        capture_method: booking.confirmed_at? ? 'automatic' : 'manual',
        currency: 'gbp',
        customer: customer,
        description: description,
        payment_method: payment_method,
        metadata: metadata,
        off_session: true,
        confirm: true,
      }, {api_key: ENV['STRIPE_ACADEMY_SECRET']})
    rescue Stripe::CardError => e
      # Error code will be authentication_required if authentication is needed
      puts "Error is: \#{e.error.code}"
      payment_intent_id = e.error.payment_intent.id
      payment_intent = Stripe::PaymentIntent.retrieve(payment_intent_id)
      puts payment_intent.id
    end
  end

  def refund_payment(payment, booking, amount, reason)
    
    return if (amount == 0 || payment.stripe_charge_id.nil?)
    refund = Stripe::Refund.create(
      { payment_intent: payment.stripe_charge_id,
        reason: "requested_by_customer",
        amount: amount,
        metadata: {
          booking_id: booking.id,
          booking_type: 'academy_hub'
        }
      }, {api_key: ENV['STRIPE_ACADEMY_SECRET']})

    booking_payment = booking.booking_payments.create(
      amount: -refund.amount,
      stripe_refund_id: refund.id,
      refund_reason: reason,
      :cp_id => payment.stripe_charge_id,
      status: 1,
      :company_reg_number =>  payment.company_reg_number,
      :company_vat_number =>  payment.company_vat_number,
      :company_reg_address => payment.company_reg_address
   )

    payment_id = booking_payment.id
    AccBookingMailer.delay.booking_payment_receipt(booking.id, payment_id)
  end

  def refund_all_payments(booking)
    booking.booking_payments.charged.each do |payment|
      # all payment values should be processed in pence
      charges = Stripe::Charge.list({payment_intent: payment.stripe_charge_id}, StripeCardPayment.new.use_connected_account(booking, payment.stripe_connect_payment))
      total_charged = charges.sum(&:amount)

      total_remaining_charge = (charges.sum { |c| c.amount - c.amount_refunded} )
      remaining_cost_on_booking_excluding_stripe = (total_remaining_charge - booking.extract_stripe_plus_vat_from_cost(total_charged))

      refund_amount = if payment.stripe_connect_payment
        total_remaining_charge
      else
        # only refund room rate + subsistence if payment to servace is enabled, we keep stripe fees and VAT
        remaining_cost_on_booking_excluding_stripe
      end
      # So this is complicated, if a booking has a partial refund already there will be some amount left over on the charge, and we don't
      # want that to be refunded a second time, so this should take the total paid and extract the stripe + vat charge
      # and compare it to the amount of  money left on the payment intent, if they are equal this payment is already fully refunded
      if total_remaining_charge != booking.extract_stripe_plus_vat_from_cost(total_charged)
        refund_payment(payment, booking, (refund_amount), "System refunded due to cancellation")
      end
    end
  end

  def get_customer_id(booking)
    booking = Booking.find(booking) if booking.is_a?(Integer) || booking.is_a?(String)
    organisation = "ID: #{booking.client_id}. Name: #{booking.client.name}"
    description = "Manager at Organisation #{organisation}"
    # should only be one customer per email
    customer = Stripe::Customer.list({email: booking.booker_email}, {api_key: ENV['STRIPE_ACADEMY_SECRET']}).first
    if customer.nil?
      customer = Stripe::Customer.create({
        email: booking.booker_email,
        description: description,
        name: booking.booker_full_name,
        metadata: {
          organisation: organisation
        }
      }, {api_key: ENV['STRIPE_ACADEMY_SECRET']},
      )
    end
    return customer.id
  end

  def get_payment_method(booking)
    # account_id = booking.hotel.stripe_detail.stripe_user_id
    customer = get_customer_id(booking)
    payment_method = nil
    paymentable_type = booking.class.name
    payment_method = StripePayment.where(paymentable_id: booking.id, paymentable_type: paymentable_type).first.try(:payment_method_id)
    # so in theory this should only be used on the first payment made on a booking, the initial confirmation
    # after that the payment method should be stored in the DB, so it will default to that one.
    # I use last here to get the last one recorded into stripe, which in theory should be the latest one used
    if payment_method.nil? && customer.present?
      payment_method = Stripe::PaymentMethod.list({customer: customer, type: 'card'}, {api_key: ENV['STRIPE_ACADEMY_SECRET']}).first.id
      if payment_method.nil?
        return nil
      end
    end
    return payment_method
  end

  def attach_payment_method_to_customer(booking)
    customer = get_customer_id(booking)
    payment_method = get_payment_method(booking)

    if payment_method.nil?
      raise "No payment method found for booking #{booking.id}"
    end
    Stripe::PaymentMethod.attach(payment_method, {customer: customer}, {api_key: ENV['STRIPE_ACADEMY_SECRET']})
  rescue Stripe::StripeError => e
    Rails.logger.error("Stripe Error: #{e.message}")
    raise e
  end

  def update_payment_type(payment_intent_id, payment_type, booking_id)
    booking = AccBooking.find(booking_id)
    Stripe::PaymentIntent.update(
      payment_intent_id,
      {metadata: {payment_type: payment_type}}, use_connected_account(booking),
    )
  end

end
