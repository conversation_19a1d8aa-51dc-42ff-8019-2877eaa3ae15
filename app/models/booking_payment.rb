class BookingPayment < StripePayment

  belongs_to :paymentable, polymorphic: true
  audited  :associated_with => :paymentable

  has_many :customer_payments, :class_name => "BookingPayment", :foreign_key => 'cp_id'
  belongs_to :customer_payment, :class_name => "BookingPayment", :foreign_key => 'cp_id'

  after_commit :recalc_total_paid 

  scope :charged, -> {where(stripe_payments: {status: [0,2]})}
  scope :refunded, -> {where("stripe_payments.status=1")}
  scope :pending, -> {where("stripe_payments.status=2")}
  scope :failed_payments, -> {where("stripe_payments.status=3")}

  def refundable
    refunded = customer_payments.sum(:amount)
    amount + refunded  #refunded negative
  end

  def recalc_total_paid
    return if self.paymentable_type != 'AccBooking'
    AccBooking.find(self.paymentable_id).recalc_total_paid!
  end

end
