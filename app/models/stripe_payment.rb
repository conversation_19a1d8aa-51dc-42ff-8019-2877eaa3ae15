class StripePayment < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')
  belongs_to :hotel
  # belongs_to :acc_booking
  belongs_to :paymentable, polymorphic: true
  # NB amount is now an integer as stripe amount is in pence
  enum :status, {
    paid: 0,
    refund: 1,
    pending: 2, # Added by ES for Stripe Bacs (3/9/23)
    failed: 3
  }

  enum :payment_type, {
    card: 0,
    bacs_debit: 1,
    credit_account: 2
  }

  scope :apprentice_payments, -> { where(paymentable_type: 'AccBooking') }

  def getBalanceTransaction(payment_intent, connected_account_data)
    attempts = 0
    begin
      Stripe::Charge.retrieve({ id: payment_intent.latest_charge, expand: ['balance_transaction'] },
                              connected_account_data)&.balance_transaction
    rescue Exception
      # had to do this because of a RACE condition where the balance transaction was not available immediately
      attempts += 1
      retry unless attempts > 5
      exit(-1)
    end
  end

  def self.calculate_stripe_fee_plus_vat(amount)
    stripe_fee = STRIPE_FEE_RATE_PERCENT.percent_of(amount)
    vat_on_stripe_fee = VAT_RATE.percent_of(stripe_fee)
    stripe_fee + vat_on_stripe_fee
  end

  def self.amount_minus_stipe_fee_w_vat(amount)
    amount - calculate_stripe_fee_plus_vat(amount)
  end
end
