class Booking < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')
  audited
  # Associations
  belongs_to :hotel
  belongs_to :rfq_location
  belongs_to :client, :foreign_key => "client_id", :class_name => "Organisation"
  belongs_to :confirmed_by, class_name: "User", optional: true
  belongs_to :hotel_confirmed_by, class_name: "User", optional: true
  belongs_to :hotel_declined_by, class_name: "User", optional: true
  belongs_to :cancelled_by, class_name: "User", optional: true
  has_many :booking_attendees
  has_many :booking_payments, as: :paymentable

  # before_save :strip_timezone_from_time
  after_update :recalculate_total_nights
  after_update :recalculate_total_cost

  # Validations
  validates :tcs_acceptance, inclusion: { in: [true], message: "You must accept the terms and conditions to proceed with the booking." }
  validates :total_cost, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  enum :booking_type, { academy_hub: 0, apprentice_hub: 1 }

  def recalculate_total_nights
    if self.check_in.present? && self.check_out.present?
      update_columns(total_nights: (self.check_out.to_date - self.check_in.to_date).to_i)
    else
      update_column(total_nights: 0)
    end
  end

  def recalculate_total_cost(recalculate_attendee_cost = true)
    if recalculate_attendee_cost
      self.booking_attendees.not_cancelled.each do |attendee|
        attendee.recalculate_total_cost(false) # recalculate attendee cost without recalculating booking cost
      end
    end
    before_cost = self.total_cost
    update_columns(total_cost: (self.booking_attendees.not_cancelled.sum { |attendee| attendee.total_cost.to_i }))

    amount = self.total_cost - before_cost
    if self.payment_method == 'Card' && amount != 0
      if amount.positive?
        academy_stripe_charge(amount.to_i) 
      elsif amount.negative?
        if self.confirmed_at? && self.eligible_for_refund?
          academy_stripe_refund(-amount.to_i)
        else # if booking is not confirmed then cancel all PIs and regenerate it as new amount
          self.booking_payments.charged.each do |payment|
            Stripe::PaymentIntent.cancel(payment.stripe_charge_id, {}, { api_key: ENV['STRIPE_ACADEMY_SECRET'] })
            payment.update(deleted_at: DateTime.now) # mark the payment as deleted
          end
          academy_stripe_charge(self.total_cost.to_i) # re-charge the booking with the new total cost
        end
      end

    end
  end

  def booker_full_name
    self.booker_forename + " " + self.booker_surname
  end

  def confirmed_by_name
    self.confirmed_by.try(:name)
  end

  def hotel_confirmed_by_name
    self.hotel_confirmed_by.try(:name)
  end

  def hotel_declined_by_name
    self.hotel_declined_by.try(:name)
  end

  def cancelled_by_name
    self.cancelled_by.try(:name)
  end

  # Only refund a singular booking if the datetime is before first checkin 4pm
  def eligible_for_refund?
    DateTime.now.before?(self&.check_in&.to_datetime&.change({ hour: 16 }))
  end

  def attendees_name_list
    if self.booking_attendees.length == 1
      return self.booking_attendees.first.forename.humanize + ' ' + self.booking_attendees.first.surname.humanize
    elsif self.booking_attendees.length == 0
      return "No attendees"
    else
      return self.booking_attendees.map {|ba| ba.forename.humanize + ' ' + ba.surname.humanize }.join(", ")
    end
  end

  def get_accepted_response
    self.rfq_location.accepted_responses.where(rfq_proposed_hotel_id: self.hotel.rfq_proposed_hotels.pluck(:id)).first
  end

  # def strip_timezone_from_time
  #   attributes = [:check_in, :check_out, :hotel_confirmed_at, :hotel_declined_at, :confirmed_at, :cancelled_at, :amended_at, :deleted_at, :created_at, :updated_at]
  #   attributes.each do |attribute|
  #     if self[attribute].is_a?(Time) || self[attribute].is_a?(ActiveSupport::TimeWithZone) || self[attribute].is_a?(DateTime)
  #       self[attribute] = self[attribute].to_date.in_time_zone("UTC").to_datetime.change(offset: "+00:00")
  #       # self.update(attribute => self[attribute].to_datetime.change(offset: "+00:00"))
  #     end
  #   end
  # end

  def total_cost_in_pounds
    self.total_cost.to_f / 100.0
  end

  def self.academy_booking_pay_types(rfq_request)
    out = []
    if rfq_request.card_pay?
      out << 'Card'
    elsif rfq_request.payment_method == 'HG to Pay'
      out << 'HG to Pay'
    else
      out << 'BACS / Credit Card (Paid to Hotel)'
    end
  end

  def academy_stripe_charge(amount)
    return false if amount <= 0
    begin
      AcademyStripeCardPayment.new.additional_booking_charge(self, amount)
    rescue Stripe::CardError => e
      Rails.logger.error("Stripe charge failed for booking #{self.id}: #{e.message}")
    end
  end

  def academy_stripe_refund(amount)
    return false if amount <= 0
    begin
      if self.booking_payments.any?
        if self.confirmed_at? && self.eligible_for_refund? 
          payment_to_refund = self.booking_payments.charged.where('amount > ?', amount).min
          raise "Payment to refund not found or insufficient amount" if payment_to_refund.nil? || payment_to_refund.amount < amount
          AcademyStripeCardPayment.new.refund_payment(payment_to_refund, self, amount, "Refund for booking #{self.id}")          
        end
      end
    rescue Stripe::CardError => e
      Rails.logger.error("Stripe refund failed for booking #{self.id}: #{e.message}")
    end
  end


  def calculate_servace_stripe_fee_total(total)
    STRIPE_FEE_RATE_PERCENT.percent_of(total).round
  end

  def calculate_vat_fee_total(total)
    VAT_RATE.percent_of(total).round
  end

  def calculate_stripe_with_vat(total)
    stripe_fee = calculate_servace_stripe_fee_total(total)
    vat = calculate_vat_fee_total(stripe_fee)
    (stripe_fee + vat).round
  end

end