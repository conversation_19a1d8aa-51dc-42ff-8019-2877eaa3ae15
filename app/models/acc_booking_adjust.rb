class AccBookingAdjust < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')
  attr_accessor :confirm_adjust, :processing_adjust, :other_reason

  before_create :set_start_stays
  after_create  :arrive_early, if: proc { |x| x.early? }

  belongs_to :acc_booking

  def self.early
    where(category: 'EARLY')
  end

  def self.non_arrivals
    where(category: 'NON')
  end

  validate :booking_present
  def booking_present
    errors.add(:base, 'Booking needs to be present') unless acc_booking.present?
  end

  validate :booking_not_cancelled
  def booking_not_cancelled
    left_at = acc_booking&.acc_booking_person&.rfq_learner&.left_at
    cancelled_at = acc_booking&.cancelled_at
    return if left_at.present? && cancelled_at.present? && left_at < cancelled_at

    errors.add(:base, 'Booking is cancelled - cannot be non or early ') if cancelled_at
  end

  validate :confirmed, on: :create
  def confirmed
    errors.add(:confirm_adjust, 'Please confirm!') unless confirm_adjust == '1'
  end

  validate :check_processing_fields, on: :update, if: proc { |x| x.processing_adjust == true }
  def check_processing_fields
    errors.add(:action, 'Please select an action') if action.blank?
    errors.add(:reason, 'Please enter reason') if reason.blank?
    return unless reason == 'Other' && other_reason.empty?

    errors.add(:other_reason, 'Please enter reason')
  end

  validate :max_non_arr_of_one_per_day, on: :create
  def max_non_arr_of_one_per_day
    b = acc_booking.acc_booking_adjusts.where('category = ? and DATE(created_at) = ?', 'NON', Date.today).first
    return unless b.present?

    errors.add(:base,
               'Booking already has a non arrival for today, cannot set more than one non-arrival per booking on the same day')
  end

  def set_start_stays
    return unless acc_booking.present?

    self.before_stays = acc_booking.stays_as_array
  end

  def arrive_early
    raise 'Not early checkin' unless early?

    booking = acc_booking
    self.before_stays = booking.stays_as_array

    block_start_date = additional_night.beginning_of_week(:sunday)

    unless week_to_adjust = booking.acc_week_blocks.where(start_date: block_start_date).first
      week_to_adjust = booking.acc_week_blocks.new
      week_to_adjust.start_date = block_start_date
    end

    week_to_adjust.set_date_to(additional_night, true)
    week_to_adjust.save!

    # set editing to true to bypass blackout date check
    booking.reset_stays(true)
    booking.reload

    self.after_stays = booking.stays_as_array
    save!
  end

  def early?
    category == 'EARLY'
  end

  def non?
    category == 'NON'
  end

  def nice_type
    if action == 'UNCONFIRMED'
      if early?
        'Early arrival (loaded in error)'
      else
        'Non arrival (loaded in error)'
      end
    elsif early?
      'Early arrival'
    else
      'Non arrival'
    end
  end

  def nice_action(text_for_client_manager_email = false)
    case action
    when 'FULL'
      'Booking Required and learner will require rest of stay. ( Full stay chargeable )'
    when 'CANCEL_ALL'
      if text_for_client_manager_email
        "if charges are required to be settled by you a pro-forma invoice will be attached to this email.
            If payment is normally made by your training provider this may still apply and they will be in touch to discuss where necessary"
      else
        'Booking not required, cancel rest of the stay ( One night chargeable )'
      end
    when 'CANCEL_STAY'
      'This week not required but future weeks are ( One night chargeable and future weeks)'
    when 'UNCONFIRMED'
      'This week is required and no changes needed, learner did stay - loaded in error'
    end
  end

  def hotel
    acc_booking.try(:hotel)
  end

  def additional_night
    if early?
      check_in - 1
    else
      nil
    end
  end

  def confirmed?
    confirmed_at.present?
  end

  def not_required?
    action == 'UNCONFIRMED'
  end

  def adjust_associated_blocks(user)
    raise "Incorrect method" unless ["FULL","CANCEL_STAY", "CANCEL_ALL", "UNCONFIRMED"].include? self.action
    booking = self.acc_booking
    @booking_cost_before_reset = booking.room_rate_plus_fees(false)

    if ["FULL", "UNCONFIRMED"].exclude? self.action
      if self.action == "CANCEL_ALL"
        clear_future_weeks
      end
      if self.action == "CANCEL_STAY"
        clear_first_week
      end
      non_arrival_one_night_charge

      booking.reset_stays(true)
      booking.reload
      self.after_stays = booking.stays_as_array
      #supplier refund card payment?
      if booking.card_pay? && booking.booking_payments.any? && booking.payment_method != 'credit account'
        payment = StripeCardPayment.new
        # recalculate the booking cost as it is now 1 night, then do not refund the fees they've already paid if pay to servace
        # if they've paid via connect then we need to refund the full amount
        refund_amount = (@booking_cost_before_reset - booking.pppn_inc_vat).to_pence
        charge_to_refund  = booking.booking_payments.charged.where('amount > ?', refund_amount).min
        payment.refund_payment(charge_to_refund, booking, refund_amount, "Non Arrival")
      end
    end

    self.confirmed_at = Time.zone.now
    self.confirmed_by = user.contact.full_name
    save!
    send_hotel_email
  end

  def non_arrival_one_night_charge
    raise 'Incorrect method' unless %w[CANCEL_STAY CANCEL_ALL].include? action

    weeks = acc_booking.booking_dates.present? ? acc_booking.booking_dates : acc_booking.acc_week_blocks
    accommodation_week = find_accommodation_week(weeks)
    earliest_day = before_stays.first['check_in'].to_date
    dates_to_reenable = [earliest_day.strftime('%a').downcase.to_sym,
                         (earliest_day + 1.day).strftime('%a').downcase.to_sym]
    dates_to_reenable.each { |date| accommodation_week.update(date => true) }
  end

  def find_accommodation_week(weeks)
    if weeks.first.class == BookingDate
      weeks.select { |week| week.week_type == BookingDate.week_types['accommodation'] }.first
    else
      weeks.first
    end
  end

  def clear_future_weeks
    raise 'Cant clear future unless category = CANCEL_ALL' unless action == 'CANCEL_ALL'

    weeks = acc_booking.booking_dates.present? ? acc_booking.booking_dates : acc_booking.acc_week_blocks
    return unless weeks.any?

    weeks.update_all(sun: false, mon: false, tue: false, wed: false, thu: false, fri: false, sat: false)
  end

  def clear_first_week
    raise 'Cant clear first week unless category = CANCEL_STAY' unless action == 'CANCEL_STAY'

    weeks = acc_booking.booking_dates.present? ? acc_booking.booking_dates : acc_booking.acc_week_blocks
    return unless weeks.any?

    weeks.first.update(sun: false, mon: false, tue: false, wed: false, thu: false, fri: false, sat: false)
  end

  def self.adjustments(opts = {})
    user = opts[:user]

    # THis may replace opts[:user] with opts[:user_id] as rails 8 seems to require it
    if opts[:user_id].present?
      user = User.find(opts[:user_id])
    end

    raise 'Must provide base user' unless user.present? && user.is_a?(User)

    start_date = opts[:start_date]
    end_date = opts[:end_date]
    hotel_id = opts[:hotel_id]
    client = opts[:client]
    programme_id = opts[:programme_id]
    outstanding = opts[:outstanding]
    hg_managed = opts[:hg_managed]
    category = opts[:category]
    exclude_errors = opts[:exclude_errors]

    raise 'Client ID must be provided' if !user.is_a_supplier? && (!user.is_an_administrator? && client.blank?)

    base = AccBookingAdjust
           .joins(acc_booking: { hotel: {}, acc_booking_person: :rfq_learner,
                                 acc_booking_header: { rfq_location: { rfq_request: { rfq_programme: :client } } } })
           .select("acc_booking_adjusts.*, rfq_learners.forename as forename, hotels.name as hotel_name,
                rfq_learners.surname as surname, rfq_programmes.name as prog_name, organisations.name as client_name")

    unless user.is_an_administrator?
      base = if user.is_a_supplier?
               base.where('acc_bookings.hotel_id in (?)', user.all_hotels.collect { |x| x.id })
             else
               base.where('rfq_programmes.id' => RfqProgramme.allowed_programmes_for(user.contact, nil, 'Apprentice'))
             end

    end

    base = base.where('acc_booking_adjusts.check_in >= ?', start_date) if start_date.present?

    base = base.where('acc_booking_adjusts.check_in <= ?', end_date) if end_date.present?

    base = base.where('organisations.id = ?', client) if client.present?

    base = base.where('rfq_programmes.id = ?', programme_id) if programme_id.present?

    base = base.non_arrivals.where(confirmed_at: nil) if outstanding
    base = base.where("organisations.apprentice_contract_level <> 'SELF'") if hg_managed
    base = base.where('hotels.id = ? ', hotel_id) if hotel_id.present?
    base = base.where('acc_booking_adjusts.category = ? ', category) if category.present?
    base = base.where("acc_booking_adjusts.action <> 'UNCONFIRMED' ") if exclude_errors

    base.order('acc_booking_adjusts.id')
  end

  def self.possible_clients
    Organisation.joins(rfq_programmes: { rfq_requests: { rfq_locations: { acc_booking_headers: { acc_bookings: :acc_booking_adjusts } } } }).distinct
  end

  def self.possible_programmes(client_id = nil, current_user = nil)
    if client_id.present?
      base = RfqProgramme.joins(rfq_requests: { rfq_locations: { acc_booking_headers: { acc_bookings: :acc_booking_adjusts } } }).distinct
      progs = base.where('rfq_programmes.client_id = ?', client_id)
      if current_user.present?
        progs_for_client =  RfqProgramme.allowed_programmes_for(current_user.contact)
        progs = progs.where('rfq_programmes.id in (?)', progs_for_client) if progs_for_client.any?
      end
    else
      progs = []
    end
    progs
  end

  def send_hotel_email
    NonArrivalsMailer.delay.hotel_non_arrival_processed(id)
  end

  def non_arrival_reasons
    reasons = [
      'Learner did not use Sunday due to personal reasons but requires Sundays in future.',
      'Learner did not use Sunday due to personal reasons and does not require Sundays in future.',
      'Sunday should not be added to booking.',
      'Manager added Sunday but was not used.',
      'Learners requires future Sundays but did not use this week only.',
      'Learner on Annual Leave',
      'Sunday booked in error',
      'All dates booked in error',
      'Learner not attending due to personal issues',
      'Isolating - Unable to attend',
      'Illness - Unable to attend',
      'Leaver - Unable to attend',
      'Course cancelled - Booking not updated',
      'Learner already completed training',
      'Booked in error',
      'Transport Issue',
      'No contact - Unable to make contact with learner'
    ]
    reasons << 'Other' if acc_booking.acc_booking_header.rfq_location.rfq_request.non_arrival_other_reason_enabled
  end
end
