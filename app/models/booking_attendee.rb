class BookingAttendee < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')
  audited associated_with: :booking
  has_associated_audits # for the related data
  # Associations
  belongs_to :booking
  belongs_to :rfq_response_room
  belongs_to :acc_booking_person
  belongs_to :user
  belongs_to :rfq_business_unit, optional: true

  # Validations
  validates :total_cost, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  after_update :recalculate_total_cost

  scope :not_cancelled, -> { where(cancelled_at: nil) }

  def recalculate_total_cost(recalculate_booking_cost = true)
    total_room_cost = (self.rfq_response_room.price_inc_vat.to_pence * self.booking.total_nights)
    total_stripe_cost = if self.booking.payment_method == 'Card'
      self.booking.calculate_stripe_with_vat(total_room_cost) 
    else
      0
    end
    total_cost = if self.cancelled_at.present?
      total_stripe_cost
    else
      total_room_cost + total_stripe_cost
    end

    self.update_column(:total_cost, total_cost)
    if recalculate_booking_cost
      self.booking.recalculate_total_cost
    end
  end

  def full_name
    "#{acc_booking_person.forename} #{acc_booking_person.surname}"
  end 

end