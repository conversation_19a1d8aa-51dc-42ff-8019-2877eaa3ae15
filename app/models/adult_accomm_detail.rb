class AdultAccommDetail < ApplicationRecord

 extend Dragonfly::Model

 has_one_attached :partner_logo
 has_one_attached :welcome_image

  dragonfly_accessor :logo do
    after_assign :resize_image
  end

  dragonfly_accessor :header_bg do
    after_assign :resize_bg_image
  end

  belongs_to :rfq_location

  def resize_image
    logo.process!(:thumb, "300x200")
  end

  def resize_bg_image
    header_bg.process!(:thumb, "1100x400")
  end

  def set_file_names(fname, &block)
     if block_given?
       accessor = block.call
     end
     puts accessor
     case accessor
     when :logo
       self.logo.name = fname
     when :header_bg
       self.header_bg.name = fname
     else
       raise 'no accessor passed'
     end
  end


  def name
    self.try(:rfq_location).try(:name)
  end

end
