class Contact < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')

  EVENT_FREQ = ['', 'Weekly', 'Monthly', 'Quarterly', 'Bi-Annually', 'Biennially', 'Rarely']

  before_destroy :check_deletable
  before_validation :downcase_email
  before_save :mkt_option
  after_validation :sync_email

  belongs_to :primary_location, foreign_key: :location_id, class_name: 'Location'
  belongs_to :parent, polymorphic: true
  has_one :organisation, as: :parent
  belongs_to :preferred_region_1, foreign_key: :preferred_region_1_id, class_name: Region.name
  belongs_to :preferred_region_2, foreign_key: :preferred_region_2_id, class_name: Region.name
  belongs_to :preferred_region_3, foreign_key: :preferred_region_3_id, class_name: Region.name
  belongs_to :offer_type
  belongs_to :legal_entity
  has_one :greggs_contact

  has_many :hotel_as_ccs, foreign_key: :conference_contact_id, class_name: 'Hotel'
  has_many :opportunities, foreign_key: :main_contact_id

  has_one :user, dependent: :destroy

  has_many :comments, as: :commentable, dependent: :destroy
  has_many :tasks, as: :asset, dependent: :destroy
  has_many :attachments, as: :parent, dependent: :destroy
  has_many :assigned_tasks, foreign_key: :assigned_to_id, class_name: Task.name
  has_many :rfq_roles, dependent: :destroy
  has_many :team_memberships, dependent: :destroy
  has_many :teams, through: :team_memberships
  has_many :rfq_programmes, through: :rfq_roles
  has_one :business_unit_manager, class_name: 'BusinessUnit', foreign_key: :manager_id

  has_one :business_unit, foreign_key: :manager_id, class_name: 'RfqBusinessUnit'

  audited associated_with: :organisation

  attr_accessor :also_a_user, :job_role, :dupe_contact_id,
                :left_date, :left_time, :mkt_opt_in, :team_id, :skip_dup_email_valid,
                :left, :user_mkt_opted_in_at_not_null

  validates :surname, presence: true
  validates_format_of :email, with: Devise.email_regexp # , :allow_blank => true, :if => :email_changed?
  validates_presence_of :telephone

  validate :email_unique, unless: :skip_dup_email_valid


  def self.ransackable_attributes(auth_object = nil)
    ["annual_event", "annual_event_date", "birthday", "cid", "cost_centre", "created_at", "creator_id", "deleted_at", "email", "event_book_freq", "first_name", "home_city", "id", "id_value", "invite_token", "job_department", "job_title", "left_at", "legal_entity_id", "location_id", "middle_name", "mkt_last_opted_in_at", "mkt_last_opted_out_at", "mkt_opted_in_at", "mobile_phone", "new_company", "notes", "offer_type_id", "organisation_id", "parent_id", "parent_type", "preferred_region_1_id", "preferred_region_2_id", "preferred_region_3_id", "salutation", "surname", "team", "telephone", "title", "updated_at", "updater_id"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["assigned_tasks", "attachments", "audits", "business_unit", "business_unit_manager", "comments", "greggs_contact", "hotel_as_ccs", "legal_entity", "offer_type", "opportunities", "parent", "preferred_region_1", "preferred_region_2", "preferred_region_3", "primary_location", "rfq_programmes", "rfq_roles", "tasks", "team_memberships", "teams", "user"]
  end

  def email_unique
    csame = Contact.current.where(email: email)
    csame = csame.where('contacts.id <> ? ', id) if id.present?

    usame = User.where('email = ? and contact_id <> ?', email, id)

    cs_emails = csame.map(&:email)
    usame = usame.reject { |u| u if cs_emails.include? u.email }
    return unless csame.any? || usame.any?

    errors.add(:base, 'Please ensure the contact email address is unique - other(s) have the same email.')
    # DONE 'is this not a security issue, as we are telling a potential hacker information about the users details'
    # Agreed - removed code - can show admin in different way
  end

  validate :correct_job_role, if: ->(x) { x.job_role.present? }
  def correct_job_role
    if parent.class == Hotel
      if job_role != 'HOTEL'
        errors.add(:base,
                   "job role must be 'HOTEL' for a hotel contact it is: #{job_role}")
      end
    elsif parent.class == Chain
      unless %w[
        CHAIN CLUSTER
      ].include?(job_role)
        errors.add(:job_role,
                   "job role for a Chain contact must be 'CLUSTER' or 'CHAIN', it is: '#{job_role}'")
      end
    end
  end

  accepts_nested_attributes_for :user
  accepts_nested_attributes_for :primary_location
  validates_associated :primary_location

  def self.deleted_ones_for(parent)
    where('deleted_at is not null').where(parent_type: parent.class.name).where(parent_id: parent.id)
  end

  def self.birthday_coming
    where("date_part('month', contacts.birthday) = ? ", (Date.today + 1.month).month)
      .where('contacts.birthday is not null')
  end

  def self.in_team
    joins(:teams)
  end

  def self.left
    where('left_at is not null')
  end

  def self.current
    where('left_at is null')
  end

  HG_TEAMS = ['ACE', 'Sales And Marketing', 'Apprentice', 'Accounts', 'Preferred Partners']

  def organisation
    parent
  end

  def active_package
    if parent.is_a?(Chain) || parent.is_a?(Hotel)
      parent.active_package
    else
      nil
    end
  end

  def left?
    left_at.present?
  end

  # allow show of email if (for imported data) the name is still blank
  def full_name
    leaver = left? ? left_string : ''
    [first_name, surname, leaver].reject(&:blank?).join(' ').to_s.html_safe
  end

  def full_name_or_email
    fn = full_name
    if fn.blank?
      return email unless email.blank?
    else
      fn = fn.titleize
    end
    fn
  end

  def full_name_and_parent
    out = full_name_or_email
    out << " - #{parent.name}" if parent.present?
    out
  end

  def loyalty_letter
    return ''
    case true
    when user.present? && user.joined_loyalty_scheme.present?
      '(L)'
    when user.present? && Recommendation.where(email: email).first.present? && first_opportunity?
      '(R)'
    else
      ''
    end
  end

  def full_name_with_salutation
    leaver = left? ? left_string : ''
    extra = salutation.blank? || salutation == first_name ? nil : "('#{salutation}')"
    fn = [title, first_name, surname, extra, leaver].reject(&:blank?).join(' ').html_safe
  end

  def initials
    [begin
      first_name[0]
    rescue StandardError
      nil
    end, surname[0]].compact.join.upcase
  end

  def first_phone
    return mobile_phone unless mobile_phone.blank?
    return telephone unless telephone.blank?

    primary_location.phone if primary_location
  end

  def le_info
    return 'No legal entity' if legal_entity.blank?

    legal_entity.name
  end

  def team_list
    teams.any? ? teams.map { |t| t.name }.to_sentence : ''
  end

  def business_unit_list
    organisation.business_units.any? ? organisation.business_units.map { |t| t.name }.to_sentence : ''
  end

  def sector_list
    organisation.sectors.any? ? teams.map { |t| t.name }.to_sentence : ''
  end

  def rfq_roles
    RfqRole.where(contact_id: id)
  end

  def deletable?
    if opportunities.count > 0
      errors[:base] << "Cannot delete -  has linked opportunities #{opportunities.collect { |o| o.id }}"
      return false
    end

    return true if parent.is_a?(Organisation) && parent.dupe_org.present?
    return true if parent.is_a?(Hotel)
    return true if parent.is_a?(Chain)
    return true if parent.nil?
    return false if organisation.present? && (organisation.try(:primary_contact).try(:id) == id)

    true
  end

  def short_name_for_task_list(opts = {})
    if opts[:with_org]
      "#{parent.name} : #{full_name[0, 40]} #{loyalty_letter}"
    else
      full_name[0, 40] + " #{loyalty_letter}"
    end
  end

  def to_s
    full_name
  end

  def birthday_for_show
    birthday.try(:strftime, '%-d %B')
  end

  def temp_password
    'A' + Digest::SHA1.hexdigest(created_at.to_i.to_s).slice(0..5) + '0z%'
  end

  def roles_for_admin_update
    case parent
    when Organisation.HG
      %w[ADMIN ADMIN_MANAGER]
    when Organisation
      if parent.legal_entities_enabled?
        %w[EXECUTIVE MANAGER TEAM BOOKER]
      else
        %w[MANAGER BOOKER TEAM]
      end
    when Hotel, Chain
      %w[HOTEL CHAIN]
    else
      []
    end
  end

  def create_user_and_invite!(user_params, creator = nil, role = nil)
    raise 'already has user' if user.present?

    if user_params.blank?
      user = User.new(name: full_name, email: email)
      user.role = role
      user.contact = self
    else
      user_params[:contact_id] = id
      user = User.create(user_params)
    end
    user.password = user.password_confirmation = temp_password
    user.password_changed_at = (User.expire_password_after + 1.year).ago
    user.organisation_name = parent.name if parent.is_a? Organisation
    user.hotel_role = 'Promoted' if parent.is_a? Hotel

    user.save
    if creator.present? && user.errors.blank?
      user.update_attribute(:creator_id, creator.id)
      UserMailer.welcome_user(user, promoting: true).deliver
    end
    user
  end

  def create_servace_app_user_and_invite(creator = nil)
    @user = user
    if @user.blank?
      role = 'BUSINESS_UNIT_MANAGER'
      @user = User.new(name: full_name, email: email)
      @user.role = role
      @user.contact = self
      @user.password = @user.password_confirmation = temp_password
      @user.password_changed_at = (User.expire_password_after + 1.year).ago
      @user.organisation_name = parent.name
      @user.save!
    end
    @user.update_attribute(:creator_id, creator.id)
    UserMailer.welcome_business_unit_manager(@user).deliver
    @user
  end

  def preferred_regions
    return unless user.present?

    user.regions.map(&:name).to_sentence
  end

  def recommended_by
    user = User.joins(:recommendations)
               .joins('INNER JOIN contacts on contacts.email = recommendations.email')
               .where('contacts.id = ?', id).first
    return nil if user && user.joined_loyalty_scheme.blank?

    user
  end

  def first_opportunity?
    opp_count = Opportunity.joins(:main_contact)
                           .joins("inner join conferences on conferences.id = opportunities.package_id and opportunities.package_type = 'Conference'")
                           .where("contacts.id = '#{id}'")
                           .where('conferences.commission_received_at is not null')
                           .count
    conf_date_count = ConferenceDate.joins(conference: { opportunity: :main_contact })
                                    .where("contacts.id = '#{id}'")
                                    .where('conference_dates.commission_received_at is not null')
                                    .count
    opp_count + conf_date_count == 1
  end

  def for_client?
    parent.is_a? Organisation
  end

  def for_supplier?
    (parent.is_a? Hotel) || (parent.is_a? Chain)
  end

  def all_tasks(scopes = [])
    base = Task
    scopes.each do |scope|
      base = base.send(scope)
    end

    tasks = base.joins("left join opportunities on opportunities.id = tasks.asset_id and tasks.asset_type='Opportunity'")
                .joins("left join conference_dates on conference_dates.id = tasks.asset_id and tasks.asset_type = 'ConferenceDate'")
                .joins('left join conferences on conferences.id = conference_dates.conference_id')
                .joins('left join opportunities cd_opps on cd_opps.package_id = conferences.id')
                .where("cd_opps.main_contact_id = :cid or opportunities.main_contact_id = :cid or (asset_type = 'Contact' and asset_id = :cid)  ", cid: id)
  end

  # ONLY call this on a single contact
  def connected_to
    out = {}
    case parent_type
    when 'Hotel', 'Chain'
      out[:hotel_conf] = Hotel.joins(:conference_contact).where('contacts.id= ?', id)
      out[:hotel_res] = Hotel.joins(:reservations_contact).where('contacts.id= ?', id)
      out[:hotel_sales] = Hotel.joins(:sales_manager_contact).where('contacts.id= ?', id)
      out[:hotel_accounts] = Hotel.joins(:accounts_contact).where('contacts.id= ?', id)
    when 'Organisation'
      out[:confs_as_bh] = Conference.joins(:budget_holder).where('contacts.id =?', id)
      out[:opps_as_mc] = Opportunity.joins(:main_contact).where('opportunities.main_contact_id =?', id)
    end
    out[:tasks] = all_tasks
    out[:attachments] = Attachment.where("attachments.parent_type = 'Contact' and attachments.parent_id = ?", id)
    out[:recipients] = Recipient.where(contact_id: id)
    out[:pra] = PreferredRateAgreement.joins(:agreed_contact).where('contacts.id =?', id)
    out
  end

  def total_connections
    out = 0
    case parent_type
    when 'Hotel', 'Chain'
      out += Hotel.where(conference_contact_id: id).count
      out  += Hotel.where(reservations_contact_id: id).count
      out  += Hotel.where(sales_manager_contact_id: id).count
      out  += Hotel.where(accounts_contact_id: id).count
    when 'Organisation'
      out += Conference.joins(:budget_holder).where('contacts.id =?', id).count
      out += Opportunity.joins(:main_contact).where('opportunities.main_contact_id =?', id).count
    end
    out += Task.where("tasks.asset_type = 'Contact' and tasks.asset_id = ?", id).count
    out += Attachment.where("attachments.parent_type = 'Contact' and attachments.parent_id = ?", id).count
    out += Recipient.where(contact_id: id).count
    out += PreferredRateAgreement.joins(:agreed_contact).where('contacts.id =?', id).count
    out
  end

  def no_connections?
    case parent_type
    when 'Hotel', 'Chain'
      return false if Hotel.where(conference_contact_id: id).exists?
      return false if Hotel.where(reservations_contact_id: id).exists?
      return false if Hotel.where(sales_manager_contact_id: id).exists?
      return false if Hotel.where(accounts_contact_id: id).exists?
    when 'Organisation'
      return false if Conference.joins(:budget_holder).where('contacts.id =?', id).exists?
      return false if Opportunity.joins(:main_contact).where('opportunities.main_contact_id =?', id).exists?
    end
    return false if Task.where("tasks.asset_type = 'Contact' and tasks.asset_id = ?", id).exists?
    return false if Attachment.where("attachments.parent_type = 'Contact' and attachments.parent_id = ?",
                                     id).exists?
    return false if Recipient.where(contact_id: id).exists?
    return false if PreferredRateAgreement.joins(:agreed_contact).where('contacts.id =?', id).exists?

    true
  end

  def actual_connections_for_humans
    out = ''
    connected_to.each do |k, v|
      next unless v.any?

      case k
      when :confs_as_bh, :opps_as_mc
        conf_roles = { confs_as_bh: 'Budget Holder for', opps_as_mc: 'Conference Main Contact' }
        out << conf_roles[k] << ': ' << v.all.map(&:id).join(', ')
      when :hotel_conf, :hotel_sales, :hotel_res, :hotel_accounts
        hotel_people = { hotel_conf: 'Hotel Conference Contact', hotel_sales: 'Hotel Sales Manager',
                         hotel_res: 'Hotel Reservations Contact', hotel_accounts: 'Hotel Accounts Contact' }
        out << hotel_people[k] << ': ' << v.all.map(&:name).join(', ')
      when :recipients
        out << 'Recipients' << ': ' << v.all.map { |r| "#{r.parent_type} - #{r.parent_id}" }.join(', ')
      when :pra
        out << 'Preferred Rates' << ': ' << v.all.map do |pra|
          "Client:#{pra.client.name} Hotel:#{pra.hotel.name}"
        end.join(', ')
      end
      out << '<br/>'
    end
    out.html_safe
  end

  def move_org(params)
    org = parent
    if params[:dupe_contact_id].present?
      dupe_contact = parent.dupe_org.contacts.readonly(false).find params[:dupe_contact_id]
      dupe_contact
    end
    begin
      transaction do
        if parent.primary_contact == self
          org.contact_id = nil
          org.save!
        end
        if dupe_contact.present?
          conferences.each do |conf|
            conf.opportunity.main_contact = dupe_contact
            conf.opportunity.client = org
            conf.save!
          end
          tasks.each do |task|
            task.asset = dupe_contact
            task.save!
          end

          if dupe_contact.user.nil? && user.present?
            user = self.user
            user.contact = dupe_contact
            user.save!
            dupe_contact.reload
            reload
          end
          destroy

          # can't sync the dupe_contact email until the old contact is deleted
          dupe_contact.update_attribute(:email, dupe_contact.user.email)
        else
          if loc = parent.dupe_org.has_location(primary_location)
            self.primary_location = loc
          elsif primary_location.nil?
            self.primary_location = parent.dupe_org.primary_location
          else
            parent.dupe_org.locations << primary_location
          end
          conferences.each do |conf|
            opp = conf.opportunity
            opp.client_id = parent.id
            opp.save!
          end
          self.parent = parent.dupe_org
          save!
        end
      end
    rescue Exception => e
      return e.message
    end
    true
  end

  def conferences
    Conference.joins(opportunity: :main_contact).where('contacts.id = ?', id)
  end

  def feedback_average
    with_feedback = conferences.select(&:feedback_completed?)
    return nil if with_feedback.empty?

    with_feedback.inject(sum = 0.0) { |sum, c| sum + c.feedback_average }.to_f / conferences.count
  end

  def shift_connections!(bad_contact, log)
    connections = bad_contact.connected_to
    connections[:recipients].each do |recipient|
      recipient.update_column(:contact_id, id) # !this does something!
      log << "recipient #{recipient.id} transferred to #{id} from #{bad_contact.id}"
    end
    connections[:pra].each do |pra|
      pra.update_column(:agreed_contact_id, id) # !this does something!
      log << "Preferred rate agreement agreed contact transferred #{pra.id} from #{bad_contact.id}"
    end
    connections[:hotel_conf].each do |hotel|
      hotel.update_column('conference_contact_id', id)
      log << "conference contact set to #{id} for hotel #{hotel.id} was #{bad_contact.id}"
    end
    connections[:hotel_res].each do |hotel|
      hotel.update_column('reservations_contact_id', id)
      log << "reservations contact set to #{id} for hotel #{hotel.id} was #{bad_contact.id}"
    end
    connections[:hotel_sales].each do |hotel|
      hotel.update_column('sales_manager_contact_id', id)
      log << "sales manager contact set to #{id} for hotel #{hotel.id} was #{bad_contact.id}"
    end
    connections[:hotel_accounts].each do |hotel|
      hotel.update_column('accounts_contact_id', id)
      log << "accounts contact set to #{id} for hotel #{hotel.id} was #{bad_contact.id}"
    end
    # now check for chain account manager
    if bad_contact.parent.is_a?(Chain) && bad_contact.parent.account_manager == bad_contact
      bad_contact.parent.update_column('account_manager_id', id)
      log << "Swapping account manager for chain #{bad_contact.parent.id}  to #{id} from #{bad_contact.id}"
    end
    bc_id = bad_contact.id
    log << if bad_contact.destroy
             "[SUCCESS] destroyed bad contact #{bc_id}"
           else
             "[PROBLEM] unable to destroy bad_contact #{bad_contact.id}"
           end
    log
  end

  def set_left!
    self.left_at = DateTime.parse(left_date + ' ' + left_time) if left_date.present? && left_time.present?
    self.skip_dup_email_valid = true
    return unless save

    user.disable! if user.present?
  end

  def left_string
    " (L: #{left_at.strftime('%d-%b-%y. %H:%M')})"
  end

  def un_leave!
    self.left_at = nil
    self.new_company = nil
    save(validate: false)
    return unless user.present?

    user.enable! if user.disabled?
  end

  def self.invite_token
    loop do
      random_token = SecureRandom.hex(10)
      break random_token unless Contact.exists?(invite_token: random_token)
    end
  end

  def opt_in_status
    if mkt_opted_in_at.present?
      'In'
    elsif mkt_last_opted_out_at.present?
      'Out'
    else
      'Undecided'
    end
  end

  def self.servace_contacts
    org = Organisation.servace
    org.contacts.where('contacts.left_at is null')
  end

  private

  def check_deletable
    deletable?
  end

  def downcase_email
    self.email = email.downcase if email.present?
  end

  def mkt_option
    if mkt_opt_in == '1' && mkt_opted_in_at.blank?
      self.mkt_opted_in_at = Time.zone.now
      self.mkt_last_opted_in_at = Time.zone.now
    elsif mkt_opt_in == '0' && (mkt_opted_in_at.present? || mkt_last_opted_out_at.blank?)
      self.mkt_opted_in_at = nil
      self.mkt_last_opted_out_at = Time.zone.now
    end
  end

  def sync_email
    return unless user.present? && email_changed? && persisted?

    user.update_attribute(:email, email)
  end
end
