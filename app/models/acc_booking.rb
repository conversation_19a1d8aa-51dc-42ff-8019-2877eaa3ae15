class AccBooking < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')

  include ClientOwnable
  include HotelOwnable

  attr_accessor :check_cancel_confirmation, :before_change_as_text, :before_change_as_arr, :validate_trainer_info, :skip_reset_room_info,
                :no_guest_email, :single_reason_req, :validate_accomm, :initial_amount_of_nights, :hotel_blackout_date_check_enabled, :override_cancellation_policy,
                :cancellation_reason_other, :skip_learner_booking_overlap_validation

  SINGLE_REASONS = [
    ['Odd Single Room', 'Medical room', 'Lone female traveller'],
    ['Approved: To be paid by <PERSON>rner', 'Approved: To be paid by <PERSON>lient']
  ]

  attribute :ji_type, :integer

  enum :ji_type, {
    joining_instruction: 0,
    rfq_joining_instruction: 1,
    virtual_join_instruction: 2
  }


  def self.apprentice_cancellation_reasons
    [
      'Apprentice on annual leave',
      'Apprentice sick',
      'Apprentice required at work',
      'Apprentice has moved group',
      'Apprentice has left programme',
      'Apprentice no longer requires accommodation',
      'Apprentice unable to attend due to personal reasons',
      'Apprentice on a break in learning',
      'Apprentice deceased',
      'Change of employer',
      'Change of programme',
      'Changed to single room',
      'Changed to twin room',
      'Maternity/Paternity leave',
      'Moved to homestay',
      'Change of hotel',
      'Change to training schedule',
      'Booked in error',
      'Hotel unable to accommodate booking',
      'Strike action',
      'Tutor - No Longer attending site',
      'Tutor - Change of Dates',
      'Tutor - Change of tutor delivering',
      'Other'
    ]
  end

  ADULT_SETTLED_BY = ['Client to pay', 'Guest to pay']

  audited associated_with: :acc_booking_header
  has_associated_audits # for the related data

  belongs_to :acc_booking_header
  belongs_to :acc_booking_person
  belongs_to :rfq_response_room
  belongs_to :acc_booking_block
  belongs_to :hotel
  belongs_to :conference
  belongs_to :accommodation_requirement
  belongs_to :legal_entity
  belongs_to :acc_selected_module
  belongs_to :joining_instruction

  belongs_to :hotel_confirmed_by_user, class_name: 'User', foreign_key: 'hotel_confirmed_by_user_id', optional: true

  has_many :acc_booking_stays, dependent: :destroy
  has_many :acc_week_blocks, as: :parent, dependent: :destroy # old week storage
  has_many :booking_dates, dependent: :destroy # new week storage
  has_many :acc_booking_chases
  has_many :acc_issues
  has_many :acc_additional_people, dependent: :destroy
  has_many :rfq_confirmation_answers
  has_many :acc_booking_adjusts
  has_many :booking_payments, as: :paymentable
  
  has_many :notes, as: :noteable
  has_many :survey_responses, foreign_key: :booking_id
  has_one :stripe_report_booking_data
  belongs_to :hotel_declined_by, class_name: 'User', foreign_key: 'hotel_declined_by_id'

  belongs_to :fee, optional: true, foreign_key: 'fee_id'

  before_validation :clear_room_if_subsistence

  accepts_nested_attributes_for :rfq_confirmation_answers
  accepts_nested_attributes_for :acc_additional_people, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :booking_dates
  validates_associated :booking_dates

  validates_associated :acc_additional_people

  validates :payment_method, presence: true, if: proc { |x|
                                                   (%w[PGB AGB].include? x.acc_booking_person.person_type) || (x.confirmed_flag && x.acc_booking_header.conference_id.present?) || (x.confirmed_flag && !x.subsistence_only && x.includes_chargeable?)
                                                 }
  validates :rfq_response_room_id, presence: true,
                                   unless: proc { |x|
                                             x.acc_booking_header.virtual? || x.subsistence_only? || x.complimentary? || x.acc_booking_person.person_type == 'PGB'
                                           }
  validates :cost_code, presence: true, if: proc { |x|
                                              !x.complimentary? && x.acc_booking_person.person_type == 'TRA'
                                            }
  validates :room_modifier, length: { maximum: 100 }, allow_blank: true

  validates :from_station, presence: true, if: lambda { |x|
                                                 x.acc_booking_header.rfq_location.present? && x.acc_booking_person.person_type == 'LEA' && x.train_details_req?
                                               }

  validates :train_details_req, presence: { message: 'Cant be blank if station present.' }, if: lambda { |x|
                                                                                                  x.from_station.present? && !x.acc_booking_header&.rfq_location&.no_train_tickets?
                                                                                                }

  validates :single_reason, presence: { message: ' cant be blank if room is single.' }, if: lambda { |x|
                                                                                              x.rfq_response_room.present? && x.acc_booking_person.person_type == 'LEA' && x.single_reason_req && x.rfq_response_room.room_type =~ /single/i && x.single_reason.blank?
                                                                                            }

  validates :single_reason, presence: { message: ' cant be blank if room is twin and single_as twin ticked.' }, if: lambda {
 |x| x.rfq_response_room.present? && x.acc_booking_person.person_type == 'LEA' && x.single_as_twin == true && x.single_reason_req && x.rfq_response_room.room_type =~ /twin/i && x.single_reason.blank?
                                                                                                                    }

  # only validate this if required by RFQ and the JI has been sent, so that it doesn't validate on initial creation
  validates :parking_required, inclusion: { in: [true, false], message: ' Must select whether parking is required' }, if: lambda {
    |x| acc_booking_header.conference_id.nil? &&
         x.acc_booking_header.rfq_location.rfq_request.parking_required_question &&
         x.joining_inst_sent_at.present? &&
         !x.cancellation_wish
  }

  validates :cancellation_reason_other, presence: true, if: lambda { |b|
                                                              b.cancellation_reason.present? && b.cancellation_reason == 'Other'
                                                            }


  # validates :payment_method, :presence => {:message =>" cant be blank if room is single."}, :if => lambda{|x| x.rfq_response_room.present? &&  x.acc_booking_person.person_type == "LEA" && x.single_reason_req &&  x.rfq_response_room.room_type =~ /single/i && x.payment_method.blank? }
  # not sure why I put this validation in Kathryn says it is not required

  validate :check_subsistence
  def check_subsistence
    unless acc_booking_header.present? && payment_method == 'Room/Rate + Subsistence' && acc_booking_header.conference_id.present?
      return
    end
    return if acc_booking_header.adult_subsistence_allowed?

    errors.add(:payment_method, "Cannot be 'Room/Rate + Subsistence' as it is not enabled on booking header.")
  end

  validate :accomm, if: ->(x) { x.validate_accomm }
  def accomm
    ar = AccommodationRequirement.where(id: accommodation_requirement_id).first
    room = RfqResponseRoom.where(id: rfq_response_room_id).first
    if ar.present? && room.present?
      unless ar.bedroom_type == room.room_type && ar.bedroom_type == room_type
        errors.add(:accommodation_requirement_id,
                   'All 3 room types must be consistent. The room type is not the same as package or booking')
        errors.add(:rfq_response_room_id,
                   'All 3 room types must be consistent. The room type is not the same as the accommodation requirement or booking!')
      end
      if RfqResponseRoom::CONF_SINGLE_ROOMS.include?(room.room_type) &&
         acc_additional_people.select { |p| p.marked_for_destruction? == false }.size > 0
        errors.add(:base, 'Sorry, can only have additional names if accommodation allows - the room type is single')
      end
    else
      errors.add(:accommodation_requirement_id, 'You must select the accommodation type ') if ar.blank?
      errors.add(:rfq_response_room_id, 'You must select a room package') if room.blank?
    end
  end

  validate :rooms_available, if: ->(b) { b.acc_booking_header.booking_type == 'ADULT' && !b.acc_booking_header.rfq_location.rfq_request.new_adult_booking_flow_toggle }, on: :update
  def rooms_available
    response = rfq_response_room.rfq_response
    (first_checkin..last_check_out).each do |d|
      room_type = self.room_type =~ /double/i ? :double : :single
      max_rooms = response.max_rooms_for(room_type, d, d)
      bookings_count = AccBooking.joins(rfq_response_room: :rfq_response)
                                 .where('acc_bookings.room_type  ilike ?', '%' + 'double' + '%')
                                 .where('acc_bookings.cancelled_at is null')
                                 .where('rfq_responses.id = ?', response.id)
                                 .where('first_checkin <= ? ', d)
                                 .where('last_check_out > ? ', d)
                                 .count

      errors.add(:base, "Sorry, insufficent #{room_type} rooms available on #{d}") if bookings_count > max_rooms
      break if errors.any?
    end
  end

  validate :duplicate_booking_for_person, if: ->(x) { x.acc_booking_person.person_type == 'LEA' && Current.user.present? && !(Current.user.id == 14667 && self.subsistence_only?) }
  def duplicate_booking_for_person
    return if cancelled?

    # Check if the booking is headless or mostly empty
    if acc_booking_header.blank?
      update_columns(deleted_at: Date.today) # Use update_columns to bypass validations
      return
    end

    bookings = acc_booking_person.rfq_learner.bookings.not_cancelled
    bookings = bookings.where(virtual_flag: false) unless virtual_flag
    bookings.each do |booking|
      next if booking.acc_booking_header.blank? # Skip headless bookings
      next if id == booking.id
      next if booking.acc_booking_adjusts.non_arrivals.any? # check if comparison booking has non arrival, if yes skip

      next unless booking.first_checkin.present? && booking.last_check_out.present?

      booking_dates.each do |bdates|
        bdates.dates_required.each do |date_to_check|
          unless date_to_check > booking.first_checkin && date_to_check < booking.last_check_out && booking.id.present?
            next
          end

          errors.add(:base,
                     'This learner: ' + acc_booking_person.rfq_learner.full_name + ' already has a booking: ' + booking.id.to_s)
          break
        end
      end
    end
  end

  validate :account_payment_has_account_number
  def account_payment_has_account_number
    if payment_method == 'Business Account' && account_number.blank?
      errors.add(:account_number, 'You must input an account number for this payment method')
    elsif payment_method != 'Business Account' && account_number.present?
      errors.add(:account_number, 'This payment method does not require an account number')
    end
  end

  validate :has_valid_account_number
  def has_valid_account_number
    return unless payment_method == 'Business Account' && account_number.present?

    bu_unit = RfqBusinessUnit.where('name = ? and postcode = ? and rfq_programme_id = ?',
                                    acc_booking_person.bu_name, acc_booking_person.postcode, rfq_response_room.rfq_response.rfq_location.rfq_request.rfq_programme.id).first
    if bu_unit.blank? || RfqBuAccount.where('rfq_response_id = ? and rfq_business_unit_id = ? and acc_number = ?',
                                            rfq_response_room.rfq_response.id, bu_unit.id, account_number).blank?
      errors.add(:account_number, 'Sorry this account number does not match the one on our records')
    end
  end

  validate :booking_cannot_have_no_dates, on: :update
  def booking_cannot_have_no_dates
    # if all accommodation dates are false for the whole booking then throw an error
    # need to use a select rather than the usualy scope here because the data ahsn't actually saved yet
    return unless !virtual_flag && booking_dates.any?

    dates = booking_dates.select { |week| week.week_type == BookingDate.week_types['accommodation'] }
    if dates.all? { |week| week.slice('sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat').all? { |k, v| v == false } }
      errors.add(:base,
                 'Cannot remove all accommodation from the entire booking! Please ensure at least one week has accommodation selected.')
    end
  end

  validate :does_not_cross_hotel_blackout_date, if: ->(b) { b.hotel_blackout_date_check_enabled }
  def does_not_cross_hotel_blackout_date
    adult = acc_booking_header.booking_type == 'ADULT'
    blackout_dates = if adult
                       HotelBlackoutDate.where(hotel_id: hotel.id, adult_blackout_date: true)
                     else
                       HotelBlackoutDate.where(hotel_id: hotel.id, apprentice_blackout_date: true)
                     end
    # so you should be allowed to check in on the end_date of the blackout date
    # you should be allowed to check out on the start_date of a blackout date
    # you should not be allowed to overlap a blackout date
    blackout_dates = blackout_dates.where("start_date < ? AND end_date > ?", last_check_out, first_checkin)
    if blackout_dates.any?
      errors.add(:base, "There is at least one non subsistence booking that overlaps the hotel blackout date - #{blackout_dates.first.start_date} - #{blackout_dates.first.end_date}")
    end
  end

  before_validation :setup_comp_room
  before_create :generate_token
  after_create :set_card_pay
  before_save :set_dates
  before_save :reset_room_info, unless: ->(x) { x.acc_booking_header.virtual? }
  after_save :hotel_confirmation, unless: ->(x) { x.acc_booking_header.virtual? }
  after_save :recalc_total_paid!

  PAYMENT_TYPES = ['Cash on Arrival', 'Cheque before arrival', 'Cash', 'Credit Card', 'BACS'] # last one acts as default when necessary - jpd - ON-1602

  scope :confirmed, -> { where('acc_bookings.confirmed_at is not null') }
  scope :cancelled, -> { where('acc_bookings.cancelled_at is not null') }
  scope :not_cancelled, -> { where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null') }
  scope :no_adjusts, -> { where.missing(:acc_booking_adjusts) }
  scope :exclude_non_arrivals, -> { joins(:acc_booking_adjusts).where(acc_booking_adjusts: { category: 'NON' }) }
  scope :hotel_declined, -> { where('hotel_declined_at is not null') }
  scope :not_resolved, -> { where('resolved_at is null') }
  scope :not_subsistence, -> { where('acc_bookings.subsistence_only is not true') }
  scope :subsistence_only, -> { where('acc_bookings.subsistence_only is true') }
  scope :charged_to_account, -> { where("acc_bookings.payment_method != 'Guest to Pay'") }
  scope :paid_by_guest, -> { where("acc_bookings.payment_method = 'Guest to Pay'") }
  scope :provisional, -> { where('confirmed_at IS NULL and confirmed_by IS NULL and confirmed_flag = ?', false) }
  scope :pending, -> { where('acc_bookings.hotel_confirmed_at is null') }
  scope :payments_due_soon, -> { where('first_checkin >= ? and first_checkin <= ?', Date.today, Date.today + 14.day) }
  scope :adults, -> { joins(:acc_booking_person).where('acc_booking_people.person_type = ?', 'AAB') }

  def self.very_urgent
    self.select('distinct acc_bookings.*').joins(:acc_booking_stays).where('acc_bookings.confirmed_at is null and acc_bookings.cancelled_at is null')
        .where('acc_bookings.first_checkin >= ?', Date.today)
        .where('acc_bookings.first_checkin <= ?', (Date.today + 3))
  end

  def self.trainer
    joins(:acc_booking_person).where("acc_booking_people.person_type = 'TRA' ")
  end

  def self.apprentice
    joins(:acc_booking_person).where('acc_booking_people.person_type in (?) ', %w[LEA PRO])
  end

  def self.conference
    joins(:acc_booking_person).where('acc_booking_people.person_type in (?) ', %w[AGB PGB])
  end

  def self.adult
    joins(:acc_booking_person).where('acc_booking_people.person_type = ? ', 'AAB')
  end

  def self.future
    where('acc_bookings.first_checkin >= ?', Date.today).where(cancelled_at: nil, deleted_at: nil)
  end

  def self.future_minus_five_days
    where('acc_bookings.first_checkin >= ?', Date.today - 5.days).where(cancelled_at: nil, deleted_at: nil)
  end

  def self.after(d)
    where('acc_bookings.first_checkin > ?', d).where(cancelled_at: nil, deleted_at: nil)
  end

  def self.late_booked
    where(
      'acc_bookings.first_checkin <= ? and acc_bookings.last_check_out >= ? and DATE(acc_bookings.created_at) >= acc_bookings.first_checkin - 14', Date.today, Date.today - 6.months
    )
      .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null')
      .where('acc_bookings.subsistence_only is false')
  end

  def self.for_ji_resendable
    joins(:acc_booking_person, acc_booking_header: { rfq_location: :rfq_request })
      .where("acc_booking_people.person_type = 'LEA'")
      .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null')
      .where('acc_bookings.confirmed_at is null')
      .where('rfq_requests.no_ji is not true')
      .where('first_checkin <= ? or epa_booking=true', Date.today + 6.weeks)
      .where('first_checkin >= ? or epa_booking=true', Date.today)
  end

  def self.dupe_trainer_bookings(org_id, email, checkin, checkout, bk_id = nil)
    out = not_cancelled.joins(acc_booking_person: :rfq_trainer, acc_booking_header: { rfq_location: { rfq_request: { rfq_programme: :client } } })
                       .where('organisations.id = ?', org_id)
                       .where('rfq_trainers.email = ? and acc_bookings.last_check_out > ? and acc_bookings.first_checkin < ? ', email, checkin, checkout)
    out = out.where('acc_bookings.id <> ? ', bk_id) if bk_id.present?
    out
  end

  def manual?
    accommodation_requirement.blank?
  end

  def paid_by_account?
    payment_method == 'Business Account'
  end

  def can_pay_by_account?
    rfq_response_room.rfq_response.rfq_location.rfq_request.enable_on_account?
  end

  # def can_confirm_subsistence_only_booking?
  #   self.acc_booking_header.rfq_location.rfq_request.subsistence_confirm_by_learner?
  # end

  def possible_train_payment_types
    return ['Guest to Pay'] if acc_booking_header.adult_bill_to == 'Guest to Pay'

    if acc_booking_header.adult_subsistence_allowed?
      return ['Room/Rate Only', 'Room/Rate + Subsistence', 'Guest to Pay']
    end

    ['Room/Rate Only', 'Guest to Pay']
  end

  def possible_group_payment_types
    if acc_booking_header.adult_bill_to == 'Guest to Pay'
      ['Guest to Pay']
    elsif payment_method != 'Full Account'
      if acc_booking_header.adult_subsistence_allowed?
        return ['Room/Rate Only', 'Room/Rate + Subsistence', 'Guest to Pay']
      end

      ['Room/Rate Only', 'Guest to Pay']

    else
      if acc_booking_header.adult_subsistence_allowed?
        return ['Full Account', 'Room/Rate Only', 'Room/Rate + Subsistence', 'Guest to Pay']
      end

      ['Full Account', 'Room/Rate Only', 'Guest to Pay']

    end
  end

  def single_reasons_not_hotel
    SINGLE_REASONS[0] + SINGLE_REASONS[1]
  end

  def single_reasons_hotel
    SINGLE_REASONS[0]
  end

  DIFF_NAMES = ['Hotel', 'Room Type', 'Pkg Type', 'Modifier', 'Preference', 'Special Requirements', 'Transport Inc?']

  def to_arr
    my_stays = acc_booking_stays.collect { |x| [x.check_in, x.check_out] }.sort_by { |x| x[0] }
    ([hotel_id, room_type, pkg_type, rooming_preference, special_requirements,
      (transport_included ? 'Inc Trans' : 'Exc Trans')] + my_stays)
  end

  def to_s
    to_arr.join(', ')
  end

  def grp_info_arr
    my_add_people = acc_additional_people.collect do |x|
                      [x.forename, x.surname, x.email, x.telephone]
                    end.sort_by { |x| x[1] }
    ([hotel_id, room_type, pkg_type, room_modifier,
      rooming_preference, special_requirements,
      first_checkin, last_check_out, try(:acc_booking_person).try(:forename),
      try(:acc_booking_person).try(:surname), try(:acc_booking_person).try(:cost_code),
      try(:acc_booking_person).try(:email), try(:acc_booking_person).try(:telephone)] + my_add_people)
  end

  def grp_info_string
    grp_info_arr.join(',')
  end

  ADULT_NAMES = ['Hotel', 'Room Type', 'Pkg Type', 'Modifier', 'Preference', 'Special Requirements', 'Check in',
                 'Check out', 'Payment', 'Forename', 'Surname']

  def adult_info_arr
    [hotel_id, room_type, pkg_type, room_modifier,
     rooming_preference, special_requirements,
     first_checkin, last_check_out, payment_method,
     try(:acc_booking_person).try(:forename),
     try(:acc_booking_person).try(:surname)]
  end

  def adult_info_string
    adult_info_arr.join(', ')
  end

  def short_info
    my_stays = acc_booking_stays.collect { |x| [x.check_in, x.check_out] }.sort_by { |x| x[0] }
    ([person, hotel.name, room_type, pkg_type, rooming_preference, special_requirements,
      (transport_included ? 'Inc Trans' : 'Exc Trans')] + my_stays).join(', ')
  end

  def shorter_info
    [room_type, pkg_type, rooming_preference, special_requirements,
     (transport_included ? 'Inc Trans' : 'Exc Trans')].reject(&:blank?).join(', ')
  end

  def set_before_change_as_text
    self.before_change_as_text = to_s
    self.before_change_as_arr = to_arr
  end

  def set_before_change_as_text_for_group
    self.before_change_as_text = grp_info_string
    self.before_change_as_arr = grp_info_arr
  end

  def auto_confirm!
    self.confirmed_at = Time.zone.now
    self.confirmed_by = 'Auto Confirmed'
  end

  def clear_confirming!
    self.confirmed_flag = false
    self.confirmed_at = nil
    self.confirmed_by = nil
  end

  def reset_confirmation
    clear_confirming!
    save!
  end

  def mark_as_hotel_confirmed_by(user)
    # self.hotel_confirmed_at = Time.zone.now
    self.confirmed_flag = true
    self.confirmed_at = Time.zone.now
    self.confirmed_by = user
    save!
    AdultAccommodationMailer.delay.send_booking_confirmation(id)
    AdultAccommodationMailer.delay.booking_details(id)
    AdultAccommodationMailer.delay.hotel_booking_details(id)
  end

  validate :validate_cancelled_or_confirmed, if: proc { |x| x.check_cancel_confirmation }

  def validate_cancelled_or_confirmed
    return unless confirmed_flag.blank? && cancellation_wish.blank?

    errors.add(:cancellation_wish, ' If you want to cancel please complete the cancellation')
    errors.add(:confirmed_flag, ' If you want to confirm please complete the confirmation')
  end

  validate :cancellation_details_present, if: proc { |x| x.cancelled? }

  def cancellation_details_present
    if check_cancel_confirmation && first_checkin < Date.today
      errors.add(:cancellation_reason, ' Sorry, you cannot cancel a booking once it has commenced')
    end
    errors.add(:cancellation_reason, ' Please enter a reason') unless cancellation_reason.present?
    errors.add(:cancelled_by, ' Please enter a name') unless cancelled_by.present?
  end

  validate :confirmation_details_present, if: proc { |x| x.confirmed? }

  def confirmation_details_present
    errors.add(:confirmed_by, ' Please enter a name') unless confirmed_by.present?
  end

  validate :validate_subsistence_only
  def validate_subsistence_only
    return unless subsistence_only?

    if rfq_response_room_id.present?
      errors.add(:rfq_response_room_id,
                 ' If no accommodation required then please select the drop down to blank as no room at the hotel is required.')
    end
    errors.add(:subsistence_only, '- cannot be as already has a non/early arrival') if acc_booking_adjusts.any?
  end

  validate :trainer_pre_check, if: proc { |x| x.validate_trainer_info }

  def trainer_pre_check
    errors.add(:first_checkin, 'Please supply') unless first_checkin.present?
    errors.add(:last_check_out, 'Please supply') unless last_check_out.present?

    if first_checkin.present? && last_check_out.present? && last_check_out <= (first_checkin)
      errors.add(:last_check_out, 'Must be later than checkin')
    end
    rfq_start = acc_booking_header.rfq_location.rfq_request.start_date
    rfq_end = acc_booking_header.rfq_location.rfq_request.end_date
    if first_checkin.present? && (first_checkin > rfq_end || first_checkin < rfq_start)
      errors.add(:first_checkin, "cannot be outside the scope of the rfq dates (#{rfq_start} - #{rfq_end})")
    end
    if last_check_out.present? && (last_check_out > rfq_end || last_check_out < rfq_start)
      errors.add(:last_check_out, "cannot be outside the scope of the rfq dates (#{rfq_start} - #{rfq_end})")
    end

    unless acc_booking_person.rfq_trainer_id.present?
      acc_booking_person.errors.add(:rfq_trainer_id,
                                    'Please supply')
    end
    if !complimentary? && rfq_response_room_id.present? && payment_method.blank?
      errors.add(:payment_method,
                 'Please supply')
    end

    if clash_ignored_by.blank? && first_checkin.present? && last_check_out.present?
      # dupe check
      dupes = AccBookingStay.joins(acc_booking: :acc_booking_person)
                            .where('acc_booking_people.rfq_trainer_id = ?', acc_booking_person.rfq_trainer_id)
                            .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null').overlaps(first_checkin, last_check_out)
      dupes = dupes.where('acc_bookings.id != ?', id) if persisted?
      dupes = dupes.pluck('acc_bookings.id')

      if dupes.any? && clash_ignored_by.blank?
        errors.add(:first_checkin,
                   "Overlaps the following booking(s) #{dupes.to_sentence}")
      end
    end
    true
  end

  validate :conference_pre_check, if: proc { |x| %w[PGB AGB].include? x.acc_booking_person.person_type }

  def conference_pre_check
    errors.add(:first_checkin, 'Please supply') unless first_checkin.present?
    errors.add(:last_check_out, 'Please supply') unless last_check_out.present?

    return unless first_checkin.present? && last_check_out.present?

    errors.add(:last_check_out, 'Must be later than check in') if last_check_out <= first_checkin
  end

  validate :adult_subsistence_allowance_check
  def adult_subsistence_allowance_check
    return unless acc_booking_person.person_type == 'TRA'
    return unless payment_method == 'Room/Rate + Subsistence'
    return if acc_booking_header.rfq_location.rfq_request.adult_subsistence_allowed?

    errors.add(:payment_method, 'Sorry, We currently do not allow subsistence on this programme.')
  end

  validate :check_comp_room_and_rfq_response_room
  def check_comp_room_and_rfq_response_room
    return unless complimentary?

    return unless rfq_response_room_id.present?

    errors.add(:rfq_response_room_id,
               'You can take the complimentary room or select a room not both. Please clear the one you dont need')

    # errors.add(:payment_method, "Please supply") unless self.payment_method.present?
  end

  validate :validate_comp_room_available
  def validate_comp_room_available
    return unless complimentary? && !cancelled_at.present?

    errors.add(:complimentary, 'Sorry, No longer available') unless comp_room_avail?
  end

  validate :check_grp_dates_within_acc_requirement
  def check_grp_dates_within_acc_requirement
    return unless try(:acc_booking_header).try(:conference).present?

    if ar = accommodation_requirement
      if first_checkin.present? && last_check_out.present?
        if first_checkin < ar.arrival_date || first_checkin > ar.departure_date
          errors.add(:first_checkin, "Must be between #{ar.arrival_date} and #{ar.departure_date}")
        end
        if last_check_out < ar.arrival_date || last_check_out > ar.departure_date
          errors.add(:last_check_out, "Must be between #{ar.arrival_date} and #{ar.departure_date}")
        end
      end
    else
      # No requirement so just make sure its within the conference dates
      c = acc_booking_header.conference
      if first_checkin.present? && last_check_out.present?
        if first_checkin < c.arrival_date || first_checkin > c.departure_date
          errors.add(:first_checkin, "Must be between #{c.arrival_date} and #{c.departure_date}")
        end
        if last_check_out < c.arrival_date || last_check_out > c.departure_date
          errors.add(:last_check_out, "Must be between #{c.arrival_date} and #{c.departure_date}")
        end
      end
    end
  end

  def cross_check_apprentice
    return unless try(:acc_booking_person).try(:person_type) == 'AGB'
    return unless first_checkin.present? && last_check_out.present?

    # dupe check
    dupes = AccBookingStay.joins(acc_booking: { acc_booking_header: { rfq_location: { rfq_request: :rfq_programme } } })
                          .joins(acc_booking: { acc_booking_person: :rfq_learner })
                          .where('rfq_programmes.client_id = ?', acc_booking_header.conference.opportunity.client_id)
                          .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null').overlaps(first_checkin, last_check_out)
                          .where('TRIM(LOWER(rfq_learners.forename)) = TRIM(LOWER(?)) and TRIM(LOWER(rfq_learners.surname)) = TRIM(LOWER(?))', acc_booking_person.forename, acc_booking_person.surname)
    dupes = dupes.where('acc_bookings.id != ?', id) if persisted?
    dupes = dupes.pluck('acc_bookings.id')
  end

  def setup_comp_room
    if complimentary? && acc_booking_header.rfq_location.present?
      self.room_type = 'Double for Sole'
      resp = acc_booking_header.rfq_location.accepted_response
      self.pkg_type = resp.comp_room_pkg
      room = resp.rfq_response_rooms.where(room_type: room_type, pkg_type: pkg_type).first

      unless room.present?
        errors.add(:base,
                   "Comp room not available for room type: #{room_type} and package type: #{pkg_type}")
        return false
      end

      self.pppn_inc_vat = 0
      self.transport_included = false
      self.hotel_id = acc_booking_header.rfq_location.accepted_response.rfq_proposed_hotel.hotel_id
      self.comp_saving_inc_vat = room.price_inc_vat
      self.comp_saving_ex_vat = room.price_ex_vat
    else
      self.complimentary = false
      self.comp_saving_inc_vat = 0
      self.comp_saving_ex_vat = 0
    end
    true
  end

  def rfq_learner
    acc_booking_person.rfq_learner
  end

  def person
    case acc_booking_person.person_type
    when 'TRA'
      acc_booking_person.rfq_trainer.full_name
    when 'LEA'
      acc_booking_person.rfq_learner.full_name
    when 'AGB', 'PGB'
      acc_booking_person
    end
  end

  def issues
    AccIssue.joins(rfq_location: { acc_booking_headers: :acc_bookings })
            .where('acc_bookings.id = ?', id)
  end

  def save_with_person(person)
    self.acc_booking_person = person
    valid?
    if person.valid?
      save
    else
      false
    end
  end

  def calculate_stays(training = false)
    if acc_booking_person.non_block_person?
      stays = [[first_checkin, last_check_out]]
    else
      dates = get_dates(training)
      splits = dates.unshift(Date.new(1800, 1, 1)).each_cons(2).slice_before do |m, n|
                 m + 1 < n
               end.map { |a| a.map(&:last) }
      stays = []
      splits.each do |s|
        stays << [s.first, s.last]
      end
    end
    stays
  end

  def get_dates(training)
    dates = []
    weeks = if booking_dates.any?
              if training
                training_dates
              else
                accommodation_dates
              end
            elsif training
              acc_booking_block.acc_week_blocks
            else
              acc_week_blocks
            end
    weeks.each do |w|
      dates += w.dates_required
    end
    dates
  end

  def get_training_dates(times_required = false)
    dates = []
    weeks = training_dates
    weeks.each do |w|
      dates += w.dates_required(times_required)
    end
    dates
  end

  def includes_chargeable?
    stays = calculate_stays
    poss_charge = whats_chargeable
    stay_dates = []
    stays.each do |s|
      stay_dates += ((s[0])..(s[1] - 1)).to_a
    end
    !(poss_charge & stay_dates).empty?
  end

  def whats_chargeable
    poss_charge = []
    weeks = accommodation_dates.any? ? accommodation_dates : acc_week_blocks
    payment_method = acc_booking_header.payment_method.present? ? acc_booking_header.payment_method : acc_booking_header.rfq_location.rfq_request.payment_method
    weeks.each do |awb|
      poss_charge += awb.possibly_chargeable(payment_method)
    end
    poss_charge
  end

  def reset_stays(editing = false)
    return true if virtual_flag?

    self.skip_reset_room_info = true
    transaction do
      acc_booking_stays.destroy_all
      training_days = []
      training_days = training_dates unless acc_booking_person.non_block_person?
      accommodation_days = calculate_stays

      nights_count = 0
      total_sub_days = 0

      training_days.each do |week|
        total_sub_days += week.dates_required.count
      end

      accommodation_days.each do |d|
        acc_booking_stays << AccBookingStay.new(check_in: d[0], check_out: d[1])
        nights_count += (d[1] - d[0]).to_i
      end

      self.total_nights = nights_count.to_i
      self.no_nights_charge_manager = total_nights
      self.subsistence_days = total_sub_days

      if !acc_booking_person.non_block_person?
        non_empty_weeks = accommodation_dates.where.not(sun: false, mon: false, tue: false, wed: false,
                                                        thu: false, fri: false, sat: false).sort
        self.first_checkin = non_empty_weeks.first.get_earliest_date_in_week
        self.last_check_out = non_empty_weeks.last.get_latest_date_in_week
      else
        self.first_checkin = accommodation_days.collect { |x| x[0] }.sort.first
        self.last_check_out = accommodation_days.collect { |x| x[1] }.sort.reverse.first
      end

      if !subsistence_only? && !editing
        # check if first checkin and last check out do not cross blackout dates
        self.hotel_blackout_date_check_enabled = true
      end

      if !acc_booking_person.non_block_person? && acc_booking_header.rfq_location && acc_booking_header.rfq_location.rfq_request.subsistence_req?
        rfq_request = acc_booking_header.rfq_location.rfq_request
        subsistence_cost = rfq_request.use_old_fees? ? acc_booking_header.rfq_location.rfq_request.subsistence_cost : rfq_request.get_fees_for_rfq&.subsistence_fees
        self.subsistence_pppn = subsistence_cost
      end
      self.total_cost_manager = total_due
      save!
    end
  end

  def display_stays
    st = []
    acc_booking_stays.each do |s|
      st << "#{s.check_in&.to_fs(:uk)} - #{s.check_out&.to_fs(:uk)}"
    end
    st.join(' <br> ').html_safe
  end

  def display_training_dates
    st = []
    training_dates.order(:start_date).each do |week|
      st << "#{week.get_earliest_date_in_week&.to_fs(:uk)} - #{week.get_latest_date_in_week&.to_fs(:uk)}"
    end
    st.join(' <br> ').html_safe
  end

  def stays_as_array
    acc_booking_stays.order(:check_in).collect { |x| { check_in: x.check_in, check_out: x.check_out } }
  end

  def reset_confirmation_answers!
    loc = acc_booking_header.rfq_location
    loc.rfq_confirmation_questions.enabled.each do |q|
      next if rfq_confirmation_answers.where(rfq_confirmation_question_id: q.id).first

      ans = rfq_confirmation_answers.build(rfq_confirmation_question_id: q.id)
      ans.question_text = q.question
      ans.mandatory = q.mandatory
      ans.position = q.position
      ans.save!(validate: false)
    end
    reload
    rfq_confirmation_answers.includes(:rfq_confirmation_question).each do |ans|
      ans.destroy unless ans.rfq_confirmation_question.present? && ans.rfq_confirmation_question.enabled?
    end
  end

  def generate_token
    self.booking_token = loop do
      random_token = SecureRandom.hex(10)
      break random_token unless RfqResponse.exists?(token: random_token)
    end
  end

  def cancelled?
    cancellation_wish || cancelled_at.present?
  end

  def confirmed?
    confirmed_flag || confirmed_at.present?
  end

  def refunding?
    refund_requested? || refund_required?
  end

  def really_confirmed?
    if acc_booking_person.trainer?
      confirmed? && hotel_confirmed_at.present?
    else
      confirmed?
    end
  end

  def provisional?
    confirmed_flag == false && confirmed_at.blank? && confirmed_by.blank?
  end

  def self.upcoming_pending_bookings?
    AccBooking.all.adults.pending.not_cancelled.payments_due_soon.any?
  end

  def hotel_name_or_subsistence
    if subsistence_only?
      'Subsistence Only'
    else
      hotel.name
    end
  end

  def needs_hotel_grp_acceptence?
    cancelled_at.blank? && hotel_confirmed_at.blank?
  end

  def row_class
    if cancelled_at.present?
      'error'
    elsif amended_at.present?
      'info'
    elsif acc_booking_person.person_type == 'PRO'
      'warning'
    elsif confirmed_at.blank?
      'success'
    end
  end

  def group_row_class
    if cancelled_at.present?
      'error'
    elsif amended_at.present?
      'info'
    elsif hotel_confirmed_at.blank?
      'success'
    end
  end

  def self.bulk_confirm!(bookings, user)
    bad = []
    bookings.not_cancelled.each do |b|
      bad << b unless b.hotel_confirm(user)
    end
    bad
  end

  def non_arrivals_emails
    brief = acc_booking_header.rfq_location.rfq_responses.joins(rfq_proposed_hotel: :hotel).where('hotels.id =?',
                                                                                                  hotel_id).first.rfq_client_briefings.first
    brief.non_arrivals_email.split(',')
  end

  def reset_room_info
    return if acc_booking_header.virtual?

    return if skip_reset_room_info == true

    if acc_booking_person.person_type == 'PGB' && rfq_response_room_id.nil?
      self.pkg_type = nil
      self.transport_included = nil
      self.pppn_inc_vat = nil
    elsif valid? && !complimentary?
      if subsistence_only?
        self.rfq_response_room = nil
        self.room_type = nil
        self.pkg_type = nil
        self.transport_included = nil
        self.pppn_inc_vat = nil
      # removing as part of istl-386, this means that subsistence bookings cannot connect to hotels for refunds
      # self.hotel = nil

      else
        if acc_booking_header.group_booking?
          ar = acc_booking_header.conference.accepted_quote
          rfq = nil
          room = ar.rfq_response_rooms.find(rfq_response_room_id)
          hotel_picked = ar.proposed_hotel.hotel
        elsif acc_booking_header.adult_booking?
          room = acc_booking_header.rfq_location.possible_room_options.find(rfq_response_room_id)
          ar = room.rfq_response
          hotel_picked = ar.rfq_proposed_hotel.hotel
          rfq = acc_booking_header.rfq_location.rfq_request
        else
          ar = acc_booking_header.rfq_location.accepted_response
          rfq = acc_booking_header.rfq_location.rfq_request
          room = acc_booking_header.rfq_location.accepted_response.rfq_response_rooms.find(rfq_response_room_id)
          hotel_picked = ar.rfq_proposed_hotel.hotel
        end
        self.rfq_response_room = room
        self.room_type = room.room_type
        self.pkg_type = room.pkg_type
        self.transport_included = room.transport_included
        self.pppn_inc_vat = room.price_inc_vat
        self.hotel = hotel_picked

        if acc_booking_header.group_booking? || acc_booking_header.adult_booking?
          self.supplement_pppn = 0
        else
          self.supplement_pppn = if ar.single_supplement? && acc_booking_person.apply_ss? && room_type == 'Single'
                                   ar.single_supplement_amt
                                 else
                                   0
                                 end
          if rfq.subsistence_req?
            if acc_booking_person.present? && acc_booking_person.person_type == 'LEA' && acc_booking_header.rfq_location.rfq_request.rfq_programme.zero_chg_sub? && acc_booking_person.rfq_learner.rfq_business_unit.zero_chg_sub?
              self.subsistence_pppn = 0
            else
              self.subsistence_pppn = (rfq.use_old_fees? ? acc_booking_header.rfq_location.rfq_request.subsistence_cost : rfq.get_fees_for_rfq&.subsistence_fees) || 0
            end
          else
            self.subsistence_pppn = 0
          end
        end
      end
    end
  end

  def recalc_total_paid!
    return true unless card_pay?

    pence = booking_payments.charged.sum(:amount) || 0
    pounds = pence.to_f / 100
    update_columns(total_paid_manager: pounds)
  end

  def set_card_pay
    return unless acc_booking_header.rfq_location.present?
    return if acc_booking_person&.rfq_learner&.rfq_business_unit&.override_card_pay?

    self.card_pay = acc_booking_header.rfq_location.rfq_request.card_pay?
    save!
  end

  def total_stripe_refund
    pence = booking_payments.refunded.sum(:amount) || 0
    -pence.to_f / 100
  end

  def fully_refunded?
    total_stripe_payments = booking_payments.charged.sum(:amount) || 0
    total_stripe_refunds = -booking_payments.refunded.sum(:amount) || 0
    total_stripe_refunds == total_stripe_payments
  end

  def cancelled_before_4?
    false # unless self.cancelled_at.present?
    # d = self.cancelled_at
    # day = d.to_date
    # ( self.first_checkin == day && (d < Time.local(day.year, day.month, day.day, 16)))  || self.first_checkin > day
  end

  def before_4_on_checkin?
    false
    # today = Date.today
    # self.first_checkin == today && (Time.zone.now < Time.local(today.year, today.month, today.day, 16))
  end

  def set_refund_flags!
    # only for card pay and confirmed
    return true unless card_pay? && confirmed_at.present?

    reset_refund_flags!
    # If it is before 4PM refund required must be marked as true, otherwise refund requested must be marked as true
    if before4pm?
      update(refund_required: true)
    else
      update(refund_requested: true)
    end
    # self.update_columns(:refund_requested => true)
  end

  def refund_completed
    update_columns(refunded: true, refund_requested: false, refund_required: false)
  end

  def reset_refund_flags!
    update_columns(refunded: false, no_refund: false, refund_requested: false, refund_required: false)
  end

  def before4pm?(time = nil)
    time = Time.current if time.nil?
    time.between?(Time.parse('00:00am'), Time.parse('2:00pm'))
  end

  # Only refund a singular booking if the datetime is before first checkin 4pm
  def eligible_for_refund?
    DateTime.now.before?(self&.first_checkin&.to_datetime&.change({ hour: 16 }))
  end

  def refund_state
    case true
    when refund_required?
      'Required'
    when refund_requested?
      'Requested'
    when refunded?
      'Refunded'
    when no_refund?
      'No Refund'
    else
      'Refundable'
    end
  end

  def set_dates
    self.cancelled_at = Time.zone.now if cancellation_wish.present? && cancelled_at.blank?
    return unless confirmed_flag.present? && confirmed_at.blank?

    self.confirmed_at = Time.zone.now
  end

  def extra_nights_charge
    if subsistence_only?
      0
    else
      no_nights_charge_manager.present? && pppn_inc_vat.present? ? (no_nights_charge_manager * pppn_inc_vat) : 0
    end
  end

  def supplement_required_for_x_nights
    if total_nights.present? && no_nights_charge_manager.present?
      sup_nights = (total_nights - no_nights_charge_manager)
      sup_nights if sup_nights > 0
    else
      0
    end
  end

  def supplement_cost
    if subsistence_only? || complimentary?
      0
    else
      (supplement_required_for_x_nights || 0) * supplement_pppn
    end
  end

  def subsistence_days_cost
    # if paying direct to servace use fee, if not then use old subsistence_pppn value
    subsistence_cost = if acc_booking_header.rfq_location.rfq_request.use_old_fees?
                         self&.subsistence_pppn || 0
                       else #if pay to servace
                        acc_booking_header.rfq_location.rfq_request.get_fees_for_rfq&.subsistence_fees || 0
                       end
    subsistence_days * subsistence_cost
  end

  def total_due
    if %w[AAB AGB PGB].include?(acc_booking_person.person_type)
      total_nights * (pppn_inc_vat || 0)
    else
      extra_nights_charge + supplement_cost + subsistence_days_cost
    end
  end

  def manager_balance
    # TODO: have added defaults to tables instead of this, remove if no problems
    # return 0.0 unless total_cost_manager #Fixes issue with nil total cost manager
    total_cost_manager - (total_paid_manager - total_stripe_refund)
  end

  def add_payment!(pounds)
    raise 'only for card pay' unless card_pay?

    card_payment = StripeCardPayment.new
    currency = acc_booking_header.rfq_location.accepted_response.currency.code
    payment.additional_booking_charge(self, pounds, currency)
  end

  def additional_info
    'Additional info'
  end

  def self.send_confirmation(booking_id)
    AccBookingMailer.delay.send_booking_confirmation(booking_id)
  end

  def self.send_due_joining_instructions
    AccBooking.each do |booking|
      if booking.hotel_has_confirmed? && booking.can_send_ji?(true)
        AccBookingMailer.delay.send_joining_instructions(booking.id)
      end
    end
  end

  def has_been_amended?
    return false unless confirmed?

    to_s != before_change_as_text
  end

  def has_been_amended_grp?
    return false unless confirmed?

    grp_info_string != before_change_as_text
  end

  def has_been_amended_adult?
    adult_info_string != before_change_as_text
  end

  def mark_as_amended_by_contact(contact, revert_hotel_confirmation = false)
    return unless confirmed?

    self.amended_by = contact.full_name
    self.amended_at = Time.zone.now
    self.hotel_confirmed_at = nil if acc_booking_person.person_type == 'TRA' && revert_hotel_confirmation
    save!
    if acc_booking_person.person_type == 'TRA'
      AccBookingMailer.delay.send_hotel_trainer_booking(id, 'amendment')
      AccBookingMailer.delay.send_trainer_booking(id, 'amendment', :booker)
      AccBookingMailer.delay.send_trainer_booking(id, 'amendment', :guest)
    else
      # asked to be removed by Victoria as part of the stripe amendments
      # AccBookingMailer.delay.send_booking_confirmation(self.id) unless (self.acc_booking_header.rfq_location.rfq_request.no_ji? && self.hotel_confirmed_at.blank?)
    end
  end

  def mark_as_amended_by_contact_grp(contact)
    return unless has_been_amended_grp?

    self.amended_by = contact.full_name
    self.amended_at = Time.zone.now
    save!
    CsBookingMailer.delay.booking_details(id, 'AMENDMENT') unless no_guest_email == '1'
    CsBookingMailer.delay.hotel_booking_details(id, 'AMENDMENT')
  end

  def mark_as_amended_by_contact_adult(contact)
    return unless has_been_amended_adult?

    diffs = BookingDiff.new(before_change_as_arr, adult_info_arr, ADULT_NAMES).email_version
    self.amended_by = contact.full_name
    self.amended_at = Time.zone.now
    save!

    AdultAccommodationMailer.delay.booking_details(id, 'AMENDMENT', diffs)
    AdultAccommodationMailer.delay.hotel_booking_details(id, 'AMENDMENT', diffs)
    return if no_guest_email == '1'

    AdultAccommodationMailer.delay.send_booking_confirmation(id, 'AMENDMENT',
                                                             diffs)
  end

  def room_type_for_confirmation
    if room_type == 'Twin for 2 people'
      'Twin'
    else
      room_type
    end
  end

  def pkg_type_long
    case pkg_type
    when 'BB'
      'Bed and Breakfast'
    when 'RO'
      'Room Only'
    when 'DBB'
      'Dinner Bed and Breakfast'
    when 'TWENTY'
      '24 Hour'
    else
      pkg_type
    end
  end

  def comp_room_avail?
    # return true if [185720, 185421].include?(self.id)
    if @comp_room_avail_flag.present?
      @comp_room_avail_flag
    else
      @comp_room_avail_flag = comp_room_check
    end
  end

  def comp_room_check
    return false unless acc_booking_person.trainer?
    return false unless first_checkin.present? && last_check_out.present?
    return false unless last_check_out > first_checkin
    return false unless acc_booking_header.rfq_location.accepted_response.comp_room_qty.to_i > 0

    ar = acc_booking_header.rfq_location.accepted_response
    min_bookings = ar.comp_grp_min.to_i
    max_comp = ar.comp_room_qty.to_i

    return false if max_comp == 0

    ((first_checkin)..(last_check_out - 1)).each do |day_to_check|
      base_query = AccBooking.joins(:acc_booking_stays, :acc_booking_header)
                             .where(acc_booking_headers: { rfq_location_id: acc_booking_header.rfq_location_id })
                             .where('acc_booking_stays.check_in <= ?', day_to_check)
                             .where('acc_booking_stays.check_out > ?', day_to_check)
                             .where(hotel_id: ar.rfq_proposed_hotel.hotel_id, cancelled_at: nil, deleted_at: nil)

      base_query = base_query.where.not(id: id) if persisted?

      return false if base_query.where(complimentary: false).count < min_bookings
      return false if base_query.where(complimentary: true).count >= max_comp
    end
    true
  end

  def create_ji_chase!
    chase = acc_booking_chases.new(
      chase_type: 'email',
      comment: 'System generated email',
      outcome: 'Joining Instructions Resent',
      assigned_to_id: '999999',
      due_at: Time.zone.now
    )

    raise "ji chase is not valid: #{chase.errors.full_messages.to_sentence}" unless chase.valid?

    chase.save_and_record
  end

  def resolve!(user)
    self.resolved_at = Time.zone.now
    self.resolved_by = user.full_name_or_email
    save(validate: false)
  end

  def under_adjustment?
    acc_booking_adjusts.present? && acc_booking_adjusts.any? do |adj|
      (adj.non? && !adj.confirmed?) || (adj.early? && adj.action != 'UNCONFIRMED')
    end
  end

  def people_output
    out = ''
    out << "#{try(:acc_booking_person).try(:forename)} #{try(:acc_booking_person).try(:surname)}"
    acc_additional_people.each do |person|
      out << '<br/>'
      out << "#{person.try(:forename)} #{person.try(:surname)}"
    end
    out.html_safe
  end

  def s_at_twinnable?
    room_type == 'Twin for 2 people' &&
      acc_booking_person.person_type == 'LEA'
  end

  def adult_rooms_selections
    rfq_response_room.rfq_response.rfq_response_rooms.where(cannot_supply: false)
  end

  def rfq_programme_name
    acc_booking_header.rfq_location.rfq_request.rfq_programme.name
  end

  def value_check
    {
      first_checkin: first_checkin,
      last_checkout: last_check_out,
      hotel_id: hotel_id,
      room_type: room_type,
      pkg_type: pkg_type,
      transport_included: transport_included,
      pppn_inc_vat: pppn_inc_vat,
      total_nights: total_nights,
      rfq_response_room_id: rfq_response_room_id,
      no_nights_charge_manager: no_nights_charge_manager,
      supplement_pppn: supplement_pppn,
      subsistence_pppn: subsistence_pppn,
      subsistence_days: subsistence_days,
      total_due: total_due,
      supplement_cost: supplement_cost,
      subsistence_days_cost: subsistence_days_cost,
      extra_nights_charge: extra_nights_charge,
      supplement_required_for_x_nights: supplement_required_for_x_nights,
      subsistence_ony: subsistence_only,
      stays: acc_booking_stays.collect { |x| [x.check_in, x.check_out] }.sort_by { |x| x[0] }

    }
  end

  def has_hotel_confirmed_if_required
    return true if subsistence_only? || !acc_booking_header.rfq_location.rfq_request.no_ji_unless_hotel_confirmed

    acc_booking_header.rfq_location.rfq_request.no_ji_unless_hotel_confirmed && hotel_confirmed_at.present?
  end

  def can_hotel_recieve_cancellation_email?
    rfq_request = acc_booking_header.rfq_location.rfq_request
    return false if hotel.blank? || subsistence_only? || virtual_flag
    return false if rfq_request.no_ji_unless_hotel_confirmed && hotel_confirmed_at.blank?
    return false if !rfq_request.no_ji_unless_hotel_confirmed && confirmed_at.blank?

    true
  end

  def can_send_ji?(check_for_late = false)
    build_booking_dates
    if !self.virtual_flag? && !booking_dates.any? && !acc_week_blocks.any?
      errors.add(:base, 'No dates for booking')
      return false
    end
    if check_for_late && (late_booking? && hotel_confirmed_at.nil?)
      errors.add(:base, 'Booking is late therefore JI cannot currently be sent')
      return false
    end
    if confirmed? || cancelled?
      errors.add(:base, "Can't send JI as booking is already confirmed/cancelled")
      false
    elsif acc_booking_header.rfq_location.rfq_request.no_ji?
      errors.add(:base, "Can't send JI as Request says no JI")
      false
    #  This bit commented out, as raised as issue ISTL-633
    # elsif self.first_checkin < Date.today
    #   self.errors.add(:base, "Can't send JI as first check in has already passed")
    #   return false
    elsif !has_hotel_confirmed_if_required
      errors.add(:base, 'JI cannot be sent until hotel confirms availability')
      false
    # don't run this if booking has been marked as non arrival, because it'll break the training dates get

    elsif !acc_booking_adjusts.non_arrivals.any? && !virtual_flag && (get_dates(true).first) > (Date.today + ji_days_in_advance.days)
      errors.add(:base, "JI cannot be sent if first checkin is greater than #{(Date.today + ji_days_in_advance.days).to_fs(:uk)}")
      false
    elsif booking_payments.pending.any?
      errors.add(:base, 'JI cannot be sent if there are pending payments')
      false
    else
      true
    end
  end

  def can_send_ji_to_lrnr?
    rfq = acc_booking_header.rfq_location.rfq_request
    confirmed? &&
      !cancelled? &&
      !rfq.no_ji? &&
      rfq.ji_to_learner? &&
      acc_booking_person.rfq_learner.present? &&
      !acc_booking_person.disabled?
  end

  def hotel_confirmation
    if acc_booking_person.person_type == 'LEA' &&
       acc_booking_header.rfq_location.rfq_request.no_ji? &&
       hotel_confirmed_at.present? &&
       hotel_confirmed_at_changed?
      AccBookingMailer.delay.send_booking_confirmation(id)
    end
  end

  def self.to_be_default_sunday
    joins(:acc_booking_person, acc_booking_header: { rfq_location: { rfq_request: :rfq_programme } })
      .where('rfq_programmes.client_id = 4289')
      .where("acc_booking_people.person_type = 'LEA'")
      .where('rfq_requests.sunday_default is true')
      .where('acc_bookings.cancelled_at is null')
      .where('acc_bookings.confirmed_at is null')
      .where('acc_bookings.first_checkin > ? ', Date.today)
      .order('acc_bookings.id ASC')
  end

  def self.display_first_day(ids = [])
    includes(:acc_booking_person).where(id: ids).map do |b|
      [b.id, b.acc_booking_person.name_for_table, Date::ABBR_DAYNAMES[b.first_checkin.wday]].join(',')
    end.join('**')
  end

  # def self.process_sunday_default!(bookings)
  #   bookings.each do |b|
  #     b.acc_week_blocks.each do |wb|
  #       wb.sun = true if wb.mon?
  #     end
  #     b.save
  #     b.reset_stays
  #   end
  # end

  def ignore_dupes!(user)
    update_columns(dupes_ignored_at: Time.zone.now, dupes_ignored_by: user.contact.full_name)
  end

  def dupes_ignored?
    dupes_ignored_at.present?
  end

  def self.check_dupe_learner_bookings(st, nd)
    books_to_check = AccBooking.joins({ acc_booking_person: :rfq_learner }, :acc_booking_stays).where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null').where(
      'acc_booking_stays.check_in >= ? and acc_booking_stays.check_in <=  ?', st, nd
    ).group('concat(rfq_learners.surname, rfq_learners.date_of_birth, acc_booking_stays.check_in)').count
    dupes = books_to_check.select { |k, v| v > 1 }
    AccBooking.joins({ acc_booking_person: :rfq_learner }, :acc_booking_stays).where(
      'concat(rfq_learners.surname, rfq_learners.date_of_birth, acc_booking_stays.check_in) in (?)', dupes.keys
    ).pluck(:id).sort
  end

  def self.daily_dupe_check
    books_to_check = AccBooking.joins({ acc_booking_person: :rfq_learner }, :acc_booking_stays).where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null').where(
      'date(acc_bookings.created_at) = ?', Date.today.yesterday
    ).group('concat(rfq_learners.surname, rfq_learners.date_of_birth, acc_booking_stays.check_in)').count
    dupes = books_to_check.select { |k, v| v > 1 }
    AccBooking.joins({ acc_booking_person: :rfq_learner }, :acc_booking_stays).where(
      'concat(rfq_learners.surname, rfq_learners.date_of_birth, acc_booking_stays.check_in) in (?)', dupes.keys
    ).pluck(:id).sort
  end

  def self.dupe_checks
    books_to_check = AccBooking.joins({ acc_booking_person: :rfq_learner }, :acc_booking_stays).where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null').where(
      'date(acc_bookings.created_at) >=?', Date.today - 6.months
    ).group('concat(rfq_learners.surname, rfq_learners.date_of_birth, acc_booking_stays.check_in)').count
    dupes = books_to_check.select { |k, v| v > 1 }
    AccBooking.joins({ acc_booking_person: :rfq_learner }, :acc_booking_stays).where(
      'concat(rfq_learners.surname, rfq_learners.date_of_birth, acc_booking_stays.check_in) in (?)', dupes.keys
    ).pluck(:id).sort
  end

  def self.check_dupe_learner_bookings_old
    ActiveRecord::Base.logger.level = 1
    bookings = {}
    a = 1
    AccBooking.where('acc_booking_people.rfq_learner_id is not null').where(cancelled_at: nil).joins(:acc_booking_person).find_in_batches(batch_size: 2000) do |batch|
      puts a
      a += 1
      batch.each do |x|
        key = "#{x.acc_booking_person.rfq_learner_id}-#{x.first_checkin}"
        bookings[key] ||= {}
        bookings[key]['count'] ||= 0
        bookings[key]['count'] = bookings[key]['count'] + 1
        bookings[key]['abhs'] ||= []
        bookings[key]['abhs'] << x.acc_booking_header_id unless bookings[key]['abhs'].include?(x.acc_booking_header_id)
      end
    end
    probs2 = bookings.select { |k, v| v['count'] > 1 }
  end

  def clear_room_if_subsistence
    # Remove unwanted room selection if subsistence chosen
    return unless subsistence_only?

    self.rfq_response_room_id = nil
  end

  def self.adult_booking_pay_types(rfq_request)
    pay_types = []

    pay_types << 'BACS / Credit Card (Paid to Hotel)'
    # pay_types << "Cheque"
    if !rfq_request.new_adult_booking_flow_toggle && rfq_request.enable_on_account? && (rfq_request.id != 194 || rfq_request.id != 224)
      pay_types << 'Business Account'
    end
    if rfq_request.advanced_payment_options?
      pay_types << 'Hotel invoice to client'
      pay_types << 'Invoice to HG'
    end
    # new stuff
    if rfq_request.card_pay?
      pay_types << 'Card'
    end
    pay_types
  end

  def get_booking_hotel
    return hotel if hotel.present?

    # Used when booking has been converted from residential to subsistence, and has therefore lost the booking link to hotel
    acc_booking_header.rfq_location.rfq_request.accepted_response.hotel
  end

  def get_booking_block_days
    # to calculate subsistence stay costs on booking-cost-info vue componenet
    block = acc_booking_header.acc_booking_blocks.where('end_date >= ? AND start_date <= ?', first_checkin,
                                                        last_check_out).first
    return [[Date.today, Date.today + 1]] unless block

    calculate_stays(true)
  end

  def get_block_days_count
    # to calculate subsistence stay costs on booking-cost-info vue componenet
    count = 0
    stays = get_booking_block_days
    stays.each do |stay|
      count += (stay[1] - stay[0]).to_i + 1
    end
    count
  end

  # hotel_confirmed_or_booked_more_than_4_weeks_in_advance
  def hotel_has_confirmed?
    return true if subsistence_only?

    !acc_booking_header.rfq_location.rfq_request.no_ji_unless_hotel_confirmed || hotel_confirmed_at.present?
  end

  def late_booking?
    # Had to put this in to process bookings from the supplier side, the join for acc_booking_header seems broken
    booking_header = has_attribute?(:acc_booking_header_id) ? acc_booking_header : AccBookingHeader.find(booking_ref)
    booking_header.rfq_location.rfq_request.late_bookings_enabled && (created_at + late_booking_threshold.day) >= first_checkin && !subsistence_only
  end

  def self.late_bookings_query(current_user = nil)
    query = joins(acc_booking_header: { rfq_location: { rfq_request: :rfq_programme } })
            .where(rfq_requests: { late_bookings_enabled: true }, hotel_confirmed_at: nil, hotel_declined_at: nil,
                   subsistence_only: false, virtual_flag: false, cancelled_at: nil)
            .where('(DATE(acc_bookings.created_at) + acc_bookings.late_booking_threshold) >= acc_bookings.first_checkin')
            .where('acc_bookings.first_checkin >= ?', Date.today)

    if current_user.present? && current_user.is_a_client?
      # Only show bookings where user is a contact in the bookings programme
      query = query.joins(acc_booking_header: { rfq_location: { rfq_request: { rfq_programme: :contacts } } })
                   .where(rfq_programmes: { client_id: current_user.contact.parent.id }, contacts: { id: current_user.contact.id })
    end

    if current_user.present? && (current_user.is_a_hotel_user? || current_user.is_a_chain_user? || current_user.is_a_cluster_user?)
      # if hotel it should just get 1, else it should get multiple
      query = query.where(hotel_id: current_user.all_hotels)
    end
    query
  end

  def hotel_confirm(current_user)
    unless update(hotel_confirmed_at: DateTime.now, hotel_confirmed_by_user_id: current_user.id)
      raise 'Booking could not be confirmed'
    end

    if acc_booking_person.person_type == 'TRA'
      # if trainer booking confirm when hotel confirms
      update(confirmed_flag: true, confirmed_at: Time.zone.now, confirmed_by: current_user.contact.full_name)
    end
    AccBookingMailer.delay.send_joining_instructions(id) if can_send_ji?
    if late_booking?
      audience = [:booker]
      audience << :person if acc_booking_person.person_type != 'PRO'
      audience.each do |audience|
        AccBookingMailer.delay.hotel_processed_booking(id, audience)
      end
    end
  rescue StandardError => e
    # add this back in if we remove validate: false
    errors.add(:base, e.message)
  end

  def hotel_decline!(user = nil, note_text = nil)
    self.hotel_declined_at = Time.zone.now
    self.hotel_declined_by_id = user.present? ? user.id : 'Not Found'
    self.cancelled_at = Time.zone.now
    self.cancelled_by = user.present? ? user.full_name_or_email : 'Token access'

    note = Note.new
    note.noteable_id = id
    note.noteable_type = 'AccBooking'
    note.note_subject = Note.note_subjects[:BookingDeclined]
    note.note_text = note_text
    note.user_id = user.present? ? user.id : nil
    note.user_type = Note.user_types[:Hotel]
    note.save
    save(validate: false)
    audience = %i[hg booker]
    audience << :person if acc_booking_person.person_type != 'PRO' && acc_booking_person.email.present?
    audience.each do |audience|
      AccBookingMailer.delay.hotel_processed_booking(id, audience)
    end
  end

  def stripe_payments_pending?
    booking_payments.any? { |bp| bp.status == 'pending' }
  end

  def room_rate_plus_fees(include_stripe_fees = true)
    include_stripe_fees = false if acc_booking_person.person_type == 'TRA'
    rfq_request = acc_booking_header.rfq_location.rfq_request

    room_amount = subsistence_only? || rfq_response_room.blank? ? 0 : rfq_response_room.price_inc_vat * total_nights
    fees_amount = 0

    if fee.present? && !rfq_request.use_stripe_connect
      fees_amount += calculate_subsistence_fee if should_charge_subsistence?
    elsif rfq_request.subsistence_cost.present? && rfq_request.subsistence_req?
      fees_amount += (rfq_request.subsistence_cost * get_training_dates.count)
    end

    total = room_amount + fees_amount
    total += calculate_stripe_with_vat(total) if include_stripe_fees && card_pay?

    total.round(2).to_f # add stripe fees
  end

  def get_fee_breakdown(include_stripe_fees = true)
    include_stripe_fees = false if self.acc_booking_person.person_type == 'TRA'
    output = ''
    rfq_request = self.acc_booking_header.rfq_location.rfq_request

    room_amount = (self.subsistence_only? || self.virtual_flag? || self.rfq_response_room.blank?) ? 0 : self.rfq_response_room.price_inc_vat
    fees_amount = 0

    fees = self.fee

    if fees
      if fees.subsistence_fees.present? && should_charge_subsistence?
        fees_amount += calculate_subsistence_fee
      end
    end

    total = (room_amount * self.total_nights) + (fees_amount)

    output << "<p>#{self.total_nights} nights Accommodation @ £#{"%.2f" % room_amount} per night (Inc VAT) (£#{"%.2f" % (room_amount * self.total_nights)})</p>" if !self.subsistence_only?

    if fees
      training_days = self.get_training_dates
      if fees.subsistence_fees.present? && should_charge_subsistence?
        output << "<p>#{no_weekends_or_bank_holidays(training_days).count} days Subsistence fee (VAT Exc) @ £#{"%.2f" % fees.subsistence_fees}  (£#{"%.2f" % (calculate_subsistence_fee)})</p>"
      end
    end
    if include_stripe_fees && self.card_pay?
      stripe_fees = calculate_servace_stripe_fee_total(total)
      output << "<p>Stripe transaction percentage fee #{get_stripe_percentage_fee}% (Plus VAT) (£#{ stripe_fees })</p>"
      output << "<p>VAT:(£#{ calculate_vat_fee_total(stripe_fees).round(2)})</p>"
    end
    return output
    # Stripe fee at 2%
  end

  def no_weekends_or_bank_holidays(dates)
    dates.select { |date| !(date.saturday? || date.sunday?) && !BankHoliday.is_bank_holiday?(date) }
  end

  def hotel_confirmed_by
    return unless hotel_confirmed_by_user.present?

    hotel_confirmed_by_user.full_name_or_email
  end

  def calculate_room_cost(cancelled_0_override = true)
    return 0 if subsistence_only? || rfq_response_room_id.blank? || (cancelled_at.present? && cancelled_0_override)

    (rfq_response_room.price_inc_vat * total_nights).round(2)
  end

  def calculate_subsistence_fee(cancelled_0_override = true)
    if (cancelled_at.present? && cancelled_0_override) || !should_charge_subsistence?
      return 0
    end

    training_days = get_training_dates
    ((self&.fee&.subsistence_fees || 0) * no_weekends_or_bank_holidays(training_days).count).round(2)
  end

  def calculate_servace_stripe_fee_total(total)
    STRIPE_FEE_RATE_PERCENT.percent_of(total).round(2)
  end

  def calculate_vat_fee_total(total)
    VAT_RATE.percent_of(total).round(2)
  end

  def calculate_stripe_with_vat(total)
    stripe_fee = calculate_servace_stripe_fee_total(total)
    vat = calculate_vat_fee_total(stripe_fee)
    stripe_fee + vat
  end

  def get_stripe_percentage_fee
    STRIPE_FEE_RATE_PERCENT || StripeFee.last.stripe_rate
  end

  def calculate_pre_fee_booking_cost
    calculate_room_cost + calculate_subsistence_fee
  end

  def extract_vat_from_cost(total)
    # only pass pence into this or the round will break it
    (total - (total / 1.2)).round
  end

  def extract_stripe_plus_vat_from_cost(total)
    (total - (total / 1.024)).round
  end

  def extract_vat_from_stripe_cost(total)
    # only pass pence into this or the round will break it
    total_extract = extract_stripe_plus_vat_from_cost(total)
    extract_vat_from_cost(total_extract).round
  end

  def extract_stripe_from_cost(total)
    # only pass pence into this or the round will break it
    total_extract = extract_stripe_plus_vat_from_cost(total).round
    total_extract - (extract_vat_from_cost(total_extract)).round
  end

  def calculate_booking_commission
    commission_raw_value = self&.acc_booking_header&.rfq_location&.accepted_response&.commission
    return 0 unless commission_raw_value.present?

    commission_rate = commission_raw_value / 100
    room_cost = calculate_room_cost

    total = room_cost * commission_rate
    vat = calculate_vat_fee_total(total)
    (total + vat).round(2)
  end

  def set_servace_fees!
    fee = acc_booking_header.rfq_location.rfq_request.get_fees_for_rfq
    return unless fee.present?

    self.fee_id = fee.id
    save
  end

  def ji_days_in_advance
    if joining_instruction.nil? # if no new JIs have been made
      number_of_days = 42 # 6 weeks
    else
      number_of_days = joining_instruction.no_of_days_in_advance_to_send
      if joining_instruction.no_of_days_in_advance_to_send_business_days
        working_days_date = add_working_days(Date.today,
                                             Date.today + joining_instruction.no_of_days_in_advance_to_send)
        number_of_days = (Date.today..working_days_date).count
      end
    end
    number_of_days
  end

  def add_working_days(date_from, date_to)
    date = date_from
    # +1 day because we don't count today
    ((date_from + 1.day)..date_to).each do
      if !date.weekday? || BankHoliday.where(on: date).present?
        date = date.next_weekday
      else
        date += 1.day
      end
    end
    date
  end

  def training_dates
    booking_dates.where(week_type: BookingDate.week_types['training'])
  end

  def accommodation_dates
    booking_dates.where(week_type: BookingDate.week_types['accommodation'])
  end

  def total_paid
    # This is + because the amount field on refunds is negative
    if booking_payments.any?
      (booking_payments.charged.sum(&:amount) + booking_payments.refunded.sum(&:amount)).to_f / 100
    else
      total_cost_manager
    end
  end

  def build_booking_dates
    return unless acc_week_blocks.any? && booking_dates.empty?

    # build new accommodation weeks
    acc_week_blocks.each do |week|
      booking_dates.build(
        week_type: BookingDate.week_types['accommodation'], start_date: week.start_date,
        sun: week.sun,
        mon: week.mon,
        tue: week.tue,
        wed: week.wed,
        thu: week.thu,
        fri: week.fri,
        sat: week.sat
      )
    end
    # build new training weeks
    acc_booking_block.acc_week_blocks.each do |week|
      booking_dates.build(
        week_type: BookingDate.week_types['training'], start_date: week.start_date,
        sun: week.sun,
        mon: week.mon,
        tue: week.tue,
        wed: week.wed,
        thu: week.thu,
        fri: week.fri,
        sat: week.sat
      )
    end
    save!
  end

  def is_stripe_connect
    return acc_booking_header.rfq_location.rfq_request.use_stripe_connect unless booking_payments.charged.any?

    all_connect = booking_payments.charged.all? { |p| p.stripe_connect_payment == true }
    all_servace = booking_payments.charged.all? { |p| p.stripe_connect_payment == false }
    if all_connect
      true
    elsif all_servace
      false
    else
      nil
    end
  end

  def booking_resolved_note
    note = notes.where(note_subject: Note.note_subjects[:BookingResolved]).first
    if note.present?
      note.note_text
    else
      ''
    end
  end

  def send_monthly_vat_summaries_to_managers
    month = Date.today.month - 1
    year = Date.today.year
    year -= 1 if month == 0 # fix for january(1) turning into 0 instead of 12
    month = 12 if month == 0 # fix for january(1) turning into 0 instead of 12
    bookings = AccBooking.joins(:booking_payments, acc_booking_person: { rfq_learner: :rfq_business_unit })
                         .where(booking_payments: { stripe_connect_payment: false })
                         .where('extract(month from first_checkin) = ?', month)
                         .where('extract(year from first_checkin) = ?', year)
                         .where.not(confirmed_at: nil).where(cancelled_at: nil, card_pay: true).distinct
    bookings_grouped_by_manager = bookings.group_by do |booking|
      booking.acc_booking_person.get_person.manager_email.strip.downcase
    end
    bookings_grouped_by_manager.each do |manager, bookings|
      puts "Sending #{bookings.count} bookings in a summary to #{manager}"
      AccBookingMailer.delay.vat_summary(manager, bookings.map(&:id))
    end
    bookings_grouped_by_finance_manager = bookings.where('rfq_business_units.send_finance_manager_joining_instructions = true and rfq_business_units.finance_manager_email is not null')
    bookings_grouped_by_finance_manager = bookings_grouped_by_finance_manager.group_by do |booking|
      booking.acc_booking_person.get_person.rfq_business_unit.finance_manager_email.strip.downcase
    end
    bookings_grouped_by_finance_manager.each do |manager, bookings|
      puts "Sending #{bookings.count} bookings in a summary to #{manager}"
      AccBookingMailer.delay.vat_summary(manager, bookings.map(&:id))
    end
  end

  def auto_pay_booking_for_tests
    return unless !confirmed? && card_pay? && !Rails.env.production?

    update(hotel_confirmed_at: Time.zone.now, hotel_confirmed_by_user_id: 15_128) # Josh Nesbitt user ID
    payment = StripeCardPayment.new
    intent = payment.create_payment_intent(self, room_rate_plus_fees, 'GBP')
    Stripe::PaymentIntent.confirm(intent.id, payment_method: 'pm_card_visa', return_url: 'https://www.example.com')
  end

  def confirmable?
    (!cancelled? && !confirmed?) && has_hotel_confirmed_if_required
  end

  def get_bu_account
    rfq_response_id = acc_booking_header.rfq_location.accepted_response.id
    rfq_business_unit_id = acc_booking_person.rfq_learner.rfq_business_unit_id
    RfqBuAccount.where(rfq_response_id: rfq_response_id, rfq_business_unit_id: rfq_business_unit_id).first
  end

  def should_send_apprentice_proforma?
    rfq_request = acc_booking_header.rfq_location.rfq_request
    total_due > 0.0 && is_residential? &&
    (rfq_request.payment_method != 'HG Client to Pay' || rfq_request.payment_method != 'HG to Pay')
  end

  def is_residential?
    !virtual_flag && !subsistence_only
  end

  def is_epa_subsistence?
    epa_booking && subsistence_only
  end

  def zero_charge_sub?
      person = acc_booking_person
      return false if person.nil? || person == 'TRA' || person&.person_type == 'TRA'
      bu = person&.get_person&.rfq_business_unit
      return false unless bu

      bu&.zero_chg_sub?
  end

  def should_charge_subsistence?
    rfq_request = acc_booking_header.rfq_location.rfq_request
    rfq_request.subsistence_req? && !zero_charge_sub?
  end
end
