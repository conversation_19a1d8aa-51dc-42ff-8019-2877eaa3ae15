class RfqLearner < ApplicationRecord

  # include ClientOwnable

  acts_as_paranoid  if (self.table_exists? && self.column_names.include?("deleted_at"))

  audited
  has_associated_audits

  attr_accessor :rfq_response_room_id, :subsistence_only, :needs_email, :manager_details,
                :hide_overriden_business_units, :read_users_cache, :person_disabled,
                :learner_emails, :next_of_kin_present, :levy, :notifications_allowed # Used for search when creating group bookings.

  belongs_to :organisation
  belongs_to :rfq_business_unit
  belongs_to :rfq_programme
  has_many :acc_booking_people
  has_many :acc_issue_allocations
  has_many :acc_issues, :through => :acc_issue_allocations
  has_many :appointments, foreign_key: :learner_id

  has_one :next_of_kin

  before_validation :strip_emails

  validates :forename, :surname, :email, :gender, :manager, :manager_email, :manager_telephone, :rfq_business_unit, :presence => true
  validates :date_of_birth, :presence => true, :unless => lambda{|x| x.adult?}

  validates :single_room_required_reason, :presence => true, :if => :single_room_required?

  validate :left_not_before_today
  def left_not_before_today
    errors.add(:left_at, "Left date cannot be set before today") if self.left && self.left_at_changed? && self.left_at < Date.today
    errors.add(:left_at, "is blank, must be set if leaving") if self.left && self.left_changed? &&  self.left_at.blank?
  end

  validates_inclusion_of :gender, :in => ["M","F","O"]

  validate :unique_person, :if => lambda{|o| o.reference.blank? && !o.force_create }, :on => :create
  def unique_person
    if self.rfq_business_unit.present?
      same_name = self.rfq_business_unit.rfq_learners.where(:forename => self.forename,
                                                            :middlename => self.middlename,
                                                            :surname => self.surname,
                                                            :date_of_birth => self.attributes['date_of_birth']).first
      if same_name.present?
        self.errors.add(:base, "This may be a duplicate of #{same_name.id}, #{same_name.reference}, #{same_name.full_name} at  #{same_name.rfq_business_unit.name} unit, please check the learners for this unit")
      end
    end
  end

  validate :unique_email, :on => :create
  def unique_email
    same_email_for_different_org = RfqLearner.where(email: self.email, left: false).where("organisation_id != ?", self.organisation_id).first

    if same_email_for_different_org.present?
      self.errors.add(:base, "This is a duplicate of #{same_email_for_different_org.email} in another organisation, please use a different email address")
      return
    end
    same_email = self&.rfq_programme&.rfq_learners&.where(:email => self.email)&.first
    if same_email.present?
      self.errors.add(:base, "This is a duplicate of #{same_email.email}, at  #{same_email.rfq_programme.name} programme, please check the learners for this unit")
      return
    end

    if self.rfq_business_unit.present?
      same_email = self&.rfq_business_unit&.rfq_learners&.where(:email => self.email)&.first

      if same_email.present?
        self.errors.add(:base, "This is a duplicate of #{same_email.email}, at  #{same_email.rfq_business_unit.name} unit, please check the learners for this unit")
      end
      return
    end
  end

  validate :bus_unit_exists, on: :update
  def bus_unit_exists
    self.errors.add(:rfq_business_unit_id, "This unit does not exist - please choose another") if self.rfq_business_unit.blank?
  end

  validate :email_addresses
  def email_addresses
    unless manager_email.blank?
      self.errors.add(:manager_email, "must not use 'mailto' in the email address") if self.manager_email =~ /\Amailto/
      self.errors.add(:manager_email, "address does not seem valid - please check it is like 'someone'@'domain'.'type' ") unless (self.manager_email =~ Devise.email_regexp)
    end
    unless email.blank?
      self.errors.add(:email, "must not use 'mailto' in the email address") if self.email =~ /\Amailto/
      self.errors.add(:email, "address does not seem valid - please check it is like 'someone'@'domain'.'type' ") unless (self.email =~ Devise.email_regexp)
    end

    unless employer_contact_email.blank?
      self.errors.add(:employer_contact_email, "must not use 'mailto' in the email address") if self.employer_contact_email =~ /\Amailto/
      self.errors.add(:employer_contact_email, "address does not seem valid - please check it is like 'someone'@'domain'.'type' ") unless (self.employer_contact_email =~ Devise.email_regexp)
    end
  end

  validates :telephone, allow_blank: true, :numericality => true, :length => { :minimum => 10, :maximum => 15 }

  validate :client_hidden_only_if_left
  def client_hidden_only_if_left
    if self.client_hidden? && !self.left? && self.left_changed?
      msg = "Client currently hidden.  Cannot restore learner as hidden from client, you must unhide before you can restore."
    elsif self.client_hidden? && !self.left?
      msg = "Cannot set to hidden unless already set to left"
    end
    errors.add(:client_hidden, msg) if msg
  end

  validate :has_email , :if => lambda{|x| x.needs_email}
  def has_email
    errors.add(:email, "is required because the programme has rfqs with learner confirmation enabled") if self.email.blank?
  end

  scope :still_here, -> {where(left: false, left_at: nil)}

  def initials
    [forename, middlename, surname].select{|x| x.present?}.collect{|x| x.first}.join()
  end

  def forenames
    [forename, middlename].select{|x| x.present?}.join(", ")
  end

  def full_name
    if middlename.present?
      [surname, forename + " " + middlename].join(", ")
    else
      [surname, forename].join(", ")
    end
  end

  def full_name_nice
    if middlename.present?
      [forename, middlename,surname].join(" ")
    else
      [forename,surname].join(" ")
    end
  end

  def full_name_learner
    full_name_nice + " (Learner)"
  end

  def first_initial_surname
    "#{forename[0,1].upcase}. #{surname.capitalize}"
  end

  def full_email
    return "" unless self.email.present?
    "#{forename} #{surname} <#{self.email}>"
  end

  def full_manager_email
    "#{manager} <#{manager_email}>"
  end

  def date_of_birth
    if self.adult?
      "Adult"
    else
      self.attributes['date_of_birth'].try(:strftime, "%d/%m/%Y")
    end
  end

  def age
    if self.adult?
      "Adult"
    else
      ((Time.zone.now - date_of_birth.to_time) / 1.year.seconds).floor
    end
  end

  def left?
    self.left_at.present?
  end

  def restore
    same_email_for_different_org = RfqLearner.where(email: self.email, left: false).where("organisation_id != ?", self.organisation_id).first
    if same_email_for_different_org.present?
      return false
    else
      self.update(left_at: nil, left: false, left_set_by: nil);
    end
    true
  end

  #only if there are no bookings
  def safe_to_destroy?
    self.acc_booking_people.count == 0
  end

  def set_manager
    if self.manager_details.present?
      mgr_arr = self.manager_details.split("||")
      self.manager = mgr_arr[0].strip
      self.manager_email = mgr_arr[1].strip
      self.manager_telephone = mgr_arr[2].strip
    end
  end

  def self.dummy_learners(prog_id, bus_id, qty=20)
    raise "Cannot put dummy learners into live data!!" if Rails.env.production?
    grps = ["r101", "zcars999", "p39", "h5"]
    (qty).times do
      l = RfqLearner.new
      l.rfq_business_unit_id = bus_id
      l.forename = Faker::Name.first_name
      l.surname = Faker::Name.last_name
      l.date_of_birth = Date.new(((Date.today.year - 16) + rand(2)), (1 + rand(11)), (1 + rand(25)))
      l.gender = ["M", "F", "O"].sample
      name = [Faker::Name.first_name, Faker::Name.last_name]
      l.manager = name.join(" ")
      l.manager_email = Faker::Internet.email(name.first)
      l.manager_telephone = Faker::Number.number(10)
      l.group_number  = grps.sample
      l.rfq_programme_id = prog_id
      l.save
      puts l.errors.full_messages.inspect
    end

  end

  def self.relink_programmes
    count = 0
    bad = []
    puts "There are #{RfqLearner.where("rfq_programme_id is null").count} learners to fix"
    puts "working ..."
    RfqLearner.where("rfq_programme_id is null").each do |l|
      if l.rfq_business_unit.present? && l.rfq_business_unit.rfq_programme_id.present?
        l.update_column(:rfq_programme_id, l.rfq_business_unit.rfq_programme_id)
        count += 1
        puts "fixed id: #{l.id}"
      else
        bad << l.id
      end
    end
    puts "fixed #{count} learners"
    puts "there were #{bad.size} not updated - ids => #{bad.inspect}"
  end

  def bookings
    AccBooking.joins(:acc_booking_person => :rfq_learner).
                           where("rfq_learners.id = ?", self.id)
  end

  def left_bookings
    # return [] if self.left_at.blank?
    AccBooking.joins(:acc_booking_person => :rfq_learner).
                            where("rfq_learners.id = ?", self.id).
                            where("acc_bookings.first_checkin is not null and acc_bookings.first_checkin > ? ", Date.today).
                            where("acc_bookings.cancelled_at is null")
  end

  def self.cancel_left_bookings(l_id, booking_ids )
    learner = RfqLearner.find l_id
    return false if learner.left_at.blank?
    bookings = learner.left_bookings.where("acc_bookings.id in (?)", booking_ids)
    bookings.each do |b|
      b = AccBooking.find b.id
      b.cancelled_at = Time.zone.now
      b.cancelled_by = "system"
      b.cancellation_reason = "learner left (#{learner.left_at.to_s})"
      b.acc_booking_person.update_column(:disabled , true)
      b.set_refund_flags! if  (b.card_pay? && b.confirmed_at.present?)
      if b.save
        AccBookingMailer.delay.send_individual_cancellation(b.id)
      end
    end
  end

  def self.possible_duplicates(with_group_code =  false)
     pattern = with_group_code ? "concat(rfq_learners.rfq_programme_id,'-', rfq_learners.surname, rfq_learners.date_of_birth, rfq_learners.group_number)" : "concat(rfq_learners.rfq_programme_id,'-', rfq_learners.surname, rfq_learners.date_of_birth)"

    learners = RfqLearner.joins(:rfq_programme => :rfq_requests).where("rfq_requests.end_date >= ?", Date.today).where("rfq_learners.left_at is null").distinct.group(pattern).count()

    found = learners.select{|k,v| v > 1}

    RfqLearner.where(pattern + ' in(?)', found.keys)
  end

  def self.bulk_leave!(learners, user)
    learners.update_all(:left => true, :left_at => Time.zone.now, :left_set_by => user.full_name_or_email)
  end

  def hidden_text
     self.client_hidden? ? "Hidden" : ""
  end

  def age_at(day)
    unless self.adult?
      dob = self.date_of_birth.to_date
      age = day.year - dob.year
      if day.month < dob.month || (day.month == dob.month && dob.day > day.day)
        age = age - 1
      end
    else
      age = nil
    end
    return age
  end

  def has_mobile_access
    return RfqLearner.where("email = ?", self.email).where("password is not null").exists?
  end

  def invite_manager_to_new_servace_application(creator)
    contact = Contact.where("email = ?", self.manager_email).first_or_create do |mgr|
      mgr.email = self.manager_email if mgr.email.blank?
      name = self.manager.split(" ")
      mgr.first_name = name.first
      mgr.surname = name.last
      mgr.telephone = self.manager_telephone
      mgr.parent = self.rfq_programme.client
    end

    if contact.valid?
      contact.save
      # Invite the business unit manager to new servace application
      contact.create_servace_app_user_and_invite(creator)
    else
      # Handle the case where the contact is not valid
      Rails.logger.error("Failed to save contact: #{contact.errors.full_messages.join(", ")}")
    end
  end

  private

  def strip_emails
    self.try(:email).try(:strip!)
    self.try(:manager_email).try(:strip!)
  end

  def filtering?
    filtering == true
  end

end
