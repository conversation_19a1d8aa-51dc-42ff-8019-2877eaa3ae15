#encoding: utf-8

class RfqBusinessUnit < ApplicationRecord

  # include ClientOwnable

  acts_as_paranoid if (self.table_exists? && self.column_names.include?("deleted_at"))

  audited

  before_destroy :no_learners
  before_save :change_card_pay

  belongs_to :business_unit_manager, :class_name => "Contact", :foreign_key => "manager_id", :optional => true

  LEARNERS = { :reference => "Reference", :forename => "Fore Name", :middlename => "Middle Name", :surname => "Surname",
               :date_of_birth => "Date of Birth", :gender => "Gender(Male, Female or Other)", :email => "Email", :name => 'Unit',
               :address1 => 'Address 1', :address2 => 'Address 2', :address3 => "Address 3", :town => 'Town', :county => 'County', :postcode => 'Postcode',
               :manager => "Manager", :manager_email => "Manager Email", :manager_telephone => "Manager Phone", :group => "Group No.",
               :special_requirements => "Special Requirements", :note => "Notes" }

  belongs_to :rfq_programme
  belongs_to :csv_file, :class_name => 'Backdrop'
  has_many :rfq_learners
  has_many :rfq_joining_instructions
  has_many :acc_booking_people
  has_many :rfq_bu_accounts

  validates :postcode, presence: true
  validates :code, presence: true, uniqueness: { scope: :rfq_programme_id, conditions: -> { where(deleted_at: nil) } }
  validates :name, presence: true, uniqueness: { scope: [:rfq_programme_id, :postcode], conditions: -> { where(deleted_at: nil) } }

  validate :zero_only_if_prog_zero

  def invite_manager_to_new_servace_application(creator)
    contact = Contact.where("id = ? or email = ?", self.manager_id, self.email).first_or_create do |mgr|
        mgr.email = self.email if mgr.email.blank?
        name = self.contact_name.split(" ")
        mgr.first_name = name.first
        mgr.surname = name.last
        mgr.telephone = self.telephone
        mgr.parent = self.rfq_programme.client
     end

     self.business_unit_manager = contact
     self.save

     # Invite the business unit manager to new servace application
     self.business_unit_manager.create_servace_app_user_and_invite(creator)
  end

  def zero_only_if_prog_zero
    if self.zero_chg_sub? && !self.rfq_programme.zero_chg_sub?
      self.errors.add(:zero_chg_sub, "cannot be set unless the Rfq Programme has already been set to allow zero subsistence charge")
    end
  end

  validate :override_reason_if_overridden

  def override_reason_if_overridden
    if self.override_card_pay? && self.override_reason.blank?
      self.errors.add(:override_reason, "must be present if overriding card pay")
    end
  end

  def title
    "#{self.name} : #{self.address}"
  end

  def card_pay_bookings
    AccBooking.joins(:acc_booking_person => :rfq_learner, :acc_booking_header => { :rfq_location => :rfq_request }).
      where("rfq_learners.rfq_business_unit_id = ?", self.id).
      where("rfq_requests.card_pay is true")
  end

  def toggle_unconfirmed_bookings!
    # only override if the booking is not confirmed and the rfq is on connected accounts not pay to servace
    bookings = self.card_pay_bookings.joins(acc_booking_header: {rfq_location: :rfq_request}).
    where(rfq_requests: {use_stripe_connect: true}, acc_bookings: {confirmed_at: nil, confirmed_flag: false})
    if self.override_card_pay?
      bookings.update_all(:card_pay => false)
    else
      bookings.update_all(:card_pay => true)
    end
  end

  def change_card_pay
    if self.override_card_pay_changed?
      toggle_unconfirmed_bookings!
    end
  end

  def self.dummy_data(parent, qty = 10)
    raise "NOT FOR PRODUCTION" if Rails.env == "production"
    (qty).times do |x|
      bu = RfqBusinessUnit.new(:name => Faker::Commerce.department + x.to_s)
      bu.rfq_programme_id = parent
      bu.save
    end
  end

  def address_array
    [address1, address2, address3, town, county, postcode].select { |x| x.present? }
  end

  def address
    self.address_array.join("\n")
  end

  def name_and_code
    [code, name].select { |x| x.present? }.join(" - ")
  end

  def current_approved_ji
    self.rfq_joining_instructions.approved.order("approved_client_at DESC").first
  end

  def current_wip_ji
    out = self.rfq_joining_instructions.not_approved.order("updated_at DESC")
    out = out.where(epa_ji: false).first
    return out
  end

  def self.zero_chg_subs
    self.where(:zero_chg_sub => true)
  end

  def managers_for_select
    mans = self.rfq_learners.map { |l| [l.manager, l.manager_email, l.manager_telephone] }.
      uniq.
      sort.
      map { |marry| marry.join(" || ") }
  end

  def self.create_from_xls(programme_id, bdrop_id)
    programme = RfqProgramme.find programme_id
    bdrop = Backdrop.find bdrop_id
    return false if bdrop.blank?
    invalid_learners = []
    f = URI.open(bdrop.file.remote_url(:expires => Time.zone.now + 1.hour))
    book = Spreadsheet.open(f)
    sheet = book.worksheet(0)
    if sheet.row(0)[0] =~ /NB:\sfields\swith/ &&
      sheet.row(1)[0] =~ /Reference\(unique per business unit\)/
      start_row = 2
    elsif sheet.row(0)[0] =~ /Reference\(unique per business unit\)/
      start_row = 1
    else
      bdrop.csv_name = "error"
      bdrop.save
      bdrop.user.update_column(:backdrop_creating, true)
      return false
    end

    for i in start_row..sheet.last_row_index do
      row = sheet.row(i)
      next_of_kin = NextOfKin.new
      learner_exists = programme.rfq_learners.where(email: row[7].to_s).any? #row[7] is email
      next if row.all?(&:blank?) || learner_exists
      learner = RfqLearner.new
      learner.organisation_id = programme.client_id
      learner.reference = row[0].to_s
      learner.adult = row[1].to_s.downcase == 'yes' ? true : false
      learner.forename = row[2].to_s
      learner.middlename = row[3].to_s
      learner.surname = row[4].to_s
      learner.date_of_birth = row[5]
      learner.gender = row[6].to_s.upcase.strip
      learner.email = row[7].to_s
      learner.telephone = row[8].to_s
      learner.manager = row[17].to_s
      learner.manager_email = row[18].to_s
      learner.manager_telephone = row[19].to_s
      learner.group_number = row[20].to_s
      learner.special_requirements = row[21].to_s
      learner.note = row[22].to_s
      next_of_kin.forename = row[23].to_s
      next_of_kin.surname = row[24].to_s
      next_of_kin.phone = row[25].to_s
      next_of_kin.email = row[26].to_s
      next_of_kin.address = row[27].to_s
      if row[28].to_s.downcase == 'y'
        learner.force_create = true
      end

      #Additional
      learner.start_date = row[28]
      learner.proposed_end_date = row[29]
      learner.programme_name = row[30].to_s
      learner.course_code = row[31].to_s
      learner.additional_information = row[32].to_s
      learner.employer_name = row[33].to_s
      learner.employer_contact_email = row[34].to_s
      learner.employer_contact_number = row[35].to_s
      learner.levy = row[36].to_s.downcase == 'yes' ? true : false

      # New fields for remit
      learner.single_room_required = row[37].to_s.downcase == 'yes' ? true : false
      learner.single_room_required_reason = row[38].to_s
      learner.client_business_unit_id = row[39].to_s
      learner.client_learner_id = row[40].to_s

      # Check if the bus_unit exists
      #bus unit is validated by name and postcode and unique by name and postcode within a programme

      bus_unit = programme.rfq_business_units.where(:code => row[9].to_s).first

      #NOTE bus_unit.code IS the unique identiier for a business unit that must be opulated by the clients from their own systems

      # If the bus_unit does not exist, create a new one
    if bus_unit.blank?
        bus_unit = programme.rfq_business_units.create(
          :code => row[9].to_s,
          :name => row[10].to_s,
          :address1 => row[11].to_s,
          :address2 => row[12].to_s,
          :address3 => row[13].to_s,
          :town => row[14].to_s,
          :county => row[15].to_s,
          :postcode => row[16].to_s,
          :client_business_unit_id => row[39].to_s,
          :finance_manager_name => row[41].to_s,
          :finance_manager_email => row[42].to_s,
          :finance_manager_telephone => row[43].to_s,
          :rfq_programme_id => programme_id
        )
      end

      if bus_unit.present?
        learner.rfq_business_unit = bus_unit
        learner.rfq_programme_id = programme_id
        learner.created_by = bdrop.user.full_name_or_email
        if next_of_kin.valid?
          learner.next_of_kin = next_of_kin
        end
        if programme.has_learner_conf_rfqs?
          learner.needs_email = true
        end
        begin
          # Needs to check business unit, as validation not passed up to the learner
          if !bus_unit.valid? || !learner.save
            invalid_learners += [{ :learner => learner, :l_errors => learner.errors&.full_messages&.to_sentence, :b_unit => bus_unit, :b_errors => bus_unit.errors&.full_messages&.to_sentence, :next_of_kin => next_of_kin }]
          end
        rescue Exception => e
            invalid_learners += [{ :learner => learner, :l_errors => 'Invalid characters detected, please remove and re-attempt the upload.', :b_unit => bus_unit, :b_errors => bus_unit.errors.full_messages.to_sentence, :next_of_kin => next_of_kin }]
        end
      else
        invalid_learners += [{ :learner => learner, :l_errors => learner.errors.full_messages.to_sentence, :b_unit => bus_unit, :b_errors => bus_unit.errors.full_messages.to_sentence, :next_of_kin => next_of_kin, :nok_errors => next_of_kin.errors.full_messages.to_sentence }]
      end
    end
    if invalid_learners.any?
      self.create_invalid_xls(programme, invalid_learners, bdrop)
    else
      bdrop.destroy
    end
  end

  def self.create_invalid_xls(programme, learners, bdrop)
    filename = ""
    loop do
      filename = "rfq_learner_errors_prog_#{programme.id}-#{SecureRandom.hex(10)}.xls"
      break unless File.exist?("#{Rails.root}\/tmp\/#{filename}")
    end
    book = WriteExcel.new("#{Rails.root}\/tmp\/#{filename}")
    sheet = book.add_worksheet
    sheet.set_column(0, 24, 31)
    sheet.write_row(0, 0, ['NB: fields with an * are required and must be put in, other fields may be left blank. The file must be saved as an older excel format  .xls file NOT the newer .xlsx format. Next of kin need at least a name and a phone number otherwise the data will be ignored.'])
    sheet.write_row(0, 22, ["Next of Kin"])

    sheet.write_row(1, 0, ['Reference(unique per business unit)', 'Adult(yes/no)', 'Learner Fore Name*', 'Learner Middle Name', 'Learner Surname*', 'Learner Date of Birth(dd/mm/yyyy)*',
                           'Learner Gender(m/f/o)*', 'Learner Email', "Business Unit Code*", 'Business Unit Name*', 'BU Address1', "BU Address2", "BU Address3",
                           "BU Town", "BU County", "BU Postcode*", 'Manager*', 'Manager Email*',
                           'Manager Phone*', 'Group Code', 'Special Requirements', 'Notes', "NoK Forename", "NoK Surname", "NoK Phone", "NoK Email", "NoK Address", 'Force Create(Y/N)', 'Learner Errors', 'Unit Errors']
    )
    learners.each_with_index do |l, idx|
      dob = l[:learner].date_of_birth.strftime("%d/%m/%Y") rescue nil
      sheet.write_row((idx + 2), 0, [
        l[:learner].reference, l[:adult], l[:learner].forename,
        l[:learner].middlename, l[:learner].surname,
        dob,
        l[:learner].gender,
        l[:learner].email, l[:b_unit].code, l[:b_unit].name,
        l[:b_unit].address1, l[:b_unit].address2, l[:b_unit].address3,
        l[:b_unit].town, l[:b_unit].county, l[:b_unit].postcode,
        l[:learner].manager,
        l[:learner].manager_email, l[:learner].manager_telephone,
        l[:learner].group_number, l[:learner].special_requirements,
        l[:learner].note, l[:next_of_kin].forename, l[:next_of_kin].surname,
        l[:next_of_kin].phone, l[:next_of_kin].email, l[:next_of_kin].address,
        l[:learner].force_create, l[:l_errors], l[:b_errors], l[:nok_errors]
      ])
    end

    book.close
    bdrop.file = open("#{Rails.root}\/tmp\/#{filename}")
    bdrop.file.name = filename
    bdrop.save
    bdrop.user.update_column(:backdrop_creating, true)
    FileUtils.rm "#{Rails.root}\/tmp\/#{filename}" if File.exist?("#{Rails.root}\/tmp\/#{filename}")
  end

  def self.export_learners(p_id, bdrop_id)
    programme = RfqProgramme.find p_id
    bdrop = Backdrop.find bdrop_id
    learners = programme.rfq_learners
    filename = bdrop.csv_name
    book = WriteExcel.new("#{Rails.root}\/tmp\/#{filename}")
    sheet = book.add_worksheet
    sheet.set_column(0, 29, 31)
    sheet.write_row(0, 0, ['NB: fields with an * are required and must be put in, other fields may be left blank. The file must be saved as an older excel format  .xls file NOT the newer .xlsx format. Next of kin need at least a name and a phone number otherwise the data will be ignored.'])
    sheet.write_row(0, 25, ["Next of Kin"])
    sheet.write_row(0, 31, ["Additional"])

    sheet.write_row(1, 0, ['System ID', 'Reference(unique per business unit)', 'Adult(yes/no)', 'Learner Fore Name*', 'Learner Middle Name', 'Learner Surname*', 'Learner Date of Birth(dd/mm/yyyy)*',
                           'Learner Gender(m/f/o)*', 'Learner Email', 'Learner Telephone', 'Left?', 'Left On', 'Business Unit Code*', 'Business Unit Name*', 'BU Address1', "BU Address2", "BU Address3",
                           "BU Town", "BU County", "BU Postcode*", 'Manager*', 'Manager Email*',
                           'Manager Phone*', 'Group Code', 'Special Requirements', 'Notes', "NoK Forename", "NoK Surname", "NoK Phone", "NoK Email", "NoK Address", 'Force Create(Y/N)',
                           'Learner Start Date', 'Learner Proposed End Date', 'Programme Name', 'Course Code', 'Additional Information', 'Employer Name', 'Employer Contact Email',
                           'Employer Contact Number', 'Levy', 'Single Room Required(yes/no)', 'Single Room Required Reason', 'Client Business Unit ID', 'Client Learner ID', "Finance Manager Name", "Finance Manager Email", "Finance Manager Telephone", "Created At"]
    )
    learners.each_with_index do |l, idx|
      n = l.next_of_kin
      bus_unit = l.rfq_business_unit

      dob = l&.date_of_birth.to_date.strftime('%d-%b-%Y') rescue nil
      data = [
        l.id,
        l.reference, (l.adult? ? 'yes' : 'no'), l.forename,
        l.middlename, l.surname,
        dob,
        l.gender,
        l.email,
        l.telephone,
        (l.left? ? 'LEFT' : ""),
        (l.left? ? l.left_at.to_s : ""),
        bus_unit ? bus_unit.code : "",
        bus_unit ? bus_unit.name : "",
        bus_unit ? bus_unit.address1 : "",
        bus_unit ? bus_unit.address2 : "",
        bus_unit ? bus_unit.address3 : "",
        bus_unit ? bus_unit.town : "",
        bus_unit ? bus_unit.county : "",
        bus_unit ? bus_unit.postcode : "",
        l.manager,
        l.manager_email, l.manager_telephone,
        l.group_number, l.special_requirements,
        l.note]
      if n.present?
        data += [n.forename, n.surname, n.phone, n.email, n.address]
      else
        data += [nil, nil, nil, nil, nil]
      end
      data += [l.force_create]
      data += [l.start_date, l.proposed_end_date, l.programme_name, l.course_code, l.additional_information,
               l.employer_name, l.employer_contact_email, l.employer_contact_number, (l.levy ? "Yes" : "No"), (l.single_room_required ? "Yes" : "No"), l.single_room_required_reason,
               bus_unit ? bus_unit.client_business_unit_id : "",
               l.client_learner_id]
      data += [ bus_unit ? bus_unit.finance_manager_name : "", bus_unit ? bus_unit.finance_manager_email : "", bus_unit ? bus_unit.finance_manager_telephone : ""]
      data += [l.created_at&.to_fs(:long) + ' - ' + l.created_by]
      sheet.write_row((idx + 2), 0, data)
    end

    book.close
    bdrop.file = open("#{Rails.root}\/tmp\/#{filename}")
    bdrop.file.name = filename
    bdrop.save
    FileUtils.rm "#{Rails.root}\/tmp\/#{filename}" if File.exist?("#{Rails.root}\/tmp\/#{filename}")
  end

  def self.export_filtered_learners(p_id, bdrop_id, filtered_learner_ids)
    programme = RfqProgramme.find p_id
    bdrop = Backdrop.find bdrop_id

    # Use the filtered learner IDs to get only the filtered learners
    learners = programme.rfq_learners.where(id: filtered_learner_ids)

    filename = bdrop.csv_name
    book = WriteExcel.new("#{Rails.root}/tmp/#{filename}")
    sheet = book.add_worksheet
    sheet.set_column(0, 28, 30)
    sheet.write_row(0, 0, ['NB: fields with an * are required and must be put in, other fields may be left blank. The file must be saved as an older excel format  .xls file NOT the newer .xlsx format. Next of kin need at least a name and a phone number otherwise the data will be ignored.'])
    sheet.write_row(0, 24, ["Next of Kin"])
    sheet.write_row(0, 30, ["Additional"])

    sheet.write_row(1, 0, ['System ID', 'Reference(unique per business unit)', 'Adult(yes/no)', 'Learner Fore Name*', 'Learner Middle Name', 'Learner Surname*', 'Learner Date of Birth(dd/mm/yyyy)*',
                           'Learner Gender(m/f/o)*', 'Learner Email', 'Learner Telephone', 'Left?', 'Left On', 'Business Unit Code*', 'Business Unit Name*', 'BU Address1', "BU Address2", "BU Address3",
                           "BU Town", "BU County", "BU Postcode*", 'Manager*', 'Manager Email*',
                           'Manager Phone*', 'Group Code', 'Special Requirements', 'Notes', "NoK Forename", "NoK Surname", "NoK Phone", "NoK Email", "NoK Address", 'Force Create(Y/N)',
                           'Learner Start Date', 'Learner Proposed End Date', 'Programme Name', 'Course Code', 'Additional Information', 'Employer Name', 'Employer Contact Email',
                           'Employer Contact Number', 'Levy', 'Single Room Required(yes/no)', 'Single Room Required Reason', 'Client Business Unit ID', 'Client Learner ID', "Finance Manager Name", "Finance Manager Email", "Finance Manager Telephone"])

    learners.each_with_index do |l, idx|
      n = l.next_of_kin
      bus_unit = l.rfq_business_unit
      dob = l&.date_of_birth.to_date.strftime('%d-%b-%Y') rescue nil
      data = [
        l.id,
        l.reference, (l.adult? ? 'yes' : 'no'), l.forename,
        l.middlename, l.surname,
        dob,
        l.gender,
        l.email,
        l.telephone,
        (l.left? ? 'LEFT' : ""),
        (l.left? ? l.left_at.to_s : ""),
        bus_unit ? bus_unit.code : "",
        bus_unit ? bus_unit.name : "",
        bus_unit ? bus_unit.address1 : "", bus_unit ? bus_unit.address2 : "", bus_unit ? bus_unit.address3 : "",
        bus_unit ? bus_unit.town : "", bus_unit ? bus_unit.county : "", bus_unit ? bus_unit.postcode : "",
        l.manager,
        l.manager_email, l.manager_telephone,
        l.group_number, l.special_requirements,
        l.note
      ]
      if n.present?
        data += [n.forename, n.surname, n.phone, n.email, n.address]
      else
        data += [nil, nil, nil, nil, nil]
      end
      data += [l.force_create]
      data += [l.start_date, l.proposed_end_date, l.programme_name, l.course_code, l.additional_information,
               l.employer_name, l.employer_contact_email, l.employer_contact_number, (l.levy ? "Yes" : "No"), (l.single_room_required ? "Yes" : "No"), l.single_room_required_reason, bus_unit ? bus_unit.client_business_unit_id : "", l.client_learner_id]
      data += [bus_unit ? bus_unit.finance_manager_name : "", bus_unit ? bus_unit.finance_manager_email : "", bus_unit ? bus_unit.finance_manager_telephone : ""]
      sheet.write_row((idx + 2), 0, data)
    end

    book.close
    bdrop.file = open("#{Rails.root}/tmp/#{filename}")
    bdrop.file.name = filename
    bdrop.save
    FileUtils.rm "#{Rails.root}/tmp/#{filename}" if File.exist?("#{Rails.root}/tmp/#{filename}")
  end

  def self.update_learners_xls(p_id, bdrop_id)
    programme = RfqProgramme.find p_id
    bdrop = Backdrop.find bdrop_id
    return false if bdrop.blank?
    invalid_learners = []
    f = URI.open(bdrop.file.remote_url(:expires => Time.zone.now + 1.hour))
    book = Spreadsheet.open(f)
    sheet = book.worksheet(0)
    if sheet.row(0)[0] =~ /NB:\sfields\swith/ &&
      sheet.row(1)[0] =~ /System\sID/
      start_row = 2
    elsif sheet.row(0)[0] =~ /Reference\(unique per business unit\)/
      start_row = 1
    else
      bdrop.csv_name = "error"
      bdrop.save
      bdrop.user.update_column(:backdrop_creating, true)
      return false
    end
    for i in start_row..sheet.last_row_index do
      row = sheet.row(i)
      next if row.all?(&:blank?)
      learner = programme.rfq_learners.where(:id => row[0].to_i).first
      if learner.nil?
        invalid_learners += [{ :row => row, :errors => "Learner not found on this programme" }]
        next
      end

      learner.reference = row[1].to_s
      learner.adult = row[2].to_s.downcase == 'yes' ? true : false
      learner.forename = row[3].to_s
      learner.middlename = row[4].to_s
      learner.surname = row[5].to_s
      learner.date_of_birth = row[6]
      learner.gender = row[7].to_s.upcase.strip
      learner.email = row[8].to_s
      learner.telephone = row[9].to_s
      learner.manager = row[20].to_s
      learner.manager_email = row[21].to_s
      learner.manager_telephone = row[22].to_s
      learner.group_number = row[23].to_s
      learner.special_requirements = row[24].to_s
      learner.note = row[25].to_s
      next_of_kin = learner.next_of_kin || NextOfKin.new
      next_of_kin.forename = row[26].to_s
      next_of_kin.surname = row[27].to_s
      next_of_kin.phone = row[28].to_s
      next_of_kin.email = row[29].to_s
      next_of_kin.address = row[30].to_s
      if next_of_kin.new_record? && next_of_kin.valid?
        learner.next_of_kin = next_of_kin
      end
      if programme.has_learner_conf_rfqs?
        learner.needs_email = true
      end

      learner.start_date = row[32]
      learner.proposed_end_date = row[33]
      learner.programme_name = row[34].to_s
      learner.course_code = row[35].to_s
      learner.additional_information = row[36].to_s
      learner.employer_name = row[37].to_s
      learner.employer_contact_email = row[38].to_s
      learner.employer_contact_number = row[39].to_s
      learner.levy = row[40].to_s.downcase == 'yes' ? true : false

       # New fields for remit
      learner.single_room_required = row[41].to_s.downcase == 'yes' ? true : false
      learner.single_room_required_reason = row[42].to_s
      learner.client_business_unit_id = row[43].to_s
      learner.client_learner_id = row[44].to_s

      unless learner.save && (learner.next_of_kin.blank? || learner.next_of_kin.valid?)
        invalid_learners += [{ :row => row, :errors => learner.errors.full_messages.to_sentence, :nok_errors => next_of_kin.errors.full_messages.to_sentence }]
      end
    end
    puts "invalid learners: #{invalid_learners.inspect}"
    if invalid_learners.any?
      RfqBusinessUnit.update_errors_xls(programme, invalid_learners, bdrop)
    end
  end

  def self.update_bus_xls(p_id, bdrop_id)
    programme = RfqProgramme.find p_id
    bdrop = Backdrop.find bdrop_id
    return false if bdrop.blank?
    invalid_bus = []
    f = URI.open(bdrop.file.remote_url(:expires => Time.zone.now + 1.hour))
    book = Spreadsheet.open(f)
    sheet = book.worksheet(0)
    if sheet.row(0)[0] =~ /NB:\sfields\swith/ && sheet.row(1)[0] =~ /System\sID/
      start_row = 2
    elsif sheet.row(0)[0] =~ /Reference\(unique per business unit\)/
      start_row = 1
    else
      bdrop.csv_name = "error"
      bdrop.save
      bdrop.user.update_column(:backdrop_creating, true)
      return false
    end

    for i in start_row..sheet.last_row_index do
      row = sheet.row(i)
      next if row.all?(&:blank?)
      business_unit = programme.rfq_business_units.where(:id => row[0].to_i).first_or_initialize

      business_unit.id = row[1].to_s
      business_unit.name = row[2].to_s
      business_unit.contact_name = row[3].to_s
      business_unit.contact_telephone = row[4].to_s
      business_unit.contact_email = row[5].to_s
      business_unit.postcode = row[6].to_s
      business_unit.address1 = row[7].to_s
      business_unit.address2 = row[8].to_s
      business_unit.address3 = row[9].to_s
      business_unit.town = row[10].to_s
      business_unit.county = row[11].to_s
      business_unit.finance_manager_email = row[12].to_s
      business_unit.finance_manager_name = row[13].to_s
      business_unit.finance_manager_telephone = row[14].to_s


      unless business_unit.save
        invalid_bus += [{ :row => row, :errors => business_unit.errors.full_messages.to_sentence }]
      end
    end

    puts "invalid business units: #{invalid_bus.inspect}"
  end

  def self.update_errors_xls(programme, invalid_learners, bdrop)
    filename = ""
    loop do
      filename = "rfq_learner_errors_prog_#{programme.id}-#{SecureRandom.hex(10)}.xls"
      break unless File.exist?("#{Rails.root}\/tmp\/#{filename}")
    end
    book = WriteExcel.new("#{Rails.root}\/tmp\/#{filename}")
    sheet = book.add_worksheet
    sheet.set_column(0, 29, 30)
    sheet.write_row(0, 0, ['NB: fields with an * are required and must be put in, other fields may be left blank. The file must be saved as an older excel format  .xls file NOT the newer .xlsx format'])
    sheet.write_row(1, 0, ['System ID', 'Reference(unique per business unit)', 'Adult(yes/no)', 'Learner Fore Name*', 'Learner Middle Name', 'Learner Surname*', 'Learner Date of Birth(dd/mm/yyyy)*',
                           'Learner Gender(m/f/o)*', 'Learner Email', 'Left?', 'Left On', 'Business Unit Code*', 'Business Unit Name*', 'BU Address1', "BU Address2", "BU Address3",
                           "BU Town", "BU County", "BU Postcode*", 'Manager*', 'Manager Email*',
                           'Manager Phone*', 'Group Code', 'Special Requirements', 'Notes', "NoK Forename", "NoK Surname", "NoK Phone", "NoK Email", "NoK Address", 'Learner Errors', 'NoK Errors']
    )
    invalid_learners.each_with_index do |l, idx|

      sheet.write_row((idx + 2), 0, [l[:row][0],
                                     l[:row][1], l[:row][2],
                                     l[:row][3], l[:row][4],
                                     l[:row][5],
                                     l[:row][6],
                                     l[:row][7], l[:row][8],
                                     l[:row][9], l[:row][10], l[:row][11],
                                     l[:row][12], l[:row][13], l[:row][14],
                                     l[:row][15],
                                     l[:row][16], l[:row][17],
                                     l[:row][18], l[:row][19],
                                     l[:row][20], l[:row][21], l[:row][22],
                                     l[:row][23], l[:row][24], l[:row][25],
                                     l[:row][26], l[:row][27], l[:row][28], l[:row][29],
                                     l[:errors], l[:nok_errors]
      ])
    end

    book.close
    bdrop.file = open("#{Rails.root}\/tmp\/#{filename}")
    bdrop.file.name = filename
    bdrop.save
    bdrop.user.update_column(:backdrop_creating, true)
    FileUtils.rm "#{Rails.root}\/tmp\/#{filename}" if File.exist?("#{Rails.root}\/tmp\/#{filename}")
  end

  def self.merge_business_units(sources, destination)
    ActiveRecord::Base.transaction do
      sources.each do |source|
        source.rfq_learners.update_all(:rfq_business_unit_id => destination.id)
        source.rfq_joining_instructions.update_all(:rfq_business_unit_id => destination.id)
        source.acc_booking_people.update_all(:rfq_business_unit_id => destination.id)
        source.destroy
      end
    end
    return true
  rescue ActiveRecord::RecordInvalid
    return false
  end

  private

  def no_learners
    return false if self.rfq_learners.present?
  end

end
