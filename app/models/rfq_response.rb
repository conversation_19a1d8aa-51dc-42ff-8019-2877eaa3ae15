class RfqResponse < ApplicationRecord
  acts_as_paranoid  if (self.table_exists? && self.column_names.include?("deleted_at"))


  before_validation :set_min_max

  # TODO this callback means that selects won't work, so change to before_save (ES)
  # after_initialize :set_comp_grp_index
  before_save :set_comp_grp_index

  attr_accessor :declining, :comp_grp_index, :check_room_loading, :max_rooms_errors

  TRANSPORTS = ["Walking Distance", "Mini Bus", "Taxi", "Coach", "Not Applicable"]
  DBB_MEAL_TYPES = ["Set Menu", "Allowance", "No Restaurant", "N/A"]
  DBB_MEAL_AREAS = ["Bar", "Restaurant", "All", "N/A"]
  COMP_ROOM_GROUPS = [[1, 4], [5, 8], [9, 12]] #NB these are [min, max]

  TOKEN_TIMEOUT_PERIOD = 24.hours

  before_create :generate_token

  has_many :rfq_response_rooms, :dependent => :destroy
  has_many :rfq_response_answers, :dependent => :destroy
  has_many :rfq_client_briefings, :dependent => :destroy
  has_many :rfq_hotel_briefings, :dependent => :destroy
  has_many :rfq_tasks
  has_many :comments, :as => :commentable, :dependent => :destroy
  has_many :rfq_adult_date_restrictions
  has_many :availability_checks
  has_many :rfq_bu_accounts

  has_many :surveys

  belongs_to :rfq_location
  belongs_to :rfq_proposed_hotel
  belongs_to :currency


  audited
  has_associated_audits # for the related data

  accepts_nested_attributes_for :rfq_response_answers
  accepts_nested_attributes_for :rfq_response_rooms

  validates_associated :rfq_response_answers, :unless => :is_declining?
  validates_associated :rfq_response_rooms, :unless => :is_declining?


  validates :nearest_train_station, :distance_from_train_station, :nearest_airport,
            :distance_from_airport, :commission, :minutes_travel, :completed_by_name, :commission_payment_terms,
            :parking_spaces, :completed_by_phone, :wifi_areas, :currency_id, :presence => true, on: :update, :unless => :is_declining?

  validates :single_supplement_amt, :numericality => true, :presence => true, :if => :single_supplement?

  validates :declined_reason, :presence => true, :if => :is_declining?
  validates :declined_reason, length: {maximum: 250}, :if => :is_declining?

  validates :adult_max_doubles, :adult_max_singles, on: :update, :numericality => {:message => "is not a number, please put '0' if none available or if using fixed dates"}, :if => lambda { |x| x.rfq_location.rfq_request.rfq_programme.adult_rfq? && x.check_room_loading }


  validates :wifi_discount_price, :comp_room_qty, :numericality => true, :allow_blank => true, :unless => :is_declining?

  validate :wifi, on: :update, :unless => :is_declining?



  def wifi
    self.errors.add(:wifi_complimentary, "Must choose yes or no") if self.wifi_complimentary.nil?
    return if self.wifi_complimentary? || self.wifi_complimentary.nil?
    self.errors.add(:wifi_discounted, "Must choose yes or no") if self.wifi_discounted.nil?
    self.errors.add(:wifi_discount_price, "Must have a price if wifi is discounted") if self.wifi_discounted? && self.wifi_discount_price.blank?
    self.errors.add(:wifi_discount_details, "Must have details if wifi is discounted") if self.wifi_discounted? && self.wifi_discount_details.blank?
  end


  validate :parking, on: :update, :unless => :is_declining?
  def parking
    self.errors.add(:parking_complimentary, "Must choose yes or no") if self.parking_complimentary.nil?
    return if self.parking_complimentary? || self.parking_complimentary.nil?
    self.errors.add(:parking_charge, "Must have a price if not complimentary") if !self.parking_complimentary? && (self.parking_charge.blank? || self.parking_charge == 0 || self.parking_charge < 0)
  end

  #TODO agree date to introduce this validation:  validate :comp_room_criteria, on: :update, :unless => :is_declining?
  def comp_room_criteria
    return true if !self.rfq_location.rfq_request.comp_trainer_room?
    if self.comp_room_qty > 0
      errors.add(:comp_grp_index, "^Must select a group size if complimentary room(s) available") if (self.comp_grp_index.blank? && self.comp_room_qty > 0)
      errors.add(:comp_room_pkg, "^Must choose one of the packages") if self.comp_room_pkg.blank?
    end
  end


  validates :dbb_meal_areas, :dbb_meal_type, :presence => true, on: :update, :if => lambda { |obj| obj.has_dbb? && !self.is_declining? }

  validate :dbb_fields, on: :update, :if => lambda { |obj| obj.has_dbb? && !self.is_declining? }

  def dbb_fields
    case self.dbb_meal_type
      when "Set Menu"
        self.errors.add(:dbb_meal_courses, "^Must select number of courses for dbb meal") if self.dbb_meal_courses.blank?
      when "Allowance"
        self.errors.add(:dbb_meal_allowance, "^Must complete the allowance available for dbb meal") if self.dbb_meal_allowance.blank?
      when "No Restaurant"
        return true
    end
  end

  validate :no_bookings_denied, on: :update, :if => lambda { |x| x.rfq_location.rfq_request.rfq_programme.adult_rfq? && x.check_room_loading }

  def no_bookings_denied
    start_date = rfq_location.rfq_request.start_date
    end_date = rfq_location.rfq_request.end_date
    #count every day! - nice query with Pauls help
    base = self.rfq_location.acc_bookings.
        where("acc_bookings.cancelled_at is null").
        where("acc_bookings.hotel_id = ?", self.hotel.id).
        select("d, count(*) as b_count, array_agg(acc_bookings.id), array_agg(acc_bookings.first_checkin), array_agg(acc_bookings.last_check_out)").
        joins("inner join (select d::date from generate_series('#{start_date}', '#{end_date}', interval '1 day') as foo(d)) as days(d) on first_checkin <= days.d and last_check_out > days.d").
        group("d").
        order("d asc")
    doubles = base.where("acc_bookings.room_type ilike '%double%'").having("count(*) > ?", adult_max_doubles)
    singles = base.where("acc_bookings.room_type ilike '%single%'").having("count(*) > ?", adult_max_singles)

    #TODO try and check date restrictions as one query instead of many
    if doubles && doubles.any?
      removals = []
      doubles.map do |bk|
        if self.rfq_adult_date_restrictions.where("start_date <= :d and end_date >:d and max_doubles >= :amount ", :d => bk.d, :amount => bk.b_count).any?
          removals << bk
        end
      end
      doubles = doubles - removals
      add_doubles_errors!(doubles) if doubles.any?
    end

    if singles && singles.any?
      removals = []
      singles.map do |bk|
        if self.rfq_adult_date_restrictions.where("start_date <= :d and end_date >:d and max_singles >= :amount ", :d => bk.d, :amount => bk.b_count).any?
          removals << bk
        end
      end
      singles = singles - removals
      add_singles_errors!(singles) if singles.any?
    end
  end

  def add_doubles_errors!(doubles)
    self.max_rooms_errors ||= {}
    errors.add(:adult_max_doubles, "Sorry there are days when the number of double rooms already booked exceeds the proposed limit - see above")
    key = "Double rooms booked exceeds #{adult_max_doubles}:"
    self.max_rooms_errors[key] = []
    doubles.each do |book|
      self.max_rooms_errors[key] << "#{book.d.to_s}: &nbsp;&nbsp#{book.b_count}  ".html_safe
    end
  end

  def add_singles_errors!(singles)
    self.max_rooms_errors ||= {}
    errors.add(:adult_max_singles, "Sorry there are days when the number of single rooms already booked exceeds the proposed limit - see above")
    key = "Singles rooms booked exceeds #{adult_max_singles}:"
    self.max_rooms_errors[key] = []
    singles.each do |book|
      self.max_rooms_errors[key] << "#{book.d.to_s}: &nbsp;&nbsp; #{book.b_count}  ".html_safe
    end
  end

  validate :room_loading_has_incs, :if => lambda { |x| !x.adult_rfq? && x.check_room_loading }

  def room_loading_has_incs
    if (self.rfq_location.rfq_request.hide_manager_room_options == false || self.rfq_location.rfq_request.hide_manager_room_options.nil?) && self.rfq_response_rooms.all?{|rm| rm.inc_manager_selection == false }
        errors.add(:base, "You must allocate at least one room for manager selection")
    elsif self.rfq_response_rooms.all?{|rm| rm.inc_adult_selection == false }
        errors.add(:base, "You must allocate at least one room for adult selection")
    end
  end

  # validates :late_booking_threshold, :presence => true, :if => lambda { |x| x.rfq_location.rfq_request.late_bookings_enabled }
  validate :late_booking_threshold_presence, if: -> {self.rfq_location.rfq_request.late_bookings_enabled}
  def late_booking_threshold_presence
    if self.late_booking_threshold.blank?
      errors.add(:late_booking_threshold, 'Please enter the number of days a late booking is classified by')
    elsif self.late_booking_threshold > 28
      errors.add(:late_booking_threshold, 'Sorry, release days cannot be over 28 days')
    end
  end


  def is_declining?
    self.declining
  end

  scope :proposed, -> { where("proposed_at is not null") }
  scope :not_accepted, -> { where("accepted_at is null") }
  scope :outstanding, -> { joins(:rfq_location => :rfq_request).
      where("rfq_locations.accepted_response_id is null").
      where("hg_provided_at is null and provided_at is null and declined_at is null") }
  scope :in_progress, -> { joins(:rfq_location => :rfq_request).
      where("rfq_locations.released_at is null").where("accepted_at is not null").
      where("rfq_locations.accepted_response_id = rfq_responses.id") }
  scope :live, -> { joins(:rfq_location => :rfq_request).where("rfq_locations.released_at is not null").where("accepted_at is not null") }
  scope :accepted, -> { where("accepted_at is not null") }
  scope :provided, -> { where("provided_at is not null") }
  scope :declined, -> {where("declined_at is not null")}

  #adult scopes
  scope :adult_outstanding, -> { where("accepted_at is null") }
  scope :adult_accepted, -> {where("accepted_at is not null and proposed_at is not null")}
  scope :adult_confirmed, -> {where("accepted_at is not null and rfq_locations.released_at is null ")}
  scope :adult_live, -> {where("accepted_at is not null and rfq_locations.released_at is not null ")}

  def self.current_live
    RfqResponse.joins(:rfq_location => {:rfq_request => :rfq_programme}).
        joins(:rfq_location => {:acc_booking_headers => :acc_bookings}).
        where("rfq_programmes.rfq_type = 'Apprentice'").
        where("acc_bookings.first_checkin >= :sunday and acc_bookings.first_checkin <= :plus_one_week and cancelled_at is null", :sunday => Date.today.end_of_week, :plus_one_week => Date.today.end_of_week + 1.week ).
        where("rfq_responses.accepted_at is not null").
        where("rfq_requests.start_date <= :sunday and rfq_requests.end_date >= :sunday", :sunday => Date.today.end_of_week ).
        where("rfq_requests.lost_at is null").distinct("rfq_responses.id")
  end

  include ActionView::Helpers::NumberHelper

  def released_for_adult?
    self.adult_released_at.present?
  end

  def setup_rooms
    self.rfq_location.rfq_request.rfq_room_options.each do |option|
      RfqRoomOption::PACKAGE_FIELDS.each do |pkg_method|
        pkg = pkg_method.to_s.upcase
        room = option.room_type
        if option.send(pkg_method)
          if (option.inc_trans? && !self.has_room_for?(room, pkg, true))
            self.rfq_response_rooms.create!(:room_type => room, :pkg_type => pkg,
                                            :vat_rate => VAT_RATE, :inc_vat => true, :transport_included => true,
                                            position: self.rfq_location.rfq_request.rfq_room_options.size + 1)
          end
          if (option.ex_trans? && !self.has_room_for?(room, pkg, false))
            self.rfq_response_rooms.create!(:room_type => room, :pkg_type => pkg,
                                            :vat_rate => VAT_RATE, :inc_vat => true, :transport_included => false,
                                            position: self.rfq_location.rfq_request.rfq_room_options.size + 1)
          end
        end
      end
    end
    self.remove_rooms_if_no_option!
  end

  def setup_answers
    rfq = self.rfq_location.rfq_request
    rfq.rfq_questions.each do |q|
      unless self.rfq_response_answers.where(:rfq_question_id => q.id).first
        ans = self.rfq_response_answers.build(:rfq_question_id => q.id)
        ans.save!(:validate => false)
      end
    end
  end


  def room_for(room_type, pkg, transport)
    pkg = pkg.to_s.upcase if (pkg.is_a? Symbol)
    self.rfq_response_rooms.where(:room_type => room_type,
                                  :pkg_type => pkg,
                                  :transport_included => transport).first
  end

  def has_room_for?(room_type, pkg, transport)
    room_for(room_type, pkg, transport).present?
  end

  def has_option_for?(room)
    if room.transport_included?

      self.rfq_location.rfq_request.rfq_room_options.where(:room_type => room.room_type,
                                                           :inc_trans => room.transport_included?
      ).where("rfq_room_options.#{room.pkg_type.downcase} is true").
          first.present?
    else

      self.rfq_location.rfq_request.rfq_room_options.where(:room_type => room.room_type,
                                                           :ex_trans => !room.transport_included?
      ).where("rfq_room_options.#{room.pkg_type.downcase} is true").
          first.present?
    end
  end

  def remove_rooms_if_no_option!
    self.rfq_response_rooms.each do |room|
      room.destroy unless self.has_option_for?(room)
    end
  end

  def train_station_info
    "Nearest train station: #{self.nearest_train_station}, #{self.distance_from_station} away."
  end

  def create_briefings!
    cb = self.rfq_client_briefings.new
    cb.save(:validate => false)
    hb = self.rfq_hotel_briefings.new
    hb.hotel_info = self.rfq_proposed_hotel.hotel.description
    hb.cancellation_policy = "72 hours prior to check in"
    hb.save(:validate => false)
  end

  def auto_complete_hotel_client_sla?
    #so far just for Babcock Training
      self.rfq_location.rfq_request.rfq_programme.client_id == 3254 && self.rfq_location.rfq_request_id < 569
  end

  def clone_hotel_fields
    hotel = self.rfq_proposed_hotel.try(:hotel)
    return unless hotel.present?
    ['nearest_train_station', 'distance_from_train_station', 'nearest_airport', 'distance_from_airport'].each do |meth|
      self.send(meth + '=', hotel.send(meth))
    end
    self.commission = 12.0
    self.save(:validate => false)
  end

  def explain_rooms
    self.rfq_response_rooms.each do |r|
      puts r.explain_txt
    end
  end

  def comp_groups_for_select
    out = []
    comp_fields_array.each_with_index do |grp, idx|
      out << ["#{grp[0]} to #{grp[1]}", idx]
    end
    out
  end

  def comp_room_group_text
    "per group of #{comp_grp_min.present? ? comp_grp_min : 0} to #{comp_grp_max.present? ? comp_grp_max : 0}; "
  end

  def comp_fields_array
    out = [cf_to_a]
    out = [] if out.first == [nil, nil]
    out += COMP_ROOM_GROUPS
    out.uniq.sort
  end

  def cf_to_a
    [self.comp_grp_min, self.comp_grp_max]
  end

  def index_of_min_max
    comp_fields_array.find_index(cf_to_a)
  end

  def ready_for_sla_ji?
    t1 = self.task_for_code("client_briefings")
    t2 = self.task_for_code("hotel_briefings")
    t1.completed? && t2.completed?
  end

  def accepted?
    self.accepted_at.present?
  end

  def not_provided?
    provided_at.blank? && hg_provided_at.blank?
  end

  def provided?
    provided_at.present? || hg_provided_at.present?
  end

  def proposed?
    self.proposed_at.present?
  end

  def ready_for_proposal?
    self.provided? && self.valid?
  end

  def safe_to_rem_ph?
    !self.provided? && !self.declined?
  end

  def ok_for_due?
    self.accepted_at.present? && self.accepted_at > RFQ_DUE_CUTOFF
  end

  def declined?
    self.declined_at.present?
  end

  def undecline!
    self.declined_at = nil
    self.declined_reason = nil
    self.declined_by = nil
    self.save!(:validate => false)
  end

  def declined_info
    return "" unless self.declined?
    "Because: #{self.declined_reason} By: #{self.declined_by}".html_safe
  end

  def completed_info
    return "" unless self.provided?
    "#{self.completed_by_name}: #{self.completed_by_phone}"
  end

  def supplier_status
    case true
      when self.declined_at.present?
        "Declined to quote"
      when self.accepted_at.present?
        "Accepted"
      when self.provided_at.present? &&
          self.rfq_location.accepted_response.present? &&
          self.rfq_location.accepted_response != self
        "Not successful"
      when self.provided_at.present?
        "Provided"
      when self.provided_at.blank?
        "Not yet provided"
    end
  end

  def hotel
    self.rfq_proposed_hotel.try(:hotel)
  end

  def name_and_hotel
    if hotel
      city_or_pcode + ", " + hotel.name
    else
      city_or_pcode
    end
  end

  def city_or_pcode
    self.rfq_location.city_or_pcode
  end

  def has_dbb?
    self.rfq_response_rooms.where(:pkg_type => 'DBB').count > 0
  end

  def dbb_meal_details
    return "not dbb" unless has_dbb?
    case self.dbb_meal_type
      when "Set Menu"
        "Set menu with #{self.dbb_meal_courses} courses in #{self.dbb_meal_areas} area(s)".html_safe
      when "Allowance"
        "Allowance worth #{number_to_currency(self.dbb_meal_allowance, :unit => "")} in #{self.dbb_meal_areas} area(s)".html_safe
      when "No Restaurant"
        "Cannot supply meals"
    end
  end

  def generate_token
    self.token = loop do
      random_token = SecureRandom.hex(6)
      break random_token unless RfqResponse.exists?(:token => random_token)
    end
    self.token_timeout_at = DateTime.now() + TOKEN_TIMEOUT_PERIOD
  end

  def clear_token
    self.update_column(:token, nil)
    self.update_column(:token_timeout_at, nil)
  end


  def all_audits(min_time = nil, max_time = nil, user = nil)
    base = Audited::Audit.unscoped
    base = base.where(:user_id => user) if user
    base = base.where(["created_at >= ?", min_time]) if min_time
    base = base.where(["created_at <= ?", max_time]) if max_time

    base.includes([:auditable, :user]).where(["auditable_id = ? and
                  auditable_type = ? or
                  associated_id = ? and
                  associated_type = ?", self, self.class.name, self, self.class.name])
  end

  def rooms_for_select
    rooms = []
    self.rfq_response_rooms.available.each do |r|
      rooms << [r.name_for_select, r.id]
    end
    rooms
  end

  def self.update_accepted_response_locations
    self.where("accepted_at is not null").each do |resp|
      resp.rfq_location.update_column(:accepted_response_id, resp.id)
    end
  end

  def task_for_code(code)
    self.rfq_tasks.where(:code => code).first
  end

  def sla_tasks
    self.rfq_tasks.sla_tasks
  end

  def adult_rfq?
    self.rfq_location.rfq_request.rfq_programme.adult_rfq?
  end

  def create_tasks!
    if self.adult_rfq?
      RfqTask::ADU_TASKS.each do |k, v|
        next if self.adult_task_not_required(k)
        unless self.rfq_tasks.where(:code => k).first.present?
          task = self.rfq_tasks.create(:name => v[:name], :code => k, :responsibility => v[:resp], :deadline => (Date.today + v[:deadline]), :position => v[:position])
        end
      end
    else
      RfqTask::APP_TASKS.each do |k, v|
        next if self.task_not_required(k)
        unless self.rfq_tasks.where(:code => k).first.present?
          task = self.rfq_tasks.create(:name => v[:name], :code => k, :responsibility => v[:resp], :deadline => (Date.today + v[:deadline]), :position => v[:position])
          if ["sign_in_and_out"].include? task.code
            task.complete!
          end
          if task.code == "hotel_client_sla" && self.auto_complete_hotel_client_sla?
            task.complete!
          end
        end
      end
    end
  end

  def task_not_required(code)
    case code
      when 'credit_app'
        if ["HG to Pay", "Manager/ Learner to Pay Entire"].include? self.rfq_location.rfq_request.payment_method
          true
        else
          false
        end
      when 'stripe_connect'
        if self.rfq_location.rfq_request.card_pay?
          if self.hotel.stripe_connected?
            true
          else
            false
          end
        else
          true
        end
      when 'bu_credit'
        using_stripe_for_payments = rfq_location.rfq_request.card_pay
        payment_to_connected_accounts = rfq_location.rfq_request.use_stripe_connect
        if using_stripe_for_payments && payment_to_connected_accounts
          true
        else
          false
        end
      else
        false
    end
  end

  def adult_task_not_required(code)
    case code
      when 'credit_app'
        if ["HG to Pay", "Manager/ Learner to Pay Entire"].include? self.rfq_location.rfq_request.payment_method
          true
        else
          false
        end
      when 'join_instr'
        if self.rfq_location.rfq_request.no_ji?
          true
        else
          false
        end
      else
        false
    end
  end

  def all_tasks_completed?
    if self.rfq_location.rfq_request.rfq_programme.adult_rfq?
      self.rfq_tasks.critical_adult_tasks.all?(&:completed?)
    else
      self.rfq_tasks.critical_tasks.all?(&:completed?)
    end
  end

  def block_programme_done?
    t = task_for_code('block_programme')
    !t.not_required && t.completed?
  end

  def adult_max_rooms_set?
    adult_max_doubles.present? && adult_max_singles.present?
  end

  def post_acceptance(current_user)
    self.rfq_location.rfq_request.close_task_review_proposal(current_user)
    self.create_tasks!
    self.create_briefings!
  end

  def clear_tasks_for_testing
    raise "Not for production" if Rails.env == "production"
    self.rfq_tasks.destroy_all
    self.rfq_client_briefings.destroy_all
    self.rfq_hotel_briefings.destroy_all

  end

  def programme_and_hotel_name
    "#{self.rfq_location.rfq_request.rfq_programme.name} :- #{self.rfq_proposed_hotel.hotel.name}"
  end

  def adult_price_hash
    prices = {}
    rooms = self.rfq_response_rooms.where(cannot_supply: false).sort_by(&:position).group_by { |x| "#{x.room_type}-#{x.pkg_type}" }
    rooms.keys.each do |k|
      rooms[k].each do |r|
        prices[k] ||= {}
        prices[k][:room_type] = r.room_type
        prices[k][:pkg_type] = r.pkg_type
        prices[k][:rooms_remaining] = r.get_remaining_rooms
        if r.transport_included?
          prices[k][:inc_trans] ||= {}
          prices[k][:inc_trans][:room_id] = r.id
          prices[k][:inc_trans][:price] = r.price_inc_vat
        else
          prices[k][:exc_trans] ||= {}
          prices[k][:exc_trans][:room_id] = r.id
          prices[k][:exc_trans][:price] = r.price_inc_vat
        end
      end
    end
    prices

  end

  def max_rooms_for(room_type, start_date, end_date)
    #NB end_date passed in needs to be -1 if a check_out date
    drs = self.rfq_adult_date_restrictions.
        where("start_date <= ? ", end_date).
        where("end_date >= ? ", start_date)
    raise "No values provided for adult max rooms" if adult_max_doubles.nil? || adult_max_singles.nil?
    case room_type
      when :double
        if drs.count > 0
          drs.minimum(:max_doubles)
        else
          self.adult_max_doubles
        end
      when :single
        if drs.count > 0
          drs.minimum(:max_singles)
        else
          self.adult_max_singles
        end
    end
  end

  def rooms_available_on(d, room_type) # a day for a simple room_type
    max_rooms = self.max_rooms_for(room_type, d, d)
    bookings_count = AccBooking.joins(:rfq_response_room => :rfq_response).
        where("acc_bookings.room_type  ilike ?", "%" + 'double' + "%").
        where("acc_bookings.cancelled_at is null").
        where("rfq_responses.id = ?", self.id).
        where("first_checkin <= ? ", d).
        where("last_check_out > ? ", d).count
    max_rooms - bookings_count
  end

  def available_date_restrictions
    out= []
    self.rfq_adult_date_restrictions.each do |dr|
      base = self.rfq_location.acc_bookings.
          where("acc_bookings.hotel_id = ?", self.hotel.id).
          where("acc_bookings.cancelled_at is null").
          select("d, count(*) as b_count, array_agg(acc_bookings.id), array_agg(acc_bookings.first_checkin), array_agg(acc_bookings.last_check_out)").joins("inner join (select d::date from generate_series('#{dr.start_date}', '#{dr.end_date}', interval '1 day') as foo(d)) as days(d)
        on first_checkin <= days.d and last_check_out > days.d").
          group("d").
          order("d asc")
      doubles = base.where("acc_bookings.room_type ilike '%double%'").having("count(*) >= ?", dr.max_doubles)
      singles = base.where("acc_bookings.room_type ilike '%single%'").having("count(*) >= ?", dr.max_singles)
      if doubles.empty? && singles.empty?
        out << dr
      end
    end
    out
  end

  def commission_text
    if self.commission_inc_vat?
      "#{self.commission}% inc VAT"
    else
      "#{self.commission}% exc VAT"
    end
  end

  def virtual?
    false
  end

  def dropdown_display_details
    "#{prog_name}, #{hotel_name}  ( #{created_date.strftime('%B-%Y')} )  ( RFQ-Request: #{course_id} )"
  end

  def combined_response_id_and_programme_id
    "#{id},#{programme_id}"
  end

  def feedback_task
    rfq_tasks.where(code: 'feedback').first
  end

  def get_academy_rooms_for_select
    rooms = self.adult_price_hash
    rooms.map { |room| {id: room[1][:exc_trans][:room_id], name: "#{room[1][:room_type]} - #{room[1][:pkg_type]} (£#{room[1][:exc_trans][:price]})"}}
  end


  private

  def set_min_max
    if self.comp_grp_index.present?
      min_max = self.comp_fields_array[self.comp_grp_index.to_i]
      self.comp_grp_min = min_max[0]
      self.comp_grp_max = min_max[1]
    else
      self.comp_grp_min = nil
      self.comp_grp_max = nil
    end
  end

  def set_comp_grp_index
    self.comp_grp_index = self.index_of_min_max
  end

  def RfqResponse.generate_slas(resp_id)
    resp = RfqResponse.find resp_id
    RfqResponse.delay.generate_hotel_client_sla(resp_id) unless resp.auto_complete_hotel_client_sla?
    RfqResponse.delay.generate_hotel_hg_sla(resp_id)
    RfqResponse.delay.generate_welcome_letter(resp_id)
  end

  def self.generate_hotel_client_sla(resp_id)
    resp = RfqResponse.find(resp_id)
    hc_sla_task = resp.rfq_tasks.where(:code => "hotel_client_sla").first
    return true unless hc_sla_task.present?
    hc_sla_task.reset!
    hc_sla_task.supersede_existing!
    doc = hc_sla_task.rfq_documents.new
    doc.doc_type = "hotel_client_sla"

    if resp.rfq_location.rfq_request.rfq_programme.adult_rfq?
      if resp.rfq_location.rfq_request.new_adult_booking_flow_toggle?
        AcademyHotelClientSlaPdf.new({:rfq_response => resp, :output_to => "/tmp", :to_file => true}).to_pdf
      else
        AduHotelClientSlaPdf.new({:rfq_response => resp, :output_to => "/tmp", :to_file => true}).to_pdf
      end
    else
      AprHotelClientSlaPdf.new({:rfq_response => resp, :output_to => "/tmp", :to_file => true}).to_pdf
    end

    fname = "h-c-sla#{resp.rfq_location.rfq_request.id}-#{resp.rfq_location.id}.pdf"
    doc.file = File.new("/tmp/#{fname}")
    doc.file.name = fname
    doc.file_content_type = "application/pdf"
    doc.save!
  end

  def self.generate_hotel_hg_sla(resp_id)
    resp = RfqResponse.find(resp_id)
    hc_sla_task = resp.rfq_tasks.where(:code => "hg_hotel_sla").first
    return true unless hc_sla_task.present?
    hc_sla_task.reset!
    hc_sla_task.supersede_existing!
    doc = hc_sla_task.rfq_documents.new
    doc.doc_type = "hg_hotel_sla"
    AprHotelHgSlaPdf.new({:rfq_response => resp, :output_to => "/tmp", :to_file => true}).to_pdf
    fname = "hg-h-sla#{resp.rfq_location.rfq_request.id}-#{resp.rfq_location.id}.pdf"
    doc.file = File.new("/tmp/#{fname}")
    doc.file.name = fname
    doc.file_content_type = "application/pdf"
    doc.save!
  end

  def self.generate_welcome_letter(resp_id)
    resp = RfqResponse.find(resp_id)
    return if resp.adult_rfq? # do not generate one for an adult rfq
    hc_sla_task = resp.rfq_tasks.where(:code => "welcome_letter").first
    return true unless hc_sla_task.present?
    hc_sla_task.reset!
    hc_sla_task.supersede_existing!
    doc = hc_sla_task.rfq_documents.new
    doc.doc_type = "welcome_letter"
    AprWelcomePdf.new({:rfq_response => resp, :output_to => "/tmp", :to_file => true}).to_pdf
    fname = "welcome_#{resp.rfq_location.rfq_request.id}_#{resp.rfq_location.id}.pdf"
    doc.file = File.new("/tmp/#{fname}")
    doc.file.name = fname
    doc.file_content_type = "application/pdf"
    doc.save!
  end

end
