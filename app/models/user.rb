class User < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')
  include CreatorUpdater

  RECOMMENDER_POINTS = 100

  SERVACE_ACCOUNTANTS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

  # Include default devise modules. Others available are:
  # :token_authenticatable, :encryptable, :confirmable, :lockable, :timeoutable and :omniauthable
  devise :database_authenticatable, :timeoutable, :registerable,
         :recoverable, :trackable, :lockable,
         :password_expirable, :password_archivable

  # taken from devise validatable  - overriding uniqueness validator re acts_as_paranoid
  validates_presence_of :email, if: :email_required?
  validates_uniqueness_of :email, allow_blank: true, if: :email_changed?, scope: :deleted_at
  validates_format_of :email, with: Devise.email_regexp, allow_blank: true, if: :email_changed?

  validates_presence_of :password, if: :password_required?
  validates_confirmation_of :password, if: :password_required?
  validates_length_of :password, within: Devise.password_length, allow_blank: true
  # validates_length_of :trainline_password, :trainline_corpref, :maximum => 200, :allow_blank => true
  # validates_presence_of :trainline_password, :trainline_corpref, :if => Proc.new{|x| x.enable_trainline? }

  # validates  :acc_username, :acc_password, :presence =>  {:message => "HG Reservations fields are required for Accommodation Stop or Apprentice Stop", :if => lambda{|u| ['MANAGER', 'BOOKER'].include?(u.role) && (u.acc_enabled? || u.app_enabled?) }}
  # validates  :con_enabled, :acceptance => { :message => " Must be able to access Conference Stop if advanced user", :if => lambda{|u| ['MANAGER', 'BOOKER'].include?(u.role) && u.advanced_user == '1' }}
  #----------------------------------------------------------------------------------------------

  before_validation :set_default_info, on: :create # used if created by form
  before_save :set_loyalty_scheme
  before_validation :set_terms_and_conditions_accepted
  after_validation :sync_email
  after_create :send_hg_mail
  before_destroy :check_not_important

  attr_accessor :terms_and_conditions, :organisation_name, :loyalty_scheme, :time_token, :human_check, :inviting,
                :mkt_opt_in

  # Setup accessible (or protected) attributes for your model
  # attr_accessible :name, :email, :password, :password_confirmation, :remember_me, :region_ids,
  #  :contact_attributes, :contact, :hotel_name, :terms_and_conditions, :organisation_name, :loyalty_scheme,
  #  :offer_type_ids, :hotel_role, :time_token, :terms_and_conditions_accepted, :advanced_events, :free_events

  # attr_accessible :name, :email, :password, :password_confirmation, :remember_me, :organisation_name, :region_ids,
  # :contact_attributes, :contact, :hotel_name, :terms_and_conditions, :offer_type_ids,
  #  :role, :loyalty_scheme, :hotel_role, :advanced_user, :disabled, :acc_username, :acc_password, :acc_enabled,
  # :app_enabled, :con_enabled, :terms_and_conditions_accepted, :conferma_username, :advanced_events, :free_events, :as => :admin

  if table_exists?
    audited require_comment: true,
            associated_with: :organisation,
            only: %i[email name role contact_id]
  end

  belongs_to :contact
  accepts_nested_attributes_for :contact

  # Feedback links
  has_many :questionnaires
  has_many :answers
  has_many :questions, through: :answers

  has_many :tasks, foreign_key: :assigned_to_id
  has_many :loyalty_changes
  has_many :earned_loyalties
  has_many :redeemed_loyalties
  has_many :recommendations
  has_many :entrants
  has_many :competitions, through: :entrants
  has_many :acc_booking_chases, foreign_key: :assigned_to_id
  has_many :notes
  has_many :joining_instructions
  has_one  :stripe_detail
  has_many :travel_forms
  has_many :fees

  has_and_belongs_to_many :offer_types
  has_and_belongs_to_many :regions
  has_and_belongs_to_many :clusters

  has_many :short_list_hotels
  has_many :shortlisted_hotels, through: :short_list_hotels, source: :hotel

  has_one :invitation
  has_one :release_stamp

  validates :role, presence: true
  validates :organisation_name, presence: true, if: proc { |u| u.role == 'MANAGER' && !u.persisted? }
  validates :terms_and_conditions, acceptance: true, if: :not_asked_yet
  validates :terms_and_conditions, inclusion: { in: [true, '1'], message: 'Sorry, you must accept the terms and conditions to use this website' }, if: proc { |x|
                                                                                                                                                         x.inviting
                                                                                                                                                       }
  validates :time_token, is_human: { secs: 30 }, if: proc { |x| !x.persisted? && x.human_check.present? }
  validate :location_email, if: ->(u) { u.role == 'MANAGER' && u.contact.try(:primary_location).present? }

  def self.ransackable_attributes(auth_object = nil)
    %w[acc_enabled acc_password acc_username advanced_events advanced_user app_enabled
       authentication_token babcock_unlimited backdrop_creating con_enabled conf_dashboard_enabled conferma_username contact_id created_at creator_id current_sign_in_at current_sign_in_ip deleted_at disabled email enable_trainline encrypted_password failed_attempts for_proposed_hotel free_events hotel_role id id_value joined_loyalty_scheme last_sign_in_at last_sign_in_ip locked_at masquerader_id mkt_last_opted_in_at mkt_last_opted_out_at mkt_opted_in_at name org_data_cleaner_access password_changed_at remember_created_at reset_password_sent_at reset_password_token role sign_in_count terms_and_conditions_accepted trainline_corpref trainline_password unlock_token updated_at updater_id]
  end

  def self.ransackable_associations(auth_object = nil)
    %w[acc_booking_chases answers audits clusters competitions contact creator earned_loyalties
       entrants fees invitation joining_instructions loyalty_changes notes offer_types old_passwords questionnaires questions recommendations redeemed_loyalties regions release_stamp short_list_hotels shortlisted_hotels stripe_detail tasks travel_forms updater]
  end

  def location_email
    contact.primary_location.errors.add(:email, "can't be blank") if contact.primary_location.email.blank?
  end

  validate :location_fax, if: ->(u) { u.role != 'MANAGER' && u.contact.try(:primary_location).present? }

  def location_fax
    return unless contact.primary_location.fax_phone.blank?

    contact.primary_location.errors.add(:fax_phone,
                                        "can't be blank")
  end

  def not_asked_yet
    return true if new_record?

    for_proposed_hotel && terms_and_conditions_accepted.nil?
  end

  validates_presence_of :hotel_role, on: :create, if: proc { |u| u.role == 'HOTEL' }

  validate :check_password_strength

  validate :appropriate_admin

  def appropriate_admin
    return unless %w[ADMIN ADMIN_MANAGER].include?(role)

    errors.add(:role, 'Cannot be admin unless HG') unless
                Organisation.allowed_admin_ids.include?(contact.parent_id)
  end

  validate :conferma_ready
  def conferma_ready
    errors.add(:conferma_username, 'must be present for accommodation_stop') if
      acc_enabled? && conferma_username.blank?
  end

  delegate :full_name_or_email, to: :contact
  delegate :full_name_and_parent, to: :contact

  #########################################
  # Roles
  ROLES = %w[ADMIN ADMIN_MANAGER BOOKER TEAM MANAGER EXECUTIVE CHAIN CLUSTER HOTEL]
  USER_ROLES = ROLES - %w[ADMIN]
  DEFAULT_ROLE = 'BOOKER'

  def organisation
    contact.parent if contact.present?
  end

  def is_an_administrator?
    %w[ADMIN ADMIN_MANAGER].include? role
  end

  def is_a_manager?
    role == 'MANAGER'
  end

  def is_an_executive?
    role == 'EXECUTIVE'
  end

  def self.admin
    where(role: %w[ADMIN ADMIN_MANAGER])
  end

  def is_a_client?
    %w[BOOKER MANAGER TEAM EXECUTIVE].include? role
  end

  def is_advanced_client?
    is_a_client? # && advanced_user?
  end

  def is_a_supplier?
    %w[CHAIN CLUSTER HOTEL].include? role
  end

  def is_a_chain_user?
    role == 'CHAIN'
  end

  def is_a_cluster_user?
    role == 'CLUSTER'
  end

  def is_a_hotel_user?
    role == 'HOTEL'
  end

  def is_a_nok_viewer?
    is_a_client? || role == 'ADMIN_MANAGER'
  end

  def is_an_admin_manager?
    role == 'ADMIN' # _MANAGER'
  end

  def email_and_org_id
    return unless contact.present? && organisation.present?

    email + ' ' + organisation.name + ' ' + organisation.id.to_s
  end

  def is_self_managed?
    is_a_client? && organisation.self_managed?
  end

  def user_type
    case true
    when is_an_administrator?
      'Admin'
    when is_a_client?
      'Client'
    when is_a_supplier?
      'Supplier'
    else
      'Unknown'
    end
  end

  def stripe_connector?
    role == 'HOTEL' &&
      contact.parent.stripe_contact == contact
  end

  def roles_for_admin_update
    case contact.parent
    when Organisation.HG
      %w[ADMIN ADMIN_MANAGER]
    when Organisation
      if contact.parent.legal_entities_enabled? || contact.parent.has_departments?
        %w[EXECUTIVE MANAGER TEAM BOOKER]
      else
        %w[MANAGER BOOKER TEAM]
      end
    when Hotel, Chain
      %w[HOTEL CHAIN]
    when nil
      %w[MANAGER BOOKER HOTEL CHAIN]
    else
      []
    end
  end

  def disable!
    update_column(:disabled, true)
  end

  def enable!
    update_column(:disabled, false)
  end

  def dj_queue
    "user_#{id}"
  end

  def can_cs_shortlist?
    is_advanced_client?
  end

  def is_primary_contact?
    contact == organisation.primary_contact
  end

  # for organisations with no departments or suppliers
  def conferences_available(other = nil)
    case role
    when 'MANAGER'
      out = organisation.conferences
      if other.present?
        out = out.joins(:opportunity)
                 .where('opportunities.main_contact_id = ?', other)
      end
      out
    when 'BOOKER'
      out = organisation.conferences
      out.joins(:opportunity)
         .where('opportunities.main_contact_id = ?', contact.id)
    when 'HOTEL', 'CHAIN'
      contact.parent.quoted_conferences
    when 'CLUSTER'
      Conference.joins(quotations: { proposed_hotel: { hotel: :cluster } })
                .where('clusters.id in (?)', clusters.map(&:id))
    else
      raise 'Unexpected role when calling conferences_available'
    end
  end

  def responses_available
    return [] unless is_a_supplier?

    RfqResponse.joins(rfq_proposed_hotel: :hotel, rfq_location: { rfq_request: :rfq_programme }).
      #  where("rfq_programmes.client_id <> ?", 3229).# hack to exclude this org from supplier views of rfqs
      where('hotels.id in (?)', all_hotels.map(&:id)).where('rfq_requests.lost_at is null').order('rfq_requests.start_date DESC')
  end

  def apprentice_responses_available(year = nil)
    return [] unless is_a_supplier?

    out = RfqResponse.joins(rfq_proposed_hotel: :hotel, rfq_location: { rfq_request: :rfq_programme })
                     .where('rfq_programmes.rfq_type = ?', 'Apprentice').
          # where("rfq_programmes.client_id <> ?", 3229).# hack to exclude this org from supplier views of rfqs
          where('hotels.id in (?)', all_hotels.map(&:id)).where('rfq_requests.lost_at is null').order('rfq_requests.start_date DESC')
    if year
      out = out.where(
        'extract(year from rfq_requests.start_date) <= :yr and extract(year from rfq_requests.end_date) >= :yr ', yr: year
      )
    end
    out
  end

  def adult_responses_available
    return [] unless is_a_supplier?

    RfqResponse.joins(rfq_proposed_hotel: :hotel, rfq_location: { rfq_request: :rfq_programme })
               .where('rfq_programmes.rfq_type = ?', 'Adult')
               .where('hotels.id in (?)', all_hotels.map(&:id)).where('rfq_requests.lost_at is null').order('rfq_requests.start_date DESC')
  end

  def client_own_conferences
    raise 'only for clients!!' unless is_a_client?

    organisation.conferences.joins(opportunity: :main_contact).where('contacts.id = ?', contact.id)
  end

  def legal_entity_ids
    case role
    when 'BOOKER', 'MANAGER', 'TEAM'
      [contact.legal_entity_id]
    when 'EXECUTIVE'
      contact.parent.legal_entities.pluck(:id)
    end
  end

  def contacts_for_dash(department = nil)
    return nil unless is_a_client?
    return [] if role == 'BOOKER' && !organisation.has_departments?

    out = contact.parent.contacts.order(:surname)
    teams = contact.teams
    if role == 'MANAGER' || teams.any?
      out = out.joins(:teams).where('teams.id in(?)', teams.map(&:id)) if teams.present?
    else
      out = []
    end
    [['All', nil]] + out.map { |c| [c.full_name, c.id] }
  end

  def opportunities_available
    case role
    when 'MANAGER'
      organisation.opportunities
    when 'BOOKER'
      organisation.opportunities.joins(:main_contact).where(['contacts.job_department = ?',
                                                             contact.job_department])
    else
      raise 'Unexpected role when calling opportunities_available'
    end
  end

  def self.hg_users
    joins(:contact).joins("inner join organisations on organisations.id = contacts.parent_id and contacts.parent_type = 'Organisation'")
                   .where(organisations: { name: 'ServAce' }, contacts: { left_at: nil }, role: 'ADMIN')
                   .order('contacts.surname asc')
  end

  def self.servace_users
    hg_users
  end

  def self.servace_users
    hg_users
  end

  def self.hg_app_users
    hg_users.where(email: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
                           '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'])
  end

  def self.with_loyalty
    joins(:earned_loyalties).includes(:redeemed_loyalties)
  end

  def self.hg_for_tasks_graph
    out = {}
    User.hg_users.each do |u|
      out.store(u.tasks_count_array[0], u.tasks_count_array[1])
    end
    out
  end

  def stops
    out = []
    out << 'Conference Stop' if con_enabled?
    out << 'Apprentice Stop' if app_enabled?
    out << 'Accommodation Stop' if con_enabled?
    out
  end

  def tasks_count_array
    [contact.initials, contact.assigned_tasks.incomplete.current.count]
  end

  ##########################################
  # build a user from a contact

  def self.from_contact!(contact, defrole = DEFAULT_ROLE, initial_pw = nil, for_prop_hotel = false)
    role = if contact.parent.is_a?(Hotel)
             'HOTEL'
           elsif contact.parent.is_a?(Chain)
             'CHAIN'
           elsif contact.parent == Organisation.HG
             'ADMIN'
           else
             defrole == 'MANAGER' ? 'MANAGER' : 'BOOKER'
           end
    raise "Can't assign non-client role" if !USER_ROLES.include?(role) && !contact.parent == Organisation.HG

    initial_pw ||= Digest::SHA1.hexdigest(DateTime.now.to_s * rand(13)).gsub(/[ab]/,
                                                                             %w[A B C D].sample) + %w[! @ $].sample

    u = User.new name: contact.full_name,
                 email: contact.email,
                 password: initial_pw,
                 password_confirmation: initial_pw
    u.password_changed_at = (User.expire_password_after + 1.year).ago
    u.contact = contact
    u.role = role
    u.for_proposed_hotel = for_prop_hotel
    u.con_enabled = true
    u.hotel_role = 'Promoted'
    u.advanced_user = true
    u.save
    u
  end

  def send_csv_upload_confirmation(count)
    return unless contact.parent.is_a?(Chain)

    chain = contact.parent
    UserMailer.csv_upload_confirmation(self, chain, count).deliver
  end

  # Called on registration. If it's a clients (and valid) then it also needs to create the org
  def saves_from_signup(from_eventstop = false)
    is_a_client? && valid? ? save_with_new_organisaton(from_eventstop) : save
  end

  # if user is in loyalty scheme need to get their current points
  def current_loyalty_points
    all_awarded_points - all_redeemed_points
  end

  def all_awarded_points
    earned_loyalties.sum(:points_awarded)
  end

  def all_redeemed_points
    redeemed_loyalties.where(denied: false).sum(:points_redeemed)
  end

  # override of devise method to allow for longer password reset for new users promoted from contact
  def reset_password_period_valid?
    return true unless respond_to?(:reset_password_sent_at)

    if sign_in_count == 0
      reset_password_sent_at && reset_password_sent_at.utc >= self.class.reset_password_within.ago - 1.day
    else
      reset_password_sent_at && reset_password_sent_at.utc >= self.class.reset_password_within.ago
    end
  end

  # from devise validatable - overriding re uniqueness validator
  def password_required?
    false # !persisted? || !password.nil? || !password_confirmation.nil?
  end

  def email_required?
    true
  end

  def teams_for_dash
    return [] if contact.teams.blank? || !contact.parent.has_departments?

    teams = if %w[MANAGER EXECUTIVE].include? role
              contact.parent.teams
            else
              contact.teams
            end
    teams.order('name ASC').map { |t| [t.name, t.id] }
  end

  def has_main_role?
    conts = []
    hids = all_hotels.map(&:id)
    hconts = Hotel.where(id: hids).pluck(:conference_contact_id, :reservations_contact_id, :sales_manager_contact_id,
                                         :accounts_contact_id)
    carray = hconts.flatten.reject(&:nil?)
    carray.include?(contact_id)
  end

  def all_hotels
    case role
    when 'ADMIN', 'ADMIN_MANAGER'
      Hotel.order(:name).to_a
    when 'CHAIN'
      if contact.parent.present?
        contact.parent.hotels.order(:name).to_a
      else
        []
      end
    when 'CLUSTER'
      clusters.inject([]) { |h, c| h + c.hotels }.sort_by { |h| h.name }
    when 'HOTEL'
      if contact.parent.present?
        [contact.parent]
      else
        []
      end
    else
      raise "user does not have a supplier role can't have hotels, role is #{role}"
    end
  end

  def all_quotations
    case role
    when 'CHAIN', 'HOTEL'
      contact.parent.quotations.includes(:conference, :hotel)
    when 'CLUSTER'
      Quotation.joins(proposed_hotel: { hotel: :cluster })
               .where('clusters.id in (?)', clusters.map(&:id))
               .includes(:conference)
    else
      raise "user does not have a supplier role can't have hotels, role is #{role}"
    end
  end

  def stripe_supplier?
    return false unless role == 'HOTEL'

    self&.contact&.parent&.stripe_contact.present?
  end

  def outstanding_documents
    # TODO: first attempt - needs completing when clearer
    return unless is_a_client?

    RfqDocument.joins("join rfq_locations on rfq_documents.parent_id = rfq_locations.id and rfq_documents.parent_type = 'RfqLocation'")
               .joins('join rfq_requests on rfq_locations.rfq_request_id = rfq_requests.id ')
               .where('rfq_requests.contact_id = ? ', contact.id)
               .where('rfq_documents.hg_verified_at is not null')
  end

  def has_events?
    # TODO: -fix all_hotels and then this will not need an if here
    # return false unless %w[CHAIN HOTEL CLUSTER].include? role

    # possible_hotel_ids = if all_hotels == [nil]
    #                        []
    #                      else
    #                        all_hotels.collect { |x| x.id }
    #                      end
    # Event.joins(:event_hotels).where('event_hotels.hotel_id in (?)', possible_hotel_ids).any?
    false
  end

  def entered?(comp)
    competitions.include?(comp)
  end

  def has_active_package?
    contact.present? && contact.active_package.present?
  end

  def supplier_locations_for_select
    RfqLocation.joins(accepted_response: { rfq_proposed_hotel: :hotel }, rfq_request: { rfq_programme: :client })
               .where("rfq_programmes.rfq_type = 'Apprentice'")
               .where("date_part('year', rfq_requests.end_date) >= :year and date_part('year', rfq_requests.start_date) <= :year", year: Date.today.year)
               .where('hotels.id in(?)', all_hotels.map(&:id)).map do |loc|
      ["#{loc.rfq_request.id} - #{loc.rfq_request.name}",
       loc.id]
    end
  end

  def access
    out = []
    out << 'Apprentice' if app_enabled?
    out << 'Conference' if con_enabled?
    out << 'no special' if !app_enabled? && !con_enabled?
    out = out.to_sentence + ' access'
    out.html_safe
  end

  def self.export(bid)
    bdrop = Backdrop.find bid
    @users = User.includes(contact: :primary_location).ransack bdrop.query
    csv_str = CSV.generate do |csv|
      csv << ['Org ID', 'Org Name', 'Managed By (if client)', 'UserLogin', 'Login Disabled?', 'Contact ID', 'Left?',
              'Next Company', 'Title', 'First Name', 'Middle Name', 'Surname', 'Salutation', 'Email', 'Telephone', 'Mobile', 'Birthday', 'Opted in for marketing?', 'Job Title', 'Department', 'Notes', 'Created', 'Address1', 'Address2', 'Address3', 'City', 'County', 'Country', 'Postcode']
      @users.result.find_in_batches(batch_size: 500) do |batch|
        puts "starting batch: #{batch.first.id}"
        batch.each do |u|
          c = u.contact
          csv << [
            begin
              u.contact.try(:parent).try(:id).to_s
            rescue StandardError
              ''
            end,
            begin
              u.contact.try(:parent).try(:name).to_s
            rescue StandardError
              ''
            end,
            begin
              u.contact.parent.respond_to?(:manager) ? u.contact.parent.manager.try(:full_name) : ''
            rescue StandardError
              ''
            end,
            begin
              u.try(:email).to_s
            rescue StandardError
              ''
            end,
            (u.disabled? ? 'disabled' : ''),
            begin
              c.id if c.present?
            rescue StandardError
              ''
            end,
            begin
              c.left_at.strftime('%d-%b-%y. %H:%M') if c.left_at.present?
            rescue StandardError
              ''
            end,
            begin
              c.new_company.to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:title).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:first_name).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:middle_name).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:surname).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:salutation).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:email).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:telephone).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:mobile_phone).to_s
            rescue StandardError
              ''
            end,
            (c.birthday.strftime('%d-%b-%y') if c.try(:birthday).present?),
            c.opt_in_status,
            begin
              c.try(:job_title).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:job_department).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:notes).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:created_at)
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:address_1).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:address_2).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:address_3).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:city).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:county).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:country).try(:name).to_s
            rescue StandardError
              ''
            end,
            begin
              c.try(:primary_location).try(:postcode).to_s
            rescue StandardError
              ''
            end
          ]
        end
      end
    end
    bdrop.save_csv(bdrop.csv_name, csv_str)
  end

  def is_self_managed?
    is_a_client? && organisation.self_managed?
  end

  def is_a_servace_accountant?
    SERVACE_ACCOUNTANTS.include?(email)
  end

  def can_access_booking?(id)
    return true if is_an_administrator?

    allowed_programmes = RfqProgramme.allowed_programmes_for(contact)
    AccBooking.joins(acc_booking_header: { rfq_location: { rfq_request: :rfq_programme } })
              .where('rfq_programmes.id in (?) AND acc_bookings.id = ?', allowed_programmes, id.to_i)
              .count > 0
  end

  def can_access_booking?(id)
    return true if is_an_administrator?

    allowed_programmes = RfqProgramme.allowed_programmes_for(contact)
    AccBooking.joins(acc_booking_header: { rfq_location: { rfq_request: :rfq_programme } })
              .where('rfq_programmes.id in (?) AND acc_bookings.id = ?', allowed_programmes, id.to_i)
              .count > 0
  end

  #-----------------------

  private

  def check_password_strength
    return unless password_required?

    errors.add(:password, 'must be at least 8 characters long') if password.to_s.size < 8
    errors.add(:password, 'must contain at least one number') unless password =~ /[0-9]/
    errors.add(:password, 'must contain at least one uppercase character') unless password =~ /[A-Z]/
    errors.add(:password, 'must contain at least one lowercase character') unless password =~ /[a-z]/
    return if password =~ /(?=.*[!£$%^&*@~#<>?+=_-])/

    errors.add(:password,
               "must contain at least one symbol '!£$%^&*@~#<>?+=_-")
  end

  def set_default_info
    self.name = contact.full_name if name.blank? && contact.present?
    contact.email = email if contact.present? && contact.email.blank?
  end

  def save_with_new_organisaton(from_eventstop = false)
    # Called only from eventstop AFAK
    x = 1
    o = Organisation.new
    o.apprentice_contract_level = 'SELF' if from_eventstop
    o.name = organisation_name
    o.primary_contact = contact
    o.primary_location = contact.primary_location
    hg = Organisation.HG
    contacts = Organisation.HG.contacts
    o.manager = Organisation.HG.contacts.where(email: '<EMAIL>').first # TODO: Who should be the default manager!?

    # Stops validations from running on the org if not appropriate for a new org not created by admin.
    o.new_registration = true

    if o.valid?
      transaction do
        o.save!
        contact.parent = o
        save!
      end
      true
    else
      # Move the validation errors for the org name to this user.
      o.errors.each do |field, msg|
        # raise "Unexpected validation error! The above code should build a valid org" unless field == :name
        if field == :name
          errors.add(:organisation_name, msg)
        else
          errors.add(:base, "Organisation #{field} : #{msg}")
        end
      end
      false
    end
  end

  def set_loyalty_scheme
    return unless is_a_client?

    if loyalty_scheme == '1' && joined_loyalty_scheme.blank?
      self.joined_loyalty_scheme = DateTime.now
      loyalty_changes.build(member: true, as_at: DateTime.now)
    elsif loyalty_scheme == '0' && joined_loyalty_scheme.present?
      self.joined_loyalty_scheme = nil
      loyalty_changes.build(member: false, as_at: DateTime.now)
    end
  end

  def set_terms_and_conditions_accepted
    return unless terms_and_conditions == '1' && terms_and_conditions_accepted.blank?

    self.terms_and_conditions_accepted = Time.zone.now
  end

  def self.managers_for_select
    where("role in ('ADMIN', 'ADMIN_MANAGER) ").includes(:contact).map do |user|
      [user.contact.initials, user.contact.id]
    end.sort
  end

  def sync_email
    return unless contact.present? && email_changed? && persisted?

    contact.update_attribute(:email, email)
  end

  def send_hg_mail
    return if inviting

    HgMailer.delay.new_user(id)
  end

  def check_not_important
    case true
    when is_a_supplier?
      if has_main_role?
        errors.add(:base, "is a hotel main contact for #{self&.contact&.parent&.name}")
        throw :abort
      end
    else
    end
  end
end
