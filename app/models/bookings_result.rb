class BookingsResult < ApplicationRecord
  has_many :booking_payments, foreign_key: :paymentable_id, primary_key: :id
  belongs_to :acc_booking_person, foreign_key: :acc_booking_person_id, primary_key: :id

  def self.refresh
    Scenic.database.refresh_materialized_view(table_name, concurrently: true, cascade: false)
  end

  def hotel_name_or_subsistence
    if self.subsistence_only?
      "Subsistence Only"
    else
      self.hotel_name
    end
  end

  def self.trainer
    joins(:acc_booking_person).where("acc_booking_people.person_type = 'TRA' ")
  end

  def is_stripe_connect
    booking.is_stripe_connect
  end

  def should_charge_subsistence?
    booking.should_charge_subsistence?
  end

  private
  
  def booking
    @booking ||= AccBooking.where(id: self.id, deleted_at: nil).first
  end
end
