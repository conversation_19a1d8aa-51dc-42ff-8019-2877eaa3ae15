class EventDraftImage < OldEventImage

  belongs_to :draft_for, :class_name => "OldEventImage", :foreign_key => :draft_id




  def resize
    size = ""
    case self.draft_for.try(:type)
      when 'EventLogo'
        size = '235x235#sw'
      when 'EventHeaderImage'
        size = '940x180#sw'
      when 'EventVenueWideImg'
        size = '450x235#sw'
      else
        raise "unknown draft for type #{self.draft_for.try(:type)}"
    end
    self.resized_image = self.image.thumb(size)
    self.resized_image.name = self.image.name
  end




end