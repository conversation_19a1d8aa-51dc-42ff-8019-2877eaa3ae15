class AccBooking<PERSON>eader < ApplicationRecord
  acts_as_paranoid if table_exists? && column_names.include?('deleted_at')

  include ClientOwnable

  attr_accessor :validate_min, :validate_ji_presence, :validate_block_dates_presence, :save_as_group,
                :new_group_code, :epa_training, :epa_subsistence_only,
                :skip_learner_booking_overlap_validation, :validate_jis_enabled, :do_not_send_save_the_date,
                :epa_joining_instruction_id, :virtual_joining_instruction_id

  audited
  has_associated_audits

  has_many :acc_booking_blocks, dependent: :destroy
  has_many :acc_booking_people, dependent: :destroy
  has_many :acc_bookings_learners, -> { where('person_type in (?)', %w[PRO LEA]) }, class_name: 'AccBooking<PERSON>erson'
  has_many :acc_bookings_learners_only, -> { where('person_type = ?', 'LEA') }, class_name: 'AccBookingPerson'
  has_many :acc_bookings_prov_only, -> { where('person_type = ?', 'PRO') }, class_name: 'AccBooking<PERSON>erson'
  has_many :acc_bookings_staff, -> { where('person_type = ?', 'STA') }, class_name: 'AccBookingPerson'
  has_many :acc_bookings, dependent: :destroy
  has_many :acc_selected_modules, dependent: :destroy

  has_many :tags, as: :taggable

  belongs_to :rfq_location
  belongs_to :virtual_time_slot
  belongs_to :virtual_module
  belongs_to :conference
  belongs_to :rfq_training_centre
  belongs_to :booker, foreign_key: :booker_id, class_name: 'Contact'

  belongs_to :selected_trainer, foreign_key: :rfq_trainer_id, class_name: 'RfqTrainer'

  accepts_nested_attributes_for :acc_booking_blocks
  accepts_nested_attributes_for :acc_bookings_learners
  accepts_nested_attributes_for :acc_bookings_staff
  accepts_nested_attributes_for :acc_selected_modules
  accepts_nested_attributes_for :tags, allow_destroy: true

  before_create :generate_token

  validates :rfq_training_centre_id, presence: true, if: proc { |x|
                                                           !x.non_block_mode && x.rfq_location && x.rfq_location.rfq_training_centres.any?
                                                         }

  validate :must_have_blocks, unless: lambda { |x|
                                        x.validate_min || x.virtual_time_slot.present? || x.virtual_module.present? || x.multi_modules_selected?
                                      }
  # validate :blocks_must_have_dates
  validate :dates_cannot_be_in_the_past
  validate :validate_ji_ownership, on: :update

  def validate_ji_ownership
    return unless validate_jis_enabled

    joining_instructions = JoiningInstruction.where(rfq_task_id: rfq_location.accepted_response.rfq_tasks.where(code: 'join_instr').first.id)
    return if joining_instructions.map(&:id).include? joining_instruction_id

    errors.add(:base, 'That JI does not belong to you')
  end

  # Gets only new JIs
  def joining_instruction
    JoiningInstruction.where(id: joining_instruction_id).first
  end

  def must_have_blocks
    return if non_block_mode
    return if acc_booking_blocks.any?

    errors.add(:base, 'You must have at least one block')
  end

  validate :must_have_people, unless: :validate_min
  def must_have_people
    return if acc_bookings_learners.any? || acc_bookings_staff.any? || acc_booking_people.any?

    errors.add(:base, 'You must have at least one person')
  end

  validate :joining_instruction_id_present
  def joining_instruction_id_present
    return unless validate_ji_presence && joining_instruction_id.blank?

    errors.add(:joining_instruction_id, 'must be selected')
  end

  validate :epa_bookings_correct_accommodation_type, if: :epa_booking?

  def epa_booking?
    save_as_epa_bookings
  end

  # For an EPA booking a booking can be subsistence only for all learners, or accommodation only, NOT both
  def epa_bookings_correct_accommodation_type
    error_message = 'All learners must be subsistence only'
    all_match = true
    if epa_subsistence_only == '1'
      all_match = acc_bookings_learners.all? { |bl| bl.subsistence_only == true }
    else
      error_message = 'All learners must NOT be subsistence only'
      all_match = acc_bookings_learners.all? { |bl| bl.subsistence_only == false }
    end
    errors.add(:base, error_message) unless all_match
  end

  validate :must_have_block_programme_cover, if: :validate_min

  def must_have_block_programme_cover
    return true if rfq_location.rfq_request.virtual?

    dates = rfq_location.get_bp_dates
    errors.add(:base, 'No Block Programme found for this location') and return false if dates.blank?

    acc_booking_blocks.each do |bl|
      errors.add(:base, "Not within block programme dates: #{rfq_location.bp_dates_pretty}") if dates.select do |d|
                                                                                                  d[0] <= bl.start_date
                                                                                                end.blank? || dates.select do |d|
                                                                                                                d[1] >= bl.end_date
                                                                                                              end.blank?
    end
  end

  validate :check_virtual_info, if: :validate_min

  def check_virtual_info
    return unless rfq_location.rfq_request.virtual? && !multi_modules_selected?

    if !rfq_location.rfq_request.enable_flexi_slots? && virtual_time_slot_id.blank?
      errors.add(:virtual_time_slot_id,
                 'Please select a timeslot')
    end
    return unless rfq_location.rfq_request.enable_flexi_slots?

    unless flexi_module_start.to_date <= rfq_location.rfq_request.end_date && flexi_module_end.to_date >= rfq_location.rfq_request.start_date
      errors.add(:flexi_module_start,
                 "Dates must be between #{rfq_location.rfq_request.start_date} and #{rfq_location.rfq_request.end_date}")
    end
  end

  validates :virtual_module_id, :flexi_module_start, :flexi_module_end, presence: true, if: proc { |x|
                                                                                              x.rfq_location.present? && x.rfq_location.rfq_request.virtual? && x.rfq_location.rfq_request.enable_flexi_slots? && !x.multi_modules_selected?
                                                                                            }

  def save_and_setup_bookings!
    self.payment_method = rfq_location.rfq_request.payment_method
    hotel_to_assign = rfq_location.accepted_response.hotel

    booking_header_ji = nil

    acc_booking_blocks.each do |b|
      [acc_bookings_learners, acc_bookings_staff].each do |list|
        list.each do |person|
          person.acc_booking_header = self
          person.update(subsistence_only: true) if epa_subsistence_only == '1'
          person.save!

          book = acc_bookings.new
          book.epa_booking = save_as_epa_bookings
          book.acc_booking_person = person
          book.acc_booking_block = b

          if person.subsistence_only?
            book.subsistence_only = true
          else
            book.rfq_response_room_id = person.rfq_response_room_id
            book.hotel = hotel_to_assign
            book.room_type = person.rfq_response_room.room_type
            book.pkg_type = person.rfq_response_room.pkg_type
            book.transport_included = person.rfq_response_room.transport_included
            book.pppn_inc_vat = person.rfq_response_room.price_inc_vat
          end
          if person.person_type == 'STA'
            book.confirmed_at = Time.zone.now
            book.confirmed_by = 'System (Staff Booking)'
          end
          if rfq_location.rfq_request.no_ji?
            book.confirmed_at = Time.zone.now
            book.confirmed_by = 'Auto Confirmed'
          end

          book.payment_method = person.payment_method

          book.joining_instruction_id = joining_instruction_id
          if book.acc_booking_header.rfq_location.rfq_request.use_stripe_connect == false
            begin
              fees = book.acc_booking_header.rfq_location.rfq_request.get_fees_for_rfq
              if fees.nil? || fees.id.nil?
                raise "Fees are not setup on this Request"
              end
              book.fee_id = fees.id
            rescue => e
              errors.add(:base, "Error assigning fee: #{e.message}")
            end
          end

          if rfq_location.rfq_request.late_bookings_enabled
            book.late_booking_threshold = rfq_location.accepted_response.late_booking_threshold
          end

          booking_dates = b.booking_date_attributes
          booking_dates.each do |week|
            booking_week = book.booking_dates.build(week[1])
            # rebuild the datetime objects since the form doesn't do it properly, this assigns the correct date to the selected time on the form
            if booking_week.week_type == BookingDate.week_types['training']
              sun_datetime = Time.zone.parse("#{booking_week.start_date.strftime('%Y-%m-%d')} #{booking_week.sun_datetime.strftime('%H:%M:%S')}")
              mon_datetime = Time.zone.parse("#{(booking_week.start_date + 1.day).strftime('%Y-%m-%d')} #{booking_week.mon_datetime.strftime('%H:%M:%S')}")
              tue_datetime = Time.zone.parse("#{(booking_week.start_date + 2.days).strftime('%Y-%m-%d')} #{booking_week.tue_datetime.strftime('%H:%M:%S')}")
              wed_datetime = Time.zone.parse("#{(booking_week.start_date + 3.days).strftime('%Y-%m-%d')} #{booking_week.wed_datetime.strftime('%H:%M:%S')}")
              thu_datetime = Time.zone.parse("#{(booking_week.start_date + 4.days).strftime('%Y-%m-%d')} #{booking_week.thu_datetime.strftime('%H:%M:%S')}")
              fri_datetime = Time.zone.parse("#{(booking_week.start_date + 5.days).strftime('%Y-%m-%d')} #{booking_week.fri_datetime.strftime('%H:%M:%S')}")
              sat_datetime = Time.zone.parse("#{(booking_week.start_date + 6.days).strftime('%Y-%m-%d')} #{booking_week.sat_datetime.strftime('%H:%M:%S')}")

              booking_week.update(sun_datetime:, mon_datetime:, tue_datetime:, wed_datetime:, thu_datetime:, fri_datetime:, sat_datetime:)
            end
            if rfq_location.rfq_request.saturday_default? && booking_week.fri? && !book.subsistence_only?
              booking_week.sat = true
            end
            if rfq_location.rfq_request.sunday_default? && booking_week.mon? && !book.subsistence_only?
              booking_week.sun = true
            end
          end

          book.booking_dates.group_by(&:start_date).each do |start_date, weeks|
            # if booking is epa allow training and accommodation weeks to be different, otherwise mandate that training equals accommodation
            next if book.epa_booking?

            training = weeks.select { |week| week.week_type == BookingDate.week_types['training'] }.first
            accommodation = weeks.select { |week| week.week_type == BookingDate.week_types['accommodation'] }.first

            training.update(sun: accommodation.sun, mon: accommodation.mon, tue: accommodation.tue, wed: accommodation.wed,
                            thu: accommodation.thu, fri: accommodation.fri, sat: accommodation.sat)
          end

          if epa_subsistence_only == '1' # if epa subsistence only is enabled then make the selected training dates apply to accommodation as well
            book.subsistence_only = true
            book.booking_dates.group_by(&:start_date).each do |start_date, weeks|
              training = weeks.select { |week| week.week_type == BookingDate.week_types['training'] }.first
              accommodation = weeks.select { |week| week.week_type == BookingDate.week_types['accommodation'] }.first

              accommodation.update(sun: training.sun, mon: training.mon, tue: training.tue, wed: training.wed,
                                   thu: training.thu, fri: training.fri, sat: training.sat)
            end
          end

          book.subsistence_pppn = rfq_location.rfq_request.get_subsistence_cost
          book.subsistence_days = book.training_dates.sum { |week| week.dates_required.count }

          book.save!
          book.reset_stays
        end
      end
    end

    return unless save_as_group == '1' && group_code.present?

    ids = acc_booking_people.learners.pluck(:rfq_learner_id)
    l = RfqLearner.where(id: ids).update_all(group_number: group_code)
    acc_booking_people.learners.update_all(learner_group_code: group_code)
  end

  def virtual?
    virtual_time_slot_id.present? || virtual_module_id.present? || multi_modules_selected.present?
  end

  def save_and_setup_vbookings!
    transaction do
      vt = virtual_time_slot

      vm = (virtual_module.present? ? virtual_module : virtual_time_slot.virtual_module)
      module_date = flexi_module_start.present? ? flexi_module_start.to_date : vm.module_date
      self.payment_method = vm.rfq_request.payment_method
      save!

      acc_bookings_learners.each do |person|
        person.acc_booking_header = self

        person.save!

        book = acc_bookings.new
        book.acc_booking_person = person
        book.virtual_flag = true
        book.payment_method = person.payment_method
        book.total_nights = 1
        book.last_check_out = module_date
        book.first_checkin = flexi_module_end.present? ? flexi_module_end.to_date : vm.module_date

        book.nice_vslot = virtual_friendly_title

        book.joining_instruction_id = joining_instruction_id
        book.save!

        stay = book.acc_booking_stays.new
        stay.check_in = module_date
        stay.check_out = module_date
        stay.save!
      end
    end

    return unless save_as_group == '1' && group_code.present?

    ids = acc_booking_people.learners.pluck(:rfq_learner_id)
    l = RfqLearner.where(id: ids).update_all(group_number: group_code)
    acc_booking_people.learners.update_all(learner_group_code: group_code)
  end

  def save_and_setup_multiple_vbookings!
    transaction do
      acc_bookings_learners.each do |person|
        person.acc_booking_header = self
        person.save!

        # //TODO go thrugh each module and book
        acc_selected_modules.each do |mod|
          booking = acc_bookings.new
          booking.joining_instruction_id = mod.joining_instruction_id
          booking.acc_selected_module = mod
          booking.acc_booking_person = person
          booking.virtual_flag = true
          booking.payment_method = person.payment_method
          booking.total_nights = 1
          booking.last_check_out = mod.flexi_module_end.to_date
          booking.first_checkin = mod.flexi_module_start.to_date
          booking.nice_vslot = "#{mod.title} : #{mod.flexi_module_start.try(:strftime,
                                                                            '%d-%b-%y %H:%M')} until #{mod.flexi_module_end.try(
                                                                              :strftime, '%d-%b-%y %H:%M'
                                                                            )}"
          booking.save!

          stay = booking.acc_booking_stays.new
          stay.check_in = mod.flexi_module_start.to_date
          stay.check_out = mod.flexi_module_end.to_date
          stay.save!
        end
      end

      if save_as_group && group_code.present?
        ids = acc_booking_people.learners.pluck(:rfq_learner_id)
        l = RfqLearner.where(id: ids).update_all(group_number: group_code)
        acc_booking_people.learners.update_all(learner_group_code: group_code)
      end
    end
  end

  def add_slot(person_type, opts = {})
    case person_type
    when 'LEA'
      person = add_learner(opts)
    end
  end

  def add_learner(opts)
    person = acc_booking_people.new
    person.person_type = 'LEA'
    person.rfq_learner_id = opts[:learner_id]
    person.save!
    person
  end

  def get_other_weeks
    acc_booking_blocks.not_cancelled.collect { |x| x.start_date.beginning_of_week(:sunday).to_s }.join(',')
  end

  def group_booking?
    conference.present?
  end

  def send_initial_emails
    if virtual?
      # send_initial_virtual_manager_emails unless self.rfq_location.rfq_request.no_manager_email?
    else
      send_initial_manager_emails unless rfq_location.rfq_request.no_manager_email?
    end
    send_initial_hotel_email if acc_bookings.where(subsistence_only: false).any? && !virtual?
    send_initial_admin_email
    send_immediate_joining_instructions unless rfq_location.rfq_request.no_ji?
  end

  def send_initial_emails_for_multiple_virtual
    # send_initial_admin_email
    return if rfq_location.rfq_request.no_ji?

    acc_bookings.each do |booking|
      AccBookingMailer.delay.send_joining_instructions(booking.id) if booking.can_send_ji?
    end
  end

  def send_initial_manager_emails
    return unless !do_not_send_save_the_date && !acc_bookings.first.late_booking?

    acc_bookings_learners_only.each do |person|
      AccBookingMailer.delay.group_booking_created_manager(id, person.id)
    end
  end

  def send_initial_virtual_manager_emails
    acc_bookings_learners_only.each do |person|
      AccBookingMailer.delay.virtual_group_booking_created_manager(id, person.id)
    end
  end

  def send_initial_admin_email
    AccBookingMailer.delay.send_initial_admin_email(id)
  end

  def send_initial_hotel_email
    AccBookingMailer.delay.send_initial_hotel_email(id)
  end

  def send_virtual_multi_joining_instructions
    first_person = acc_bookings_learners_only.first
    # For manager
    acc_bookings_learners_only.each do |person|
      @booking_person.acc_bookings.each do |booking|
        AccBookingMailer.delay.send_joining_instructions(booking.id) if booking.can_send_ji?
      end
    end
  end

  def send_immediate_joining_instructions
    acc_bookings_learners_only.each do |person|
      person.acc_bookings.each do |booking|
        AccBookingMailer.delay.send_joining_instructions(booking.id) if booking.can_send_ji?(true)
      end
    end
  end

  def confirm!(current_user, from_perspective = nil)
    if from_perspective.present?
      if from_perspective == 'PROV'
        acc_bookings.joins(:acc_booking_person)
                    .where('acc_booking_people.person_type = ?', 'PRO')
                    .where('acc_bookings.subsistence_only is null or acc_bookings.subsistence_only = ?', false)
                    .where(hotel_confirmed_at: nil).update_all(hotel_confirmed_at: Time.zone.now)
      else
        bookings = from_perspective.acc_bookings
                                   .where('acc_bookings.subsistence_only is null or acc_bookings.subsistence_only = ?', false)
                                   .where(hotel_confirmed_at: nil)
        bookings.each do |booking|
          booking.hotel_confirm(current_user)
        end
      end
    else
      bookings = acc_bookings
                 .where('acc_bookings.subsistence_only is null or acc_bookings.subsistence_only = ?', false)
                 .where(hotel_confirmed_at: nil)
      bookings.each do |booking|
        booking.hotel_confirm(current_user)
      end
      if acc_bookings.first.acc_booking_person.person_type == 'TRA'
        AccBookingMailer.delay.send_trainer_booking(acc_bookings.first.id, 'confirmed', :booker)
        AccBookingMailer.delay.send_trainer_booking(acc_bookings.first.id, 'confirmed', :person)
      end
    end
  end

  def booking_confirmations(bids)
    bids.each do |bid|
      AccBookingMailer.delay.send_booking_confirmation(bid)
    end
  end

  def generate_token
    self.confirm_token = loop do
      random_token = SecureRandom.hex(16)
      break random_token unless AccBookingHeader.exists?(confirm_token: random_token)
    end
  end

  def reference
    id
  end

  def non_subsistence_learner_count
    acc_booking_people.enabled.joins(:acc_bookings).where(person_type: 'LEA').where('acc_bookings.subsistence_only is false or acc_bookings.subsistence_only is null').distinct.count
  end

  def check_learners_for_possible_dupe_bookings(blocks_to_check = acc_booking_blocks,
                                                people_to_check = acc_bookings_learners,
                                                exclude_booking = nil)
    fails = []
    people_to_check.each do |person|
      persons_bookings = AccBooking.joins(:acc_booking_person).where(acc_booking_people: { id: person.id })
      persons_bookings.each do |booking|
        next unless persons_bookings.where.not(id: booking.id).where(cancelled_at: nil).where(deleted_at: nil).where(
          'acc_bookings.first_checkin >= ? AND acc_bookings.last_check_out <= ?', booking.first_checkin, booking.last_check_out
        ).present?

        person.errors.add(:rfq_response_room_id, "^Overlaps with existing booking(s) #{booking.id}")
        fails << person
      end
    end
    fails.empty? # if empty ie no fails return true.
  end

  def dates_cannot_be_in_the_past
    return unless virtual_module_id.present?
    return unless flexi_module_start.present? && flexi_module_start < DateTime.now

    errors.add(:flexi_module_start, "can't be in the past")
  end

  def hg_or_client_pay_all?
    ['HG to Pay', 'HG Client to Pay'].include? payment_method
  end

  # only for use with conference linked abhs
  def next_requiring_names?(start, exclude_person = 0)
    base = acc_bookings.joins(:acc_booking_person)
                       .where('acc_booking_people.person_type = ?', 'PGB')
                       .where('acc_booking_people.id != ?', exclude_person)
                       .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null')
                       .order('acc_bookings.first_checkin asc, acc_bookings.last_check_out asc, acc_bookings.room_type asc, acc_booking_people.id')

    # check similar_dates
    similar_bookings = base.where('acc_bookings.first_checkin >= ? ', start)

    return similar_bookings.first if similar_bookings.count > 0

    return base.first if base.count > 0

    false
  end

  # only for use with conference linked abhs
  def all_requiring_names(exclude_person = 0)
    acc_booking_people.joins(:acc_bookings)
                      .where(person_type: 'PGB')
                      .where('acc_booking_people.id != ?', exclude_person)
                      .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null')
  end

  def all_named
    acc_booking_people.joins(:acc_bookings)
                      .where(person_type: 'AGB')
                      .where('acc_bookings.cancelled_at is null AND acc_bookings.deleted_at is null')
  end

  def adult_booking?
    booking_type == 'ADULT'
  end

  def virtual_module_date
    # TODO: check if this is acceptable
    if flexi_module_date.present?
      flexi_module_date
    elsif self&.virtual_module&.module_date.present?
      virtual_module.module_date
    elsif self&.virtual_time_slot&.virtual_module&.module_date.present?
      virtual_time_slot.virtual_module.module_date
    else
      'Error retrieving date'
    end
  end

  def virtual_module_title
    if virtual_module_id.present?
      virtual_module.title
    else
      virtual_time_slot.try(:virtual_module).try(:title)
    end
  end

  def virtual_module_description
    if virtual_module_id.present?
      virtual_module.description
    else
      virtual_time_slot.try(:virtual_module).try(:description)
    end
  end

  def virtual_module_start
    if virtual_module_id.present?
      flexi_module_start
    else
      virtual_time_slot.try(:starting_at)
    end
  end

  def virtual_module_end
    if virtual_module_id.present?
      flexi_module_end
    else
      virtual_time_slot.try(:ending_at)
    end
  end

  def online_resource
    if virtual_module_id.present?
      virtual_module
    else
      virtual_time_slot.try(:virtual_module)
    end
  end

  def virtual_module_friendly_times
    "#{virtual_module_start.try(:strftime, '%H:%M')} until #{virtual_module_end.try(:strftime, '%H:%M')}"
  end

  def virtual_friendly_title
    [virtual_module_title, ', ', virtual_module_date.strftime('%d-%b-%y'), ' : ',
     virtual_module_friendly_times].join
  end

  def survey_link
    loc = rfq_location
    ENV['FEEDBACK_APP'] + '/survey/' + loc.survey_token
  end

  def ical
    cal = Icalendar::Calendar.new
    cal.event do |e|
      e.dtstart = DateTime.civil(virtual_module_date.year, virtual_module_date.month,
                                 virtual_module_date.day, virtual_module_start.hour, virtual_module_start.min)
      e.dtend = DateTime.civil(virtual_module_date.year, virtual_module_date.month,
                               virtual_module_date.day, virtual_module_end.hour, virtual_module_end.min)
      e.summary = virtual_module_title
      e.description = virtual_module_description
    end
    cal.publish
    cal.to_ical
  end

  def get_current_block
    acc_booking_blocks.where('end_date >= :today and start_date >= :today', today: Date.today)
  end
end
