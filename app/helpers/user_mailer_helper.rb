#encoding: UTF-8

module UserMailerHelper
  #NB this helper assumes @user is present

  def welcome_body
    out =""
    out << header

    if @user.contact.for_client?
      if @user.con_enabled?
        out << conference_stop
        out << cstop_options
      end
      if @user.app_enabled?
        out << apprentice_stop
      end
      if @user.acc_enabled?
        out << accommodation_stop
      end
    elsif @user.contact.for_supplier?
      if @user.role == 'HOTEL'
        out << hotel_user
      else
        out << chain_user
      end
    end

    if promoting?
      out << promotion
      out << temp_login
    else
      out << login

    end

    out << footer
    out.html_safe
  end

  def advancing_body
    out =""
    out << "<p style='margin-bottom: 2em'><strong>Dear #{@user.full_name_or_email}</strong>,</p>"
    out << "<p>Congratulations, you are now an advanced user on Conference Hub. </p>"
    out << advanced_user
    out << login
    out << footer
    out.html_safe
  end

  def invited_body
    out =""
    out << invited_header
    out << invite_token
    if @user.con_enabled?
      out << conference_stop
    end
    if @user.app_enabled?
      out << apprentice_stop
    end
    if @user.acc_enabled?
      out << accommodation_stop
    end
    if @user.con_enabled?
      out << event_stop
    end

    out << footer
    out.html_safe

  end

  def cstop_options
    res=""
    if @user.advanced_user?
      res << advanced_user
    else
      res << basic_user
    end
    if @user.joined_loyalty_scheme.blank?
      res << not_loyalty
    else
      res << in_loyalty
    end
    res
  end

  def promoting?
    @promoting
  end

  def registering?
   !@promoting && !@inviting
  end

  def header
    "<p style='margin-bottom: 2em'><strong>Dear #{@user.full_name_or_email}</strong>,</p>
     <p>
       Welcome to Servace.<br/> #{', thank you for registering' unless promoting? }.
    </p>"
  end

  def invited_header
    out = []
    out << "<p>REMINDER</p>" if @resend
    out << "<p style='margin-bottom: 2em'><strong>Dear #{@user.email}</strong>,</p>
     <p>
       Welcome to Servace.
    </p>"
    out.join()
  end

  def conference_stop
    out =   "<h3>ConferenceHub</h3>
            Within ConferenceHub you can now view your enquiries, proposals and history online at the touch of a button.
            Our dedicated team and this online system will help you find the right venue saving you time and money.
            </p>"
    if @inviting
      out << "<p style='margin-bottom: '>
                Why not also join our loyalty scheme? This is our way of saying thank you,
                for every event booked over £2000*
                you can collect points and receive shopping vouchers for yourself,
                your company or a charity of your choice.
                </p>" unless @loyalty_not_wanted
    end
    out
  end

  def apprentice_stop
    "<h3>ApprenticeHub</h3>This streamlined booking solution supports you and your learners throughout the booking process from sending the Joining instruction and reminders for training to online dedicated feedback to support continuous improvement of the stay"
  end

  def accommodation_stop
    "<h3>AccommodationHub</h3>Our online portal gives you access to over 80,000 hotels worldwide. Log-in to make a booking in line with your companies travel policy and view rates in your preferred locations."
  end

  def event_stop
    "<h3>EventHub</h3>EventHub allows you to create, customise and build your event web page and emails. This simple online solution gives you a streamlined solution to view manage payments, track bookings and instant access to view your event progress 24/7."

  end



  def in_loyalty
    "As you are a member of our loyalty scheme, for every event you book over £1000*
     you can now collect points and receive shopping vouchers for yourself,
     your company or a charity of your choice.
     This is our way of saying thank you to you!
     To view your enquiries, points or to redeem vouchers please visit #{ link_to "Conference Hub", new_user_session_url(:host => P3_HOST)}
     and log in to your account."
  end

  def not_loyalty
    "<p style='margin-bottom: 2em'>
    Why not also join our loyalty scheme? This is our way of saying thank you,
    for every event booked over £2000*
    you can collect points and receive shopping vouchers for yourself,
    your company or a charity of your choice.
    </p>
    <p style='margin-bottom: 2em'>
    So what are you waiting for?
    To join the loyalty scheme please visit
    #{ link_to 'My Conference Hub', client_loyalty_scheme_url(:host => P3_HOST) }.
   </p>"
  end

  def adv_bullets
    "<table face='Helvetica, Arial' bgcolor='#FFFFFF' style='background: #FFFFFF; height: 100%;margin-left:50px; font-family: Helvetica, Arial;font-size:13px; color:#4a4a4c;'>
            <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> View and edit your events online</td>
            </tr>
            <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> Export your bookings and create reports</td>
            </tr>
            <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> Client Dashboard – online reminders of next requirement</td>
            </tr>
            <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> Search and shortlist your perfect venue</td>
            </tr>
            <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> Map It advanced search</td>
            </tr>
            <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> Offers and news viewable in required locations</td>
            </tr>
           <tr>
              <td>#{image_tag attachments['pin-mini-cs.png'].url, :alt => 'pin'} </td>
              <td>&nbsp;</td>
              <td> View preferred rates across your company</td>
            </tr>
        </table>"
  end

  def advanced_user
    "<p>As an advanced user you can not only view your enquiries,
     proposals and history online but also can
     take advantage of the additional benefits now available to you, these include:" +
     adv_bullets +
     "If you have not created your conference yet, then just
      #{link_to 'log in to your personal dashboard', new_user_session_url(:host => P3_HOST) }
     and select ‘Click here for a new conference’    </p>"
  end

  def basic_user
    " <p style='margin-bottom: 2em'>
    If you have not created your conference yet, then just #{ link_to "Log In", new_user_session_url(:host => P3_HOST)} and
    select ‘Click here for a new conference’</p>

    <p>Why not upgrade you access on
    Conference Hub? Click #{link_to 'here' , cs_benefit_url(:host => P3_HOST) }
     </p>"
  end

  def login
    "<p>
      To log in please #{link_to 'click here', new_user_session_url(:host => P3_HOST) } and enter your username (#{@user.email}) and password,
      if you have forgotten your password then please select this #{link_to 'forgotten your password link', new_password_url(@user, :host => P3_HOST ) }
      and log into your account.
    </p>"
  end

  def temp_login
    "<p>
    Your username: <strong> #{@user.email}</strong>  </br>
    Your temporary password: <strong> #{@user.contact.temp_password}</strong> (please do not use the copy & paste option to login)
  </p>

  <p style='margin-bottom: 2em'>
    You will be prompted to change your password once logged in, which needs to be at least 8 characters; and include 1 capital letter and 1 number i.e. Servace1.
  </p>"
  end

  def promotion
    "<p>Login at #{link_to 'app.servace.co.uk/users/sign_in', new_user_session_url(:host => P3_HOST) } using the following details:</p>"
  end

  def invite_token
    "<h3>Servace</h3>You have been invited to join Servace by your organisation: #{@contact.parent.name}
     <p>Please visit #{link_to 'Update My Profile', invited_user_url( @contact.invite_token) } to complete your profile and log-in.</p>
     <p>When you have logged in you will be able to start to utilise the Servace dedicated service. If you have not yet had training our team will be happy to help, please call us on #{MAIN_PHONE} to arrange webinar training.</p>".html_safe
  end

  def hotel_user
    hotel = @user.contact.parent
    res = ""
    res << "<p> #{hotel.try(:creator).try(:contact).try(:full_name_or_email) || 'Someone' } has registered your venue with us at www.servace.co.uk.</p>"
    case true
    when (hotel.finished? && hotel.verified?)
     res <<    "<p>Your hotel needs no further editing and is ready to make quotations</p>"
    when hotel.finished?
      res <<  " <p>Your hotel is completed but is waiting for approval by Servace</p>"
    else
      res << " <p>Your venue profile is not quite finished; please complete stages 1-5, which will enable us to verify your profile.</p>"
    end
    res
  end

  def chain_user
    "<p>Your chain and hotels can now be seen within Conference Hub.</p>"
  end



  def footer
    res = []
    unless Rails.env.test?
      res << "<p>If you would like to read more about our other services please visit #{link_to "www.servace.co.uk", hg_home_url} or speak to a member of the team we will be happy to help.</p>"
      res << "<p>
      Many Thanks,<br/>
      Servace brought to you by Servace
      </p>"
    end
    if @user.contact.for_client?
      res << "<p><small>*For further information please visit #{link_to 'Loyalty Page', cs_loyalty_url(:host => P3_HOST)} to view Loyalty scheme T&C’s</small></p>" if @user.con_enabled?
    end
    res.join()
  end

  def rfq_sig(adult=false)
    if adult
      "<p>Many thanks,<br/>
        Servace Accommodation Team</p>".html_safe
    else
      "<p>Many thanks,<br/>
        Servace Apprentice Team</p>".html_safe
    end
  end


end
