module NavigationHelper

  def currently_at(pin, selected_tab, selected_sub_tab=nil, sub_sub_menu = nil, selected_sub_sub_tab = nil)
    render(:partial => "layouts/navigation/pin_tabs", :locals => {:current_pin => pin})

    render(:partial => "layouts/navigation/#{pin.downcase}_main_nav",
           :locals => {:current_tab => selected_tab,
                       :selected_sub_tab => selected_sub_tab,
                       :sub_sub_menu => sub_sub_menu,
                       :selected_sub_sub_tab => selected_sub_sub_tab}) +
    render( :partial => 'layouts/flash_and_dropfile' )
  end

  def admin_currently_at(selected_tab, sub_menu=nil, selected_sub_tab = nil, parent=nil, sub_sub_menu = nil, selected_sub_sub_tab = nil)
    render(:partial => "layouts/navigation/admin_tabs",
            :locals => {:current_tab => selected_tab,
                  :sub_menu => sub_menu,
                  :selected_sub_tab => selected_sub_tab,
                  :parent => parent,
                  :sub_sub_menu => sub_sub_menu,
                  :selected_sub_sub_tab => selected_sub_sub_tab}) +
     render( :partial => 'layouts/flash_and_dropfile' )
  end

  def nav_tab(title, url, options={})
    current_tab = options.delete(:current)
    current_tab ||= "NONE"
    link_options = options.delete(:link_options)
    css_classes = [options[:class].to_s]
    if current_tab.present?
      css_classes << (title =~ /#{current_tab}/ ? "active" : 'inactive')
    else
      css_classes << "inactive"
    end
    options[:class] = css_classes.compact.join(" ")
    content_tag(:li, link_to(title, url, link_options), options)
  end

  def nav_pin(key, title, url, options={})
    current_tab = options.delete(:current)
    which_class = (current_tab.downcase == key.downcase) ? "active" : nil
    options[:class] = (options[:class].to_s.split(" ") + [which_class]).compact.join(" ")
    arrow_class = "arrow-#{key.downcase}"
    content_tag(:li, class: options[:class]) do
      link_to(url) do
        safe_join([
          image_tag('RGBServaceArrow.png',style: "width: 25px; height: 25px; rotate: 90deg; margin-right: 6px;" , class: "nav-arrow #{arrow_class}"),
          content_tag(:span, title.html_safe)
        ])
      end
    end
  end

  def new_nav_pin(key, title, url, options={})
  current_tab = options.delete(:current)
    which_class = (current_tab.downcase == key.downcase) ? " active" : ''
    arrow_class = "arrow-#{key.downcase}"
    options[:class] = (options[:class].to_s.split(" ") + [which_class]).compact.join(" ")
    content_tag(:li, class: options[:class] + 'd-inline-block') do
      link_to(url, class: "nav-link" + which_class) do
        safe_join([
          image_tag('RGBServaceArrow.png',style: "width: 25px; height: 25px; rotate: 90deg; margin-right: 6px;" , class: "nav-arrow #{arrow_class}"),
          content_tag(:span, title.html_safe, class: '')
        ])
      end
    end
  end

  def nav_for_contacts(params, parent, selected)
    if params[:organisation_id].present?
      @organisation ||= parent
      if @contact.present? && !@contact.new_record?
        admin_currently_at('Organisations', 'organisation', 'Contacts', parent, 'contact', selected)
      else
        admin_currently_at('Organisations', 'organisation', 'Contacts')
      end
    elsif params[:hotel_id].present?
      @hotel = parent
      if @contact.present? && !@contact.new_record?
        admin_currently_at('Hotels', 'hotel', 'Contacts', parent, 'contact', selected)
      else
        admin_currently_at('Hotels', 'hotel', 'Contacts')
      end
    elsif params[:chain_id].present?
      @chain = parent
      if @contact.present? && !@contact.new_record?
        admin_currently_at('Chains', 'chain', 'Contacts', parent, 'contact', selected)
      else
        admin_currently_at('Chains', 'chain', 'Contacts')
      end


    end
  end

  def release_star
    "Releases <div data-target='releases.star' class='hide fade star-circle' >
    <span   class='glyphicon glyphicon-star  ' style='font-size:20px;color:#eeff00;text-shadow: 0 2px 2px grey;'></span>
    </div>".html_safe
  end

end
