# encoding: UTF-8

class AprProformaPdf < Prawn::Document

    include ActionView::Helpers::NumberHelper
    include ActionView::Helpers::UrlHelper
    include ActionView::Helpers::AssetTagHelper
    include ActionView::Helpers::OutputSafetyHelper
    include Admin::RfqResponsesHelper

    FONT_SIZE       = 10
    H1_SIZE         = 14
    H2_SIZE         = 12
    GENERAL_MARGIN  = 30

    def initialize(options={})
      @booking = options[:booking]
      if options[:test_data].present?
        @rfq_location = options[:test_data]
        @hotel = @rfq_location.accepted_response.rfq_proposed_hotel.hotel
        @cbrief = @rfq_location.accepted_response.rfq_client_briefings.first
        @hbrief = @rfq_location.accepted_response.rfq_hotel_briefings.first

        @booking = AccBooking.new
        @booking.id = 999999999
        @booking.acc_booking_person = AccBookingPerson.new(:person_type => "LEA", :temp_learner_name => "Joe Bloggs")
        @booking.acc_booking_person.rfq_learner = RfqLearner.new
        @booking.acc_booking_person.rfq_learner.rfq_business_unit = RfqBusinessUnit.new(:name => "My Unit")
        @booking.room_type = "Twin room for 2 people"
        @booking.pkg_type = "DBB"
        @booking.payment_method = AccBooking::PAYMENT_TYPES.first
        @booking.acc_booking_stays = [AccBookingStay.new(:check_in => Date.today, :check_out => Date.today + 4)]
        @person = @booking.acc_booking_person

        @rfq_request = RfqRequest.new
        @rfq_response = RfqResponse.new
        @rfq_programme = RfqProgramme.new
        @client = Organisation.new

      else
        @hotel = @booking.hotel if !@booking.subsistence_only?
        @rfq_location = @booking.acc_booking_header.rfq_location
        @rfq_request = @rfq_location.rfq_request
        @rfq_programme = @rfq_request.rfq_programme
        @client = @rfq_programme.client
        @person = @booking.acc_booking_person
        if @person.person_type == "AAB"
          @rfq_response = @booking.rfq_response_room.rfq_response

          @cbrief = @rfq_response.rfq_client_briefings.first
          @hbrief = @rfq_response.rfq_hotel_briefings.first
        else
          @rfq_response = @rfq_location.accepted_response
          @cbrief = @rfq_response.rfq_client_briefings.first
          @hbrief = @rfq_response.rfq_hotel_briefings.first
        end

      end

      @output_to = options[:output_to] || "/tmp"

      @to_file = options[:to_file] || false

      #raise "NO Booking" if options[:test_data].blank? && @booking.blank?
      #raise "NO LOCATION" unless @rfq_location.present?
      @toc = []
      options.merge!(:page_size => "A4", :bottom_margin => 40)
      @tic = "*"
      super
      font_setup
    end

    def font_setup
      font_families.update("Roboto" => {
        :normal => "#{Rails.root.to_s}/app/assets/fonts/Roboto-Regular.ttf",
        :italic => "#{Rails.root.to_s}/app/assets/fonts/Roboto-MediumItalic.ttf",
        :bold => "#{Rails.root.to_s}/app/assets/fonts/Roboto-Bold.ttf",
      })
      font "Roboto"
    end

    def to_pdf
      font_size FONT_SIZE
      header
      main_table
      footer

      if @to_file
        render_file("#{@output_to}/proforma_#{@rfq_request.id}_#{@rfq_location.id}.pdf")
      else
        render
      end
    end

    def header
      h1("Proforma Invoice", :center)
      address = @hotel.present? ? ([@hotel.name] + [@hotel.primary_location.address_1 + ' ' + @hotel.primary_location&.postcode]).join("\n") : "N/A"
      phone_no = @hotel.present? ? "Tel: #{@hotel.primary_location.phone}" : "N/A"
      h2(address, :center)
      h2(phone_no, :center)

      h2(Date.today.to_s)
    end

    def main_table

      data = 
        ["Learner Manager Name", @booking.acc_booking_person.person_type == 'LEA' ? @booking.acc_booking_person.rfq_learner.manager : 'N/A'],
        ["Guest Name", @person.name_for_table],
        ["Stays", @booking.display_stays.gsub("<br>", "\n")],
        ["Room Type", @booking.room_type_for_confirmation],
        ["Accommodation Package", @booking.pkg_type],
        ["Booking Number", @booking.id],
        [@cbrief.business_unit_override || "Business Unit" , @person.business_unit_for_table]
      if @person.person_type == "AAB"
        data <<  ["Dealership Address", @person.address_one_liner]
      end

      data << ["Payment Terms", @booking.payment_method] if @booking.payment_method.present?

      if !@booking.acc_booking_header.rfq_location.hide_payment_section
        data <<  ["#{@booking.no_nights_charge_manager} Nights", "Accommodation"] if @booking.no_nights_charge_manager > 0 && !@booking.subsistence_only?

        data <<["#{@booking.supplement_required_for_x_nights} Nights Single Room Supplement", "Accommodation"] if @booking.supplement_pppn > 0 && !@booking.subsistence_only?

        data <<["#{@booking.subsistence_days} Days Subsistence", "Subsistence Allowance"] if @booking.subsistence_pppn > 0

        if @booking.card_pay?
          if @booking.confirmed_by.present?
            data <<  ["Total Amount Paid",number_to_currency(@booking.total_paid_manager, :unit => "£")]
          else
            data <<  ["Total Amount Due",number_to_currency(@booking.room_rate_plus_fees, :unit => "£")]
          end
        else
          data <<  ["Total Amount Due",number_to_currency(@booking.total_due, :unit => "£")]
        end
      end



      table(
       data, :width => 500
      ) do
        columns(0).style :width => 150
      end

    end

    def footer
      move_down 30
      text "All payments must be made at least 5 business days before the night of arrival.", :align => :center
      text "All deposits are non-refundable", :align => :center
      text "Please note this is not a VAT receipt. A full VAT Invoice will be issued on the day of departure.", :align => :center
      text "All bookings and payments are subject to the ServAce terms and conditions issued at the time of booking.", :align => :center
      unless @booking.card_pay?
        text "Please make cheques payable to #{@hbrief.make_payable_to}", :align => :center, :style => :bold
      end
      text "Company Reg No: #{@hotel.company_reg_number}", :align => :center, :style => :bold if @hotel.present?

    end

    def h1(output_text, align=:left)
      font_size H1_SIZE
      text output_text, :align => align
      move_down 20
      font_size FONT_SIZE
    end

    def h2(output_text, align=:left)
      font_size H2_SIZE
      move_down 20
      text output_text, :align => align
      move_down 20
      font_size FONT_SIZE
    end


end
