class StripeReconciliationMonthlyInvoicePdf
  def initialize(bookings, recipient)
    @bookings = bookings
    @recipient = recipient
  end

  def print_data
    pdf = Prawn::Document.new
    logopath = "#{Rails.root}/app/assets/images/imgphase3/servace-logo.jpg"
    initial_y = pdf.cursor
    initialmove_y = 5
    address_x = 0
    invoice_header_x = 325
    lineheight_y = 12
    font_size = 9

    pdf.move_down initialmove_y

    # Add the font style and size
    pdf.font "Helvetica"
    pdf.font_size font_size

    title = @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) ? "Monthly Client Summary" : "Monthly Hotel Invoice Request"
    pdf.text title, size: 12, style: :bold

    pdf.move_down 30
    #start with EON Media Group
    pdf.text_box "ServAce", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y
    pdf.text_box "10 Business Village, Wynyard Business Park", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y
    pdf.text_box "Wynyard, TS22 5FG", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y * 2
    pdf.text_box "VAT No: 888 9700 50", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y

    last_measured_y = pdf.cursor
    pdf.move_cursor_to pdf.bounds.height

    pdf.image logopath, :width => 215, :position => :right

    pdf.move_cursor_to last_measured_y

    # client address
    pdf.move_down 30
    last_measured_y = pdf.cursor

    # client and hotels have primary locations but business unit location info is on the business unit itself
    primary_location = generate_primary_location(@recipient)
    pdf.text_box "#{@recipient.name}", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y
    pdf.text_box "#{primary_location.address_1}, #{primary_location.address_2}, #{primary_location.address_3}", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y
    pdf.text_box "#{primary_location.city + ',' if primary_location.city.present?} #{primary_location.county}, #{primary_location.postcode}", :at => [address_x,  pdf.cursor]

    pdf.move_cursor_to last_measured_y

    total_charged = @bookings.sum {|b| b.booking_payments.charged.sum(&:amount)}
    total_room_cost = @bookings.sum {|b| b.calculate_room_cost}
    total_room_vat = AccBooking.new.extract_vat_from_cost(total_room_cost.to_pence).to_f / 100

    total_commission = if @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit)
      0
    elsif @recipient.is_a?(Hotel)
      @bookings.sum {|b| b.calculate_booking_commission}
    end

    total_cost = if @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) #this means client not a hotel
      @bookings.sum {|b| b.total_paid}
    elsif @recipient.is_a?(Hotel)
      @bookings.sum {|b| b.calculate_room_cost}
    end

    invoice_header_data = [
      ["For bookings within", "#{@bookings.first.first_checkin.strftime("%B %Y")}"],
      ["Total Nights", "#{total_nights(@bookings)}"],
      ["Amount Due", "£#{'%.2f' % (total_cost)}"],
    ]

    pdf.table(invoice_header_data, :position => invoice_header_x, :width => 215) do
      style(row(0..1).columns(0..1), :padding => [1, 5, 1, 5], :borders => [])
      style(row(2), :background_color => 'e9e9e9', :border_color => 'dddddd', :font_style => :bold)
      style(column(1), :align => :right)
      style(row(2).columns(0), :borders => [:top, :left, :bottom])
      style(row(2).columns(1), :borders => [:top, :right, :bottom])
    end

    pdf.move_down 45


    # Hotel Accommodation Section 
    pdf.text_box "Hotel Accommodation", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y
    pdf.text_box "Total monthly hotel accommodation (see backing schedule)", :at => [address_x,  pdf.cursor]

    hotel_room_cost_data = [
      ["Net", "VAT", "Total"],
      ["£#{'%.2f' % (total_room_cost - total_room_vat)}", "£#{'%.2f' % total_room_vat}", "£#{'%.2f' % (total_room_cost)}"],
    ]

    pdf.move_up 15
    pdf.table(hotel_room_cost_data, :position => invoice_header_x, :width => 215) do
      style(row(0..1).columns(0..2), :padding => [1, 5, 1, 5], :borders => [])
      style(column(2), :align => :right)
    end

    pdf.move_down 15
    pdf.text_box "The above amount includes the total VAT amount shown above which is your input VAT to recover subject to
                    normal input VAT recovery rules", :at => [address_x,  pdf.cursor]

    pdf.move_down 60

    # Subsistence Section 
    pdf.text_box "Subsistence Contribution (Outside the scope of VAT)", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y

    total_subsistence_cost = @bookings.sum {|b| b.calculate_subsistence_fee}

    subsistence_cost_data = [
      ["Total"],
      ["£#{'%.2f' % total_subsistence_cost}"],
    ]

    pdf.move_up 15
    pdf.table(subsistence_cost_data, :position => invoice_header_x, :width => 215) do
      style(row(0..1).columns(0), :padding => [1, 5, 1, 5], :borders => [])
      style(column(0), :align => :right)
    end
    
    pdf.move_down 15
    pdf.text_box "Total monthly subsistence contribution (see backing schedule)", :at => [address_x,  pdf.cursor]

    pdf.move_down 60

    if  @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) #this means client not a hotel
      # Payment Processing Fee section
      pdf.text_box "Payment Processing Fee", :at => [address_x,  pdf.cursor]
      pdf.move_down lineheight_y
      pdf.text_box "Total monthly payment processing fees (see backing schedule)", :at => [address_x,  pdf.cursor]

      total_stripe_cost = AccBooking.new.extract_stripe_from_cost(@bookings.sum {|b| b.booking_payments.charged.sum(&:amount)})
      total_stripe_vat = (AccBooking.new.extract_vat_from_stripe_cost(total_charged).to_f / 100 )
      # this needs to be in pence for the vat calculation, then we concert it to £ for the view here
      total_stripe_cost = (total_stripe_cost.to_f / 100).round(2)
      stripe_fee_data = [
        ["Net", "VAT", "Total"],
        ["£#{'%.2f' % total_stripe_cost }", "£#{'%.2f' % total_stripe_vat}", "£#{'%.2f' % (total_stripe_cost + total_stripe_vat)}"],
      ]

      pdf.move_up 15
      pdf.table(stripe_fee_data, :position => invoice_header_x, :width => 215) do
        style(row(0..1).columns(0..2), :padding => [1, 5, 1, 5], :borders => [])
        style(column(2), :align => :right)
      end

      pdf.move_down 15
      pdf.text_box "The above amount includes the total VAT amount shown above which is your input VAT to recover subject to
                      normal input VAT recovery rules", :at => [address_x,  pdf.cursor]
    elsif @recipient.is_a?(Hotel)
      # Commission Section
      pdf.text_box "ServAce Commission", :at => [address_x,  pdf.cursor]
      pdf.move_down lineheight_y
      pdf.text_box "Total monthly ServAce commission (see backing schedule)", :at => [address_x,  pdf.cursor]
      pdf.move_down lineheight_y
      pdf.text_box "This will be invoiced separately", :at => [address_x,  pdf.cursor]

      commission_data = [
        ["Total"],
        ["£#{'%.2f' % (total_commission)}"],
      ]

      pdf.move_up 15
      pdf.table(commission_data, :position => invoice_header_x, :width => 215) do
        style(row(0..1).columns(0), :padding => [1, 5, 1, 5], :borders => [])
        style(column(0), :align => :right)
      end
    end

    pdf.move_down 60
    







    pdf.start_new_page

    pdf.text_box "Breakdown of Monthly Totals", :at => [address_x,  pdf.cursor]
    pdf.move_down lineheight_y
    
    invoice_services_data = [[{content: "Booking ID", width: 60}, "Stay", "Total Nights", "Name"]]
    invoice_services_data[0] << "Hotel Cost (inclusive of VAT at 20%)" if !@bookings.first.subsistence_only?
    invoice_services_data[0] << "Subsistence Contribution (outside the scope of VAT)"
    invoice_services_data[0] << if  @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) #this means client not a hotel
      "Payment Processing Fee (inclusive of VAT at 20%)"
    elsif @recipient.is_a?(Hotel)
      "ServAce Commission (inclusive of VAT at 20%)"
    end
    invoice_services_data[0] << {content: "Total", width: 50}

      @bookings.each_with_index do |booking, index|
        
        booking_total_cost = booking.total_paid
        booking_total_charged = booking.booking_payments.charged.sum(&:amount)
        subsistence_cost = booking.calculate_subsistence_fee 
        room_cost = booking.calculate_room_cost
        booking_commission = booking.calculate_booking_commission
        person = booking.acc_booking_person.get_person
        # dates
        invoice_services_data << [booking.id.to_s, (booking.first_checkin.strftime("%d/%m/%Y") + '  ' + booking.last_check_out.strftime("%d/%m/%Y")).html_safe, total_nights([booking]).to_s, (person.forename + ' ' + person.surname)]

        # hotel cost
        if @bookings.first.subsistence_only?
          invoice_services_data[index + 1] << ''
        else
          invoice_services_data[index + 1] << '£%.2f' % room_cost
        end

        # Subsistence Contribution (outside the scope of VAT)
        invoice_services_data[index + 1] << '£%.2f' % booking.calculate_subsistence_fee 
        
        # Payment Processing Fee (inclusive of VAT at 20%) OR ServAce Commission (inclusive of VAT at 20%)
        invoice_services_data[index + 1] << if  @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) #this means client not a hotel
          '£%.2f' % ((booking.extract_stripe_plus_vat_from_cost(booking_total_charged)).to_f / 100).round(2)
        elsif @recipient.is_a?(Hotel)
          '£%.2f' % booking_commission
        end

        # total
        booking_total_net_cost = if @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) #this means client not a hotel
          booking_total_cost
        elsif @recipient.is_a?(Hotel)
          (room_cost)
        end

        # Total
        invoice_services_data[index + 1] << '£%.2f' % (booking_total_net_cost)
        
      end

      commission_or_stripe = if  @recipient.is_a?(Organisation) || @recipient.is_a?(RfqBusinessUnit) #this means client not a hotel
        total_stripe_cost + total_stripe_vat
      elsif @recipient.is_a?(Hotel)
        total_commission
      end

      invoice_services_data << ["", "", "", "",
        '£%.2f' %  (total_room_cost),
        '£%.2f' %  (total_subsistence_cost),
        '£%.2f' %  (commission_or_stripe),
        '£%.2f' %  (total_cost)
      ]

    pdf.table(invoice_services_data, :width => pdf.bounds.width) do
      style(row(1..-1).columns(0..-1), :padding => [4, 5, 4, 5], :borders => [:bottom], :border_color => 'dddddd')
      style(row(0), :background_color => 'e9e9e9', :border_color => 'dddddd', :font_style => :bold)
      style(row(0).columns(0..-1), :borders => [:top, :bottom])
      style(row(0).columns(0), :borders => [:top, :left, :bottom])
      style(row(0).columns(-1), :borders => [:top, :right, :bottom])
      style(row(-1), :border_width => 2)
      style(column(2..-1), :align => :right)
    end

    pdf.move_down 15

    pdf.text_box "The above amount includes the total VAT amount which is your input VAT to recover subject to normal input VAT recovery rules.", :at => [address_x,  pdf.cursor]

    if @recipient.is_a?(Hotel)
      pdf.move_down 30
      pdf.text 'Please submit a single invoice to ServAce to claim payment for the bookings detailed above. This only relates to bookings where payment has been made directly to ServAce via Stripe.', :align => :left

      pdf.move_down 10
      pdf.text 'Invoices should be <NAME_EMAIL>'
    end

    return pdf
  end




  def generate_primary_location(recipient)
    if recipient.is_a?(Organisation) || recipient.is_a?(Hotel)
      recipient.primary_location
    elsif recipient.is_a?(RfqBusinessUnit)
      # Mock up a location here for business units since it doesn't have one
      Location.new(address_1: recipient.address1, address_2: recipient.address2, address_3: recipient.address3, city: '', county: recipient.county, postcode: recipient.postcode)
    end
  end

  def total_nights(bookings)
    bookings.sum {|b| b.cancelled? ? 0 : b.total_nights}
  end
end
