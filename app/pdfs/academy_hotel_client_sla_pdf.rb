class AcademyHotelClientSlaPdf < Prawn::Document

  include ActionView::Helpers::NumberHelper
  include ActionView::Helpers::UrlHelper
  include ActionView::Helpers::AssetTagHelper
  include ActionView::Helpers::OutputSafetyHelper
  include Admin::RfqResponsesHelper

  FONT_SIZE       = 10
  H1_SIZE         = 15
  H2_SIZE         = 13
  H3_SIZE         = 11
  GENERAL_MARGIN  = 30

  def initialize(options={})
    @to_file = true
    @rfq_response = options[:rfq_response]
    @hotel= @rfq_response.rfq_proposed_hotel.hotel
    @rfq_location = @rfq_response.rfq_location
    @rfq_request = @rfq_response.rfq_location.rfq_request
    @rfq_programme = @rfq_request.rfq_programme
    @client = @rfq_programme.client
    @hbrief = @rfq_response.rfq_hotel_briefings.first
    @cbrief = @rfq_response.rfq_client_briefings.first
    @output_to = options[:output_to] || "/tmp"

    if @cbrief.business_unit_override.present?
      @bu_name_to_use = @cbrief.business_unit_override
    else
      @bu_name_to_use = "company"
    end

    @to_file = options[:to_file] || false
    raise "NO Response" unless @rfq_response.present?
    raise "NO LOCATION" unless @rfq_location.present?
    @toc = []
    options.merge!(:page_size => "A4", :bottom_margin => 40)
    @tic = "*"
    @next_num = 1
    super
    font_setup
  end

  def font_setup
    font_families.update("Roboto" => {
        :normal => "#{Rails.root.to_s}/app/assets/fonts/Roboto-Regular.ttf",
        :italic => "#{Rails.root.to_s}/app/assets/fonts/Roboto-MediumItalic.ttf",
        :bold => "#{Rails.root.to_s}/app/assets/fonts/Roboto-Bold.ttf",
    })
    font "Roboto"
  end

  def to_pdf

    font_size FONT_SIZE
    front_cover
    dist_list
    overview
    sla_hotel
    other_info
    contact_details
    toc
    footer

    render_file("#{@output_to}/h-c-sla#{@rfq_request.id}-#{@rfq_location.id}.pdf")

  end

  def front_cover

    move_down 200
    image "#{Rails.root.to_s}/app/assets/images/servace-logo.jpg", :position => :center
    move_down 70
    font_size 30
    text "Service Level Agreement", :align => :center

    font_size 14
    move_down 40
    text "Between", :align => :center
    move_down 10
    text "#{@client.name}", :align => :center
    move_down 10
    text "and", :align => :center
    move_down 10
    text "#{@hotel.name}", :align => :center


    font_size FONT_SIZE
    start_new_page

  end

  def dist_list
    move_down 50
    h1 "Distribution List:"
    move_down 10
    table([
              ["#{@rfq_request.hg_contact.full_name}", "Servace"],
              ["#{@rfq_request.contact.full_name}", "#{@client.name}"],
              [@hbrief.general_manager, "#{@hotel.name}"]

          ], :cell_style => {:borders => []})
    start_new_page
  end

  def overview
    @toc << ["1.0", "Overview", page_number]
    h1 "1.0 Overview"
    h2 "Definitions"
    text "BLACK OUT: the functionality on the System for the Hotel to close out availability to the Client."
    move_down 10
    text "CLIENT : #{@client.name}"
    move_down 10
    text "GUEST: the Client’s guest staying at the Hotel."
    move_down 10
    text "HOTEL  : #{@hotel.name}"
    move_down 10
    text "SERVACE: ServAce Solutions Limited"
    move_down 10
    text "SYSTEM: ServAce Academy booking system"
    move_down 10
    text "Servace submits this Service Level Agreement between the Client and the Hotel to in connection with accommodation services required by the Client for its staff and wider dealership network and service partners attending technical training courses."
    move_down 5
    text "This document outlines the nature and method of support the Hotel shall provide to the Client and the Hotel  agrees to deliver the service levels outlined in this agreement."
    move_down 5
    text "ServAce shall enter separate agreements with the Client and the Hotel in relation to the services provided and use of the System and the three documents shall together form the full arrangement in place between the parties."
    move_down 20

  end

  def sla_hotel
    start_new_page
    @toc << ["2.0", "Service Level Agreement - Hotel", page_number]
    h1 "2.0 Service Level Agreement - Hotel"
    @toc << ["2.1", "Rooms / Rates Agreed", page_number]
    h2 "Rooms / Rates Agreed"
    h3 "Based on a per person per night rate inc VAT and Stripe booking fees for bookings made [		]/2025 to 30/06/26 (the Period) "
    table(get_room_data, :header => true, :position => :center, :cell_style => {:padding => 6, :inline_format => true}) do
      column(3).style :align => :right
    end
    move_down 10

    @toc << ["2.2", "Service Deliverables", page_number]
    h2 "2.2 Service Deliverables"
    text "To support the partnership agreement in place between the Client and Servace, the Hotel will deliver accommodation services on the following terms."
    move_down 10

    @toc << ["2.2.1", "Allocation", page_number]
    h2 "2.2.1 Allocation"
    text "a) The Hotel shall hold 8 rooms (the Allocation) each week from Monday to Thursday (inclusive) for the Period."
    move_down 5
    text "b) The Hotel may release the Allocation 14 days prior to check-in if the Allocation or any part thereof has not been booked."
    move_down 5
    text "c) Rooms booked within the Allocation and Block Release Period shall automatically be honoured by the Hotel."
    move_down 5
    text "d) Bookings made outside of the Allocation or after the Block release Period shall be subject to an availability check and the Hotel may accept or decline the booking."
    move_down 10

    @toc << ["2.2.2", "Block Release Period and Black Out Dates", page_number]
    h2 "2.2.2 Block Release Period and Black Out Dates"
    text "a) ServAce will load an initial training Calendar provided by the Client showing the provisional training requirements for the full calendar year to the System."
    move_down 5
    text "b) Any issues with availability must be highlighted to ServAce within one week of receipt of an amended calendar."
    move_down 5
    text "c) Any amendments or updates to the training Calendar will be communicated via the System and any issues must be highlighted by the Hotel within 48 hours of receipt."
    move_down 5
    text "d) The Hotel shall ensure that any Black Out dates are uploaded to the System as soon as possible. Bookings made prior to the System being updated by the Hotel will be honoured."
    move_down 5
    text "e) If for any reason space is not available when names are entered in the System due to a change in the calendar and additional availability not being checked, the Hotel shall email or contact ServAce on 0344 8223227 or <EMAIL>."
    move_down 5
    text "f) It is the Hotel’s responsibility to use the black out function in the System and ensure that availability is updated on a daily basis. This can be closed out 7 days prior if general room availability or room type is limited. This is only to be utilized to avoid late bookings and amendments being added to the System without authorization."
    move_down 5
    text "g) If this Black Out date is not updated and a booking is added to the system that the hotel is unable to accommodate, it is the Hotel’s responsibility to outbook an alternative guest and accommodate the Client’s guest at the agreed Client Rate."
    move_down 5
    text "h) For new bookings or amendments required during a blackout period, the ServAce team will contact the Hotel for authorisation before updating the System."
    move_down 10


    @toc << ["2.2.3", "Cancellations", page_number]
    h2 "2.2.2 Cancellations"
    text "a) For individual bookings the Hotel requires cancellation by 2 pm on the day of arrival."
    move_down 5
    text "b) Any refunds processed will be minus the Stripe booking fee (the Stripe Fee) paid at the time of booking."
    move_down 5
    text "c) Refunds will not be given once a guest has checked in to the Hotel."
    move_down 10

    @toc << ["2.2.4", "Cancellations due to Events outside of the Clients control", page_number]
    h2 "2.2.4 Cancellations due to Events outside of the Clients control"
    text "Where the Client cancels a booking due to circumstances outside of its control, including but not limited to acts of God, war,  terrorist attacks, fire, explosion or accidental damage, pandemics, extreme adverse weather conditions, labour disputes including strikes, industrial action or walk outs. It shall not be in breach of this agreement nor liable for any failure or delay in the performance of any  obligations under this SLA and the time for performance of the obligations shall be extended accordingly. "
    
    @toc << ["2.2.4", "Agreed Service Levels.", page_number]
    h2 "Agreed Service Levels"
    text "a) The hotel agrees to only allocate a minimum standard Double bed to any guest, for the duration of their stay."
    move_down 5
    text "b) Where a Guest arrives without a booking, the Hotel must call ServAce on the 24 hour booking line on 0344 8223227. ServAce shall seek to gain payment on the Hotels behalf for the booking."
    move_down 5
    text "c) Where a new booking is made via the System, the Hotel shall respond to the booking request within one hour of receipt."
    move_down 5
    text "d) Failure to meet the new booking response time set out in clause 2.2.5(c) on [3] occasions in a calendar month shall provide the Client with a right to terminate this Agreement with immediate effect in line with clause 4.3."
    move_down 5
    text "e) Any Hotel reward points earned on the bookings made under this agreement will be assigned to ID number and be held by the Client. It is the responsibility of the Client to ensure points are collected and attributed accordingly."
    move_down 10
    start_new_page

    @toc << ["3.0", "Payments", page_number]
    h1 "3.0 Payments"
    text "a) All payments for the accommodation booking must be made at the time of booking via the online booking link."
    move_down 5
    href="https://www.servace.co.uk/_files/ugd/865360_9c8cc1adde3b44f09bc7d7431b1b7e7d.pdf"

    text "b) All bookings will be subject to the terms and conditions of Academy Booking Service:"
    text '<color rgb="0000FF"><u><link href="https://www.servace.co.uk/_files/ugd/865360_9c8cc1adde3b44f09bc7d7431b1b7e7d.pdf">Terms And Conditions</link></u></color>', :inline_format => true

    move_down 5
    text "c) Guests must pay for any additional charges made whilst staying at the Hotel by cash or card before checkout directly to the Hotel."
    move_down 5
    text "d) All refunds will be issued minus the STRIPE booking fee of 2% plus VAT."
    move_down 10
    start_new_page

    @toc << ["4.0", "Term and Termination ", page_number]
    h1 "4.0 Term and Termination"
    text "This SLA will commence on the 01 July 2025 and Expire on the 30 June 2026. All costs, added value and details as defined above are to be fixed during this period."
    move_down 10
    text "a) In the event that the Client business requirements change then the Client retains the right to cancel this agreement giving 12 weeks' notice."
    move_down 5
    text "b) In addition to the above, should the Client not be satisfied with the service levels provided by the Hotel and can identify that the same issue has been raised on the System on three consecutive occasions, then the Client retains the right to cancel this agreement with immediate effect."
    move_down 5
    text "c) The Hotel will provide 12 weeks' notice to the Client and ServAce should they wish to withdraw from this contract."
    move_down 10
    start_new_page

    @toc << ["5.0", "Review", page_number]
    h1 "5.0 Review"
    text "This SLA will be reviewed every month by the Hotel and Servace along with feedback from the date the SLA commences. The Client  must regularly review the details supplied to the Hotel provided on the System in accordance with this SLA, including all contact details, estimates of room nights required. This should be done at least once every three months  and changes should be notified to Servace."
    start_new_page

    @toc << ["6.0", "Confidentiality and Data Protection", page_number]
    h1 "6.0 Confidentiality and Data Protection"
    text "The Hotel undertakes to:"
    move_down 5
    text "(a) only process any personal data on behalf of the Client in accordance with the terms of this Agreement except where otherwise required by applicable law;"
    move_down 5
    text "(b) observe and comply at all times with applicable Data Protection Laws, and to provide the Client with such assistance in complying with the same as the Client may reasonably expect of a diligent service provider;"
    move_down 5
    text "(c) implement technical and organisational measures to ensure the security of the personal data, and notify the Client without delay (and in any event within 36 hours) after becoming aware of a personal data breach;"
    move_down 5
    text "(d) not transfer any such personal data to any country or territory outside the UK or the European Economic Area (EEA) without the prior written consent of the Client;"
    move_down 5
    text "(e) ensure that: (i) the personal data is accessible only to personnel who need to have access to it in order to carry out their roles in the performance of the Hotels obligations under this Agreement; (ii) all such persons are subject to contractual obligations of confidentiality in respect of such personal data; and (iii) where relevant to their duties all such persons have been appropriately trained in the requirements of Data Protection Laws;"
    move_down 5
    text "(f) at the choice of the Client, delete or return all the personal data to the Client after the end of this Agreement or, if sooner, completion of the purpose for which such personal data was provided to the Hotel for processing and delete all copies held unless otherwise required by law."
    move_down 5
    start_new_page

  end

  def other_info

    start_new_page
    @toc << ["7.0", "Additional Information Provided by the Hotel", page_number]
    h1 "7.0 Additional Information Provided by the Hotel"

    info_table = []
    if @rfq_response.parking_complimentary?
      info_table << ["Complimentary Parking"]
    else
      info_table << ["Parking #{@rfq_response.parking_charge} per day"]
    end

    if @rfq_response.wifi_complimentary?
      info_table << ["Complimentary WiFi"]
    else
      if @rfq_response.wifi_discounted?
        info_table << ["Discounted WiFi #{number_to_currency(@rfq_response.wifi_discount_price, :unit => "£")} #{@rfq_response.wifi_discount_details}"]
      else
        info_table << ["No Complimentary WiFi"]
      end
    end

    table(info_table, :position => :center, :width => 500) do

    end

    move_down 20
    blackout_table = []
    blackout_dates = @rfq_response.rfq_proposed_hotel.hotel.hotel_blackout_dates.adult_blackout_dates
    if blackout_dates.any?
      h2 "Blackout Dates"
      blackout_table << ["From", "To"]
      blackout_dates.each do |bd|
        blackout_table << [bd.start_date.strftime("%d/%m/%Y"), bd.end_date.strftime("%d/%m/%Y")]
      end
    end
    table(blackout_table, :position => :center, :width => 500) do

    end
    move_down 20

    qanda_table = []
    @rfq_response.rfq_response_answers.each do |answer|
      question = answer.rfq_question
      if question.pre_yn?
        pre_yn =  answer.pre_y_n? ? "Yes" : "No"
      else
        pre_yn = ""
      end
      prompt =  answer.pre_y_n? ? question.prompt : ""

      qanda_table << [question.question_text, pre_yn, prompt,  what_output(answer, :text) ]

    end
    table(qanda_table, :position => :center, :width => 500 ) do
      column(0).style :width => 300
      column(1).style :width => 35
    end


  end


  def contact_details
    start_new_page
    h1 "2.#{@next_num} Contact Details"
    @toc << ["2.#{@next_num}","Contact Details", page_number]
    @next_num += 1
    h2 "Hotel"
    text "Accepted on behalf of the Hotel General Manager"
    move_down 5
    text  "*IMPORTANT", color: 'FF0000'
    move_down 5
    text "By clicking the acceptance button on the RFQ task you confirm that you accept the terms contained in this Service Level Agreement and you agree to be bound to the terms contained therein."

    move_down 20
    
    h2 "Client"
    text "Accepted on behalf of the Client"
    move_down 5
    text  "*IMPORTANT", color: 'FF0000'
    move_down 5
    text "By clicking the acceptance button on the RFQ task you confirm that you accept the terms contained in this Service Level Agreement and you agree to be bound to the terms contained therein."
  end



  def toc
    go_to_page 2
    move_down 200
    h1 "Table of contents"
    table @toc, {:cell_style => {:borders => {}}}
  end

  def footer
    repeat(:all, :dynamic => true) do
      bounding_box([0, 0], :width => bounds.width, :height => 35) do
        self.line_width = 0.5
        stroke_horizontal_line 0, bounds.width, :at => bounds.height - 5
        font_size 9
        move_down 10
        table([["\xC2\xA9".force_encoding('UTF-8') + " Servace", "Page <b>#{page_number}</b> of <b>#{page_count}  #{Date.today}</b>", "Commercial in Confidence"]],
              :width => bounds.width, :cell_style => {:borders => [], :inline_format => true, :font_size => 7}) do
          column(1).style :align => :center
          column(2).style :align => :right
        end
      end
    end
  end




  def h1(output_text)
    font_size H1_SIZE
    text output_text
    move_down 10
    font_size FONT_SIZE
  end

  def h2(output_text)
    font_size H2_SIZE
    text output_text
    move_down 10
    font_size FONT_SIZE
  end

  def h3(output_text)
    font_size H3_SIZE
    text output_text
    move_down 10
    font_size FONT_SIZE
  end

  def get_room_data
    tdata = [["<b>Room Type</b>", "<b>Package</b>", "<b>Price PPPN Inc VAT</b>"]]
    @rfq_response.rfq_response_rooms.each do |room|
      tdata << [
          room.room_type,
          room.pkg_type,
          room.cannot_supply? ? "cannot supply" : number_to_currency(room.price_inc_vat, :unit => "")]
    end
    tdata
  end






end