<template>
  <n-button size="small" type="primary" @click="showInviteContact = true">
    {{ props.already_created ? "Re-Invite" : "Invite" }} saved manager
  </n-button>

  <n-modal v-model:show="showInviteContact">
    <n-card
      style="width: 800px"
      :title="'Invite ' + props.learner.manager"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
    >
      <n-grid :span="24">
        <n-gi :span="24">
          <p>
            Are you sure you want to invite {{ props.learner.manager }} to the business
            unit dashboard?
          </p>
        </n-gi>
      </n-grid>

      <n-grid :span="24">
        <n-gi :span="24">
          <div style="display: flex; justify-content: flex-end">
            <n-button
              type="error"
              style="margin-right: 15px"
              @click="showInviteContact = false"
            >
              Cancel
            </n-button>
            <n-button type="primary" @click="inviteContact"> Invite </n-button>
          </div>
        </n-gi>
      </n-grid>
    </n-card>
  </n-modal>
</template>

<script setup>
import { ref } from "vue";
import axios from "axios";
import { useMessage } from "naive-ui";
const message = useMessage();

const props = defineProps({
  learner: Object,
  already_created: Boolean,
});

const showInviteContact = ref(false);

function inviteContact() {
  axios
    .post(window.location.href + "/invite_learners_manager")
    .then((resp) => {
      message.success("Invited successfully", { duration: 50000 });
      showInviteContact.value = false;
      location.reload();
    })
    .catch((err) => {
      if (err.response.data.errors) {
        let errors = err.response.data.errors;
        errors.forEach((error) => {
          message.error(error, { duration: 50000 });
        });
      } else {
        message.error("There was an error inviting the contact", { duration: 50000 });
      }
    });
}
</script>
