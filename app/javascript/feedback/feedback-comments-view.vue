<template>
  <n-tabs type="line" animated>
    <n-tab-pane name="comments" tab="Comments">
      <n-card size="medium">
        <n-spin :show="loading">
          <n-table :bordered="true" :single-line="false">
            <thead>
              <tr>
                <th style="width: 100px">Date</th>
                <th>Name</th>
                <th>Question</th>
                <th>You Said</th>
                <th v-if="isAdminOrTrainer">Trainer Comment</th>
                <th v-if="isAdmin">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="row in comments"
                :key="row.id"
                style="background-color: blue"
                :class="rowClassName(row)"
              >
                <td>{{ new Date(row.created_at).toLocaleDateString() }}</td>
                <td>{{ row.survey_response?.name || "Unknown" }}</td>
                <td>{{ row.question_title || "Unknown question" }}</td>
                <td>{{ row.comments || "No response" }}</td>
                <td v-if="isAdminOrTrainer">
                  {{ row.trainer_comment || "No trainer comment" }}
                </td>
                <td v-if="isAdmin">
                  <n-button @click="updateTrainerComment(row)" size="small">
                    Edit Comment
                  </n-button>
                </td>
              </tr>
            </tbody>
          </n-table>
          <n-empty
            v-if="!comments.length"
            description="No comments available"
          />
        </n-spin>
      </n-card>
    </n-tab-pane>
    <n-tab-pane name="analysis" tab="AI Analysis">
      <comments-analysis :id="id" :month="month" :year="year" type="hotel" />
    </n-tab-pane>
  </n-tabs>

  <n-modal v-model:show="showModal" preset="dialog" title="Add Trainer Comment">
    <n-form>
      <n-form-item label="Comment">
        <n-input
          v-model:value="trainerComment"
          type="textarea"
          placeholder="Enter your comment"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item>
    </n-form>
    <template #action>
      <n-space>
        <n-button @click="cancelCallback">Cancel</n-button>
        <n-button type="primary" @click="submitForm" :loading="loading">
          Submit
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, onMounted, h } from "vue";
import {
  NButton,
  NInput,
  NModal,
  NSpace,
  NSpin,
  NTable,
  NTabs,
  NTabPane,
  NForm,
  NFormItem,
} from "naive-ui";
import CommentsAnalysis from "./feedback-analysis.vue";
import axios from "axios";

const props = defineProps({
  id: {
    type: [String, Number],
    required: true,
  },
  month: {
    type: [String, Number],
    required: true,
  },
  year: {
    type: [String, Number],
    required: true,
  },
  currentUser: {
    type: Object,
    required: true,
  },
  isAdministrator: {
    type: Boolean,
    default: false,
  },
});

// Log props for debugging
console.log("View props:", props);

const loading = ref(false);
const comments = ref([]);
const showModal = ref(false);
const selectedComment = ref(null);
const trainerComment = ref("");

// Helper function to safely check if user is admin or trainer
// const isAdminOrTrainer = computed(() => {
//   return props.currentUser && (props.isAdministrator)
// })

// Helper function to safely check if user is admin
const isAdmin = computed(() => {
  return props.isAdministrator;
});

const columns = computed(() => {
  const baseColumns = [
    {
      title: "Date",
      key: "created_at",
      render(row) {
        return new Date(row.created_at).toLocaleDateString();
      },
    },
    {
      title: "Name",
      key: "name",
      render(row) {
        return row.survey_response?.name || "Unknown";
      },
    },
    {
      title: "Question",
      key: "question_title",
      render(row) {
        return row.question_title || "Unknown question";
      },
    },
    {
      title: "You Said",
      key: "comments",
      render(row) {
        return row.comments || "No response";
      },
    },
  ];

  if (isAdminOrTrainer.value) {
    baseColumns.push({
      title: "Trainer Comment",
      key: "trainer_comment",
      render(row) {
        return row.trainer_comment || "No trainer comment";
      },
    });
  }

  if (isAdmin.value) {
    baseColumns.push({
      title: "Actions",
      key: "actions",
      render(row) {
        return h(
          NButton,
          {
            onClick: () => updateTrainerComment(row),
          },
          { default: () => "Edit Comment" }
        );
      },
    });
  }

  return baseColumns;
});

const fetchComments = async () => {
  loading.value = true;
  try {
    console.log("Fetching comments for:", {
      id: props.id,
      month: props.month,
      year: props.year,
    });
    const response = await axios.get(
      `/feedback/feedback/${props.id}/feedback_comments_data`,
      {
        params: {
          month: props.month,
          year: props.year,
        },
      }
    );

    console.log("Fetched comments:", response.data.comments);
    comments.value = response.data.comments;
  } catch (error) {
    console.error("Error fetching comments:", error);
    window.$message.error("Failed to load comments");
  } finally {
    loading.value = false;
  }
};

const submitForm = async () => {
  if (!selectedComment.value || !trainerComment.value) return;

  loading.value = true;
  try {
    await axios.post(
      `/feedback/answers/${selectedComment.value.id}/add_trainer_comments`,
      {
        trainer_comment: trainerComment.value,
      }
    );
    window.$message.success("Comment saved successfully");
    showModal.value = false;
    await fetchComments(); // Refresh the comments
  } catch (error) {
    console.error("Error saving comment:", error);
    window.$message.error("Failed to save comment");
  } finally {
    loading.value = false;
  }
};

const cancelCallback = () => {
  showModal.value = false;
  selectedComment.value = null;
  trainerComment.value = "";
};

const updateTrainerComment = (row) => {
  showModal.value = true;
  selectedComment.value = row;
  trainerComment.value = row.trainer_comment || "";
};

const rowClassName = (row) => {
  console.log("Row data:", row);
  console.log("Chosen option:", row.chosen_option);
  const isRed = row.chosen_option === 4;
  console.log("Is red row:", isRed);
  return isRed ? "redrow" : "";
};

onMounted(() => {
  console.log("Component mounted, props:", props);
  console.log("Current user:", props.currentUser);
  fetchComments();
});
</script>

<style scoped>
.n-table {
  margin-top: 16px;
  width: 100%;
}

.redrow {
  background-color: red !important;
}

:deep(.n-table-td) {
  padding: 12px !important;
  vertical-align: top !important;
}

:deep(.n-table-th) {
  background-color: #f5f5f5 !important;
  font-weight: bold !important;
  padding: 12px !important;
}

:deep(.n-button) {
  margin: 0 !important;
}
</style>
