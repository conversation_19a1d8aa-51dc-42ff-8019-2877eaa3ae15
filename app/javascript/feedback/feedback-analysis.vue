<template>
    <n-card size="medium" title="Comments Analysis" ref="commentsAnalysis">

        <div v-if="loading" class="loading-container">
            <n-spin size="large" />
            <p class="loading-text">Our AI is Analyzing the Feedback Data...</p>
            <p class="loading-text">This can take a little while</p>
        </div>

        <div v-else-if="error">
            <n-alert type="error" :title="error" />
        </div>
        <div v-else>
            <div v-if="analysisData && analysisData[0]">
                <!-- Overall Summary Section -->
                <n-card title="Overall Summary" class="mb-4">
                    <n-space vertical>
                        <n-alert :type="getQualityType(analysisData[0].overall_summary.overall_quality)">
                            Overall Quality: {{ analysisData[0].overall_summary.overall_quality }}
                        </n-alert>

                        <div class="section">
                            <h3>Key Themes</h3>
                            <div class="tag-container">
                                <n-tag v-for="theme in analysisData[0].overall_summary.key_themes" 
                                      :key="theme" 
                                      type="info"
                                      class="tag-wrapper">
                                    <span class="tag-content">{{ theme }}</span>
                                </n-tag>
                            </div>
                        </div>

                        <div class="section" v-if="analysisData[0].overall_summary.major_red_flags.length">
                            <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                <h3 style="margin: 0; margin-right: 1rem;">Major Flags</h3>
                                <n-space size="small">
                                    <n-tag type="error" size="tiny">High Incidence >10%</n-tag>
                                    <n-tag type="warning" size="tiny">Low Incidence <10%</n-tag>
                                </n-space>
                            </div>
                            <div class="tag-container">
                                <n-tag v-for="flag in analysisData[0].overall_summary.major_red_flags" 
                                      :key="flag"
                                      :type="getRedFlagType(flag)"
                                      class="tag-wrapper">
                                    <span class="tag-content">{{ flag }}</span>
                                </n-tag>
                            </div>
                        </div>

                        <div class="section">
                            <h3>Areas for Improvement</h3>
                            <div class="tag-container">
                                <n-tag v-for="area in analysisData[0].overall_summary.areas_for_improvement" 
                                      :key="area"
                                      type="warning"
                                      class="tag-wrapper">
                                    <span class="tag-content">{{ area }}</span>
                                </n-tag>
                            </div>
                        </div>
                    </n-space>
                </n-card>

                <!-- Questions Analysis Section -->
                <n-card title="Detailed Analysis by Question" class="mb-4">
                    
                    <!-- Use data table for regular feedback, collapse for training feedback -->
                    <n-data-table v-if="displayMode === 'table'" 
                        :columns="questionColumns" 
                        :data="questionData" 
                        :pagination="false" 
                        :bordered="true"
                        :single-line="false" />
                    
                    <n-collapse v-else>
                        <n-collapse-item v-for="(analysis, question) in analysisData[0].questions" 
                                       :key="question" 
                                       :title="question">
                            <n-space vertical>
                                <n-alert :type="getQualityType(analysis.overall_quality)">
                                    Quality Rating: {{ analysis.overall_quality }}
                                </n-alert>

                                <div class="section">
                                    <h4>Key Themes</h4>
                                    <n-tag v-for="theme in analysis.key_themes" 
                                          :key="theme" 
                                          type="info" 
                                          class="mr-2 mb-2">
                                        {{ theme }}
                                    </n-tag>
                                </div>

                                <div class="section" v-if="analysis.major_red_flags.length">
                                    <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                        <h4 style="margin: 0; margin-right: 0.5rem;">Major Flags</h4>
                                        <n-space size="small">
                                            <n-tag type="error" size="tiny">High >10%</n-tag>
                                            <n-tag type="warning" size="tiny">Low <10%</n-tag>
                                        </n-space>
                                    </div>
                                    <n-tag v-for="flag in analysis.major_red_flags" 
                                          :key="flag" 
                                          :type="getRedFlagType(flag)" 
                                          class="mr-2 mb-2">
                                        {{ flag }}
                                    </n-tag>
                                </div>

                                <div class="section">
                                    <h4>Areas for Improvement</h4>
                                    <n-tag v-for="area in analysis.areas_for_improvement" 
                                          :key="area" 
                                          type="warning" 
                                          class="mr-2 mb-2">
                                        {{ area }}
                                    </n-tag>
                                </div>
                            </n-space>
                        </n-collapse-item>
                    </n-collapse>
                </n-card>
            </div>
        </div>

        <!-- <pre>{{ analysisData }}</pre> -->
    </n-card>

    <n-space justify="end">
        <n-button v-if="showRefresh" 
            data-cy="refreshData" 
            @click="refreshData" 
            type="primary" 
            style="margin-top: 10px; margin-right: 10px;" 
            :loading="refreshing">
            <template #icon><n-icon><RefreshIcon /></n-icon></template>
            Refresh Analysis
        </n-button>
        <n-button data-cy="exportToCSV" @click="exportToCsv" type="info" style="margin-top: 10px;">
            Export To CSV
        </n-button>
    </n-space>
</template>

<script setup>
import axios from 'axios';
import { computed, ref, onMounted, onUnmounted, shallowRef, useTemplateRef, h } from 'vue'
import { useMessage } from 'naive-ui'
import { useIntersectionObserver } from '@vueuse/core'
import { NTag, NDataTable, NThing, NIcon } from 'naive-ui'
import RefreshIcon from '../icons/RefreshIcon.vue'

const message = useMessage()

const props = defineProps({ 
    id: Number, 
    month: Number, 
    year: Number, 
    type: [String, Number],
    displayMode: {
        type: String,
        default: 'table', // 'table' or 'collapse'
        validator: (value) => ['table', 'collapse'].includes(value)
    },
    showRefresh: {
        type: Boolean,
        default: true
    },
    autoLoad: {
        type: Boolean,
        default: true
    }
})

const dataRows = ref([]);
const loaded = ref(false)
const loading = ref(false)
const refreshing = ref(false)
const error = ref(null)
const analysisData = ref(null)
const pollInterval = ref(null)

const target = useTemplateRef('commentsAnalysis')
const targetIsVisible = shallowRef(false)

const { stop } = useIntersectionObserver(
    target,
    ([entry], observerElement) => {
        targetIsVisible.value = entry?.isIntersecting || false
        if (!pollInterval.value && targetIsVisible.value && props.showRefresh) {
            startPolling()
        }
    },
)

function exportToCsv() {
    if (!analysisData.value || !analysisData.value[0]) {
        message.error('No data available to export');
        return;
    }

    const data = analysisData.value[0];

    // Prepare CSV content
    let csvContent = "Question,Quality Rating,Key Themes,Red Flags,Areas for Improvement\n";

    // Add overall summary
    csvContent += "\nOverall Summary\n";
    csvContent += `Overall Quality,${data.overall_summary.overall_quality}\n`;
    csvContent += `Key Themes,${data.overall_summary.key_themes.join('; ')}\n`;
    csvContent += `Major Red Flags,${data.overall_summary.major_red_flags.join('; ')}\n`;
    csvContent += `Areas for Improvement,${data.overall_summary.areas_for_improvement.join('; ')}\n`;

    // Add questions data
    csvContent += "\nDetailed Analysis by Question\n";
    Object.entries(data.questions).forEach(([question, analysis]) => {
        const row = [
            `"${question.replace(/"/g, '""')}"`,
            analysis.overall_quality,
            `"${analysis.key_themes.join('; ')}"`,
            `"${analysis.major_red_flags.join('; ')}"`,
            `"${analysis.areas_for_improvement.join('; ')}"`
        ];
        csvContent += row.join(',') + '\n';
    });

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `feedback_analysis_${props.id}_${props.month}_${props.year}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('CSV file downloaded successfully');
}

async function pollAnalysis() {
    try {
        const endpoint = props.showRefresh ? 
            `/feedback/ai_analysis/${props.id}/poll` : 
            `/feedback/ai_analysis/${props.id}`;
            
        const response = await axios.get(endpoint, {
            params: {
                month: props.month,
                year: props.year,
                type: props.type
            }
        });

        if (response.data.status === 'processing') {
            // Continue polling
            return;
        }

        if (response.data.error) {
            error.value = response.data.error;
            loading.value = false;
            stopPolling();
            return;
        }

        // Analysis is complete
        analysisData.value = response.data;
        loading.value = false;
        stopPolling();
    } catch (err) {
        error.value = "Error analyzing feedback data";
        loading.value = false;
        stopPolling();
    }
}

async function loadAnalysis() {
    try {
        loading.value = true;
        error.value = null;

        const response = await axios.get(`/feedback/ai_analysis/${props.id}`, {
            params: {
                month: props.month,
                year: props.year,
                type: props.type
            }
        });

        if (response.data.status === 'processing') {
            // Start polling if data is still being generated
            startPolling();
            return;
        }

        if (response.data.error) {
            error.value = response.data.error;
            loading.value = false;
            return;
        }

        // Data exists and is complete
        analysisData.value = response.data;
        loading.value = false;
    } catch (err) {
        error.value = "Error loading feedback analysis";
        loading.value = false;
    }
}

function startPolling() {
    loading.value = true;
    error.value = null;
    // Poll every 2-3 seconds
    const pollDelay = props.showRefresh ? 3000 : 2000;
    pollInterval.value = setInterval(pollAnalysis, pollDelay);
}

function stopPolling() {
    if (pollInterval.value) {
        clearInterval(pollInterval.value);
        pollInterval.value = null;
    }
}

async function refreshData() {
    try {
        refreshing.value = true;
        error.value = null;

        const response = await axios.get(`/feedback/ai_analysis/${props.id}/refresh`, {
            params: {
                month: props.month,
                year: props.year,
                type: props.type
            }
        });

        if (response.data.status === 'processing') {
            message.success('Analysis refresh started. This may take a few moments...');
            // Clear existing data and show loading state
            analysisData.value = null;
            loading.value = true;
            startPolling();
        } else if (response.data.error) {
            error.value = response.data.error;
            message.error(response.data.error);
        }
    } catch (err) {
        error.value = "Error refreshing analysis";
        message.error("Failed to refresh analysis");
    } finally {
        refreshing.value = false;
    }
}

onMounted(() => {
    if (props.autoLoad) {
        if (props.showRefresh) {
            loadAnalysis();
        } else {
            startPolling();
        }
    }
});

onUnmounted(() => {
    stopPolling();
});

function getQualityType(quality) {
    switch (quality.toLowerCase()) {
        case 'good':
            return 'success';
        case 'requires improvement':
            return 'warning';
        case 'inadequate':
            return 'error';
        default:
            return 'info';
    }
}

function getRedFlagType(flag) {
    // Check if this is a low incidence issue (less than 10%)
    const incidenceMatch = flag.match(/(\d+(?:\.\d+)?%)/);
    if (incidenceMatch) {
        const percentage = parseFloat(incidenceMatch[1]);
        if (percentage < 10) {
            return 'warning'; // Use warning (orange) for low incidence
        }
    }
    return 'error'; // Use error (red) for higher incidence or no percentage
}

const questionColumns = [
    {
        title: 'Question',
        key: 'question',
        width: 300,
    },
    {
        title: 'Most Prevalent Quality Rating',
        key: 'quality',
        width: 150,
        render: (row) => {
            return h(NTag, {
                type: getQualityType(row.quality),
                class: 'tag-wrapper'
            }, { default: () => h('span', { class: 'tag-content' }, row.quality) })
        }
    },
    {
        title: 'Key Themes',
        key: 'themes',
        width: 200,
        render: (row) => {
            return h('div', { class: 'tag-container' }, row.themes.map(theme =>
                h(NTag, {
                    type: 'info',
                    class: 'tag-wrapper'
                }, { default: () => h('span', { class: 'tag-content' }, theme) })
            ))
        }
    },
    {
        title: () => h('div', [
            h('span', 'Flags'),
            h('div', { style: 'font-size: 11px; font-weight: normal; margin-top: 4px;' }, [
                h(NTag, { type: 'error', size: 'tiny', style: 'margin-right: 4px;' }, { default: () => 'High Incidence >10%' }),
                h(NTag, { type: 'warning', size: 'tiny' }, { default: () => 'Low Incidence <10%' })
            ])
        ]),
        key: 'redFlags',
        width: 200,
        render: (row) => {
            return h('div', { class: 'tag-container' }, row.redFlags.map(flag =>
                h(NTag, {
                    type: getRedFlagType(flag),
                    class: 'tag-wrapper'
                }, { default: () => h('span', { class: 'tag-content' }, flag) })
            ))
        }
    },
    {
        title: 'Areas for Improvement',
        key: 'improvements',
        width: 200,
        render: (row) => {
            return h('div', { class: 'tag-container' }, row.improvements.map(area =>
                h(NTag, {
                    type: 'warning',
                    class: 'tag-wrapper'
                }, { default: () => h('span', { class: 'tag-content' }, area) })
            ))
        }
    }
]

const questionData = computed(() => {
    if (!analysisData.value || !analysisData.value[0]) return []

    return Object.entries(analysisData.value[0].questions).map(([question, data]) => ({
        key: question,
        question,
        quality: data.overall_quality,
        themes: data.key_themes || [],
        redFlags: data.major_red_flags || [],
        improvements: data.areas_for_improvement || []
    }))
})

</script>

<style scoped>

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.loading-text {
    margin-top: 0.5rem;
}

.n-table td.redrow {
    background-color: #f9acac;
}

.section {
    margin-bottom: 1rem;
}

.section h3,
.section h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}
.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.tag-wrapper {
    max-width: 100%;
    height: auto !important;
    white-space: normal !important;
    padding: 0.5rem 0.5rem 0.5rem 0.5rem; /* Increased padding */
}

.tag-content {
    display: inline-block;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
}

:deep(.n-tag) {
    height: auto !important;
    min-height: 2rem;
}

:deep(.n-data-table .n-tag) {
    margin: 0.125rem 0;
}

:deep(.n-data-table-td) {
    vertical-align: top;
    padding: 0.75rem !important;
}

.n-data-table :deep(.n-tag__content) {
    white-space: normal;
    word-wrap: break-word;
}

</style>