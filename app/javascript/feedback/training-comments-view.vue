<template>
  <n-tabs type="line" animated>
    <n-tab-pane name="comments" tab="Training Comments">
      <n-space vertical>
        <n-card v-if="trainers.length" size="medium">
          <n-grid x-gap="12" :cols="2">
            <n-gi v-if="trainers.length">
              <n-form-item label="Filter by Trainer(s)">
                <n-select
                  data-cy="filterByTrainer"
                  v-model:value="selectedTrainers"
                  label-field="name"
                  value-field="ids"
                  multiple
                  :options="trainers"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item
                v-if="show_rfq_trainers"
                label="Filter by RFQ Trainer(s)"
              >
                <n-select
                  data-cy="filterByRFQTrainer"
                  v-model:value="selectedRFQTrainers"
                  label-field="name"
                  value-field="id"
                  multiple
                  :options="rfq_trainers"
                />
              </n-form-item>
            </n-gi>
          </n-grid>

          <n-grid x-gap="12" :cols="2">
            <n-gi> </n-gi>

            <n-gi>
              <div style="display: flex; justify-content: flex-end">
                <n-button
                  data-cy="filterCommentsBtn"
                  round
                  type="primary"
                  @click="getData"
                >
                  Filter Comments
                </n-button>
              </div>
            </n-gi>
          </n-grid>
        </n-card>
      </n-space>
      <n-space vertical>
        <div id="pdfcontainer">
          <n-card
            data-cy="traineeCommentsTable"
            title="Trainee Comments"
            size="large"
          >
            <n-table :bordered="true" :single-line="false">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Trainer</th>
                  <th v-if="show_rfq_trainers">RFQ Trainer</th>
                  <th>Name</th>
                  <th>You Said</th>
                  <th>We Did</th>
                  <th>Reviewed By</th>
                  <th style="text-align: center">
                    Include on PDF
                    <p style="margin: 0">
                      <n-button
                        tertiary
                        :type="selectAllComments ? 'error' : 'success'"
                        @click="allComments"
                        style="font-size: 22px"
                        size="tiny"
                      >
                        {{ selectAllComments ? "-" : "+" }}
                      </n-button>
                    </p>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(row, idx) in dataRows">
                  <td :class="rowColor(row)">{{ row.date }}</td>
                  <td :class="rowColor(row)">{{ row.trainer }}</td>
                  <td v-if="show_rfq_trainers" :class="rowColor(row)">
                    {{ row.rfq_trainer }}
                  </td>
                  <td :class="rowColor(row)" v-html="row.name"></td>
                  <td :class="rowColor(row)">
                    <p>
                      <strong>{{ row.question_title }}</strong>
                    </p>
                    <p>{{ row.comments_text }}</p>
                  </td>
                  <td :class="rowColor(row)">
                    {{ row.trainer_comment }}
                    <p>
                      <n-button
                        @click="updateTrainerComment(row)"
                        text
                        style="font-size: 24px"
                      >
                        <n-icon :component="CreateIcon"> </n-icon>
                      </n-button>
                    </p>
                  </td>
                  <td :class="rowColor(row)">
                    <p>{{ row.trainer_comment_user }}</p>
                    <p>{{ row.trainer_comment_date }}</p>
                  </td>
                  <td style="text-align: center">
                    <n-checkbox
                      :checked-value="row.id"
                      :unchecked-value="null"
                      v-model:checked="selectedComments[idx]"
                    ></n-checkbox>
                  </td>
                </tr>
              </tbody>
            </n-table>
          </n-card>
        </div>
      </n-space>

      <n-space justify="end">
        <n-button
          data-cy="exportCommentsToPdf"
          @click="exportToPdf"
          type="info"
          style="margin-top: 10px"
        >
          Export To PDF
        </n-button>
      </n-space>

      <n-modal
        data-cy="addComment"
        v-model:show="showModal"
        preset="dialog"
        title="Add Comment"
        content="Are you sure?"
        positive-text="Submit"
        negative-text="Cancel"
        @positive-click="submitCallback"
        @negative-click="cancelCallback"
      >
        <n-input
          v-model:value="selectedComment.trainer_comment"
          placeholder="Add Comment"
        />
      </n-modal>
    </n-tab-pane>
    <n-tab-pane v-if="isAdmin && isVisible" name="analysis" tab="AI Analysis">
      <comments-analysis :id="id" :month="month" :year="year" type="trainee" />
    </n-tab-pane>
  </n-tabs>
</template>

<script setup>
import axios from "axios";
import { computed, ref } from "vue";
import { useMessage } from "naive-ui";
import CommentsAnalysis from "./feedback-analysis.vue";
import CreateIcon from "@vicons/material/CreateRound";

const message = useMessage();

const props = defineProps({
  id: Number,
  month: Number,
  year: Number,
  isAdmin: Boolean,
  isVisible: Boolean,
});

const surveys = ref([]);

const trainers = ref([]);
const show_rfq_trainers = ref(false);
const rfq_trainers = ref([]);
const selectedRFQTrainers = ref([]);
const selectedTrainers = ref([]);
const selectedComments = ref([]);
const selectAllComments = ref(false);

const trainersLoaded = ref(false);

const showModal = ref(false);
const selectedComment = ref({ trainer_comment: "" });

function updateTrainerComment(row) {
  showModal.value = true;
  selectedComment.value = JSON.parse(JSON.stringify(row));
  console.log(row);
}

function allComments() {
  selectAllComments.value = !selectAllComments.value;

  var value = selectAllComments.value;

  if (value) {
    selectedComments.value = dataRows.value.map((n) => (value ? n.id : null));
  } else {
    selectedComments.value.forEach((n, index) => {
      selectedComments.value[index] = null;
    });
  }
}

function exportToPdf() {
  var url =
    window.location.origin +
    "/feedback/feedback/" +
    props.id +
    "/training_comments_tab/?year=" +
    props.year +
    "&month=" +
    props.month;

  if (selectedComments.value.length) {
    if (selectedComments.value.filter((n) => n).length) {
      url =
        url +
        "&" +
        selectedComments.value
          .filter((n) => n)
          .map((n, index) => `comments[${index}]=${n}`)
          .join("&");
    }
  }

  if (selectedTrainers.value.length) {
    url =
      url +
      "&" +
      selectedTrainers.value
        .map((n, index) => `trainers[${index}]=${n}`)
        .join("&");
  }

  if (selectedRFQTrainers.value.length) {
    url =
      url +
      "&" +
      selectedRFQTrainers.value
        .map((n, index) => `rfq_trainers[${index}]=${n}`)
        .join("&");
  }

  window.open(url, "_blank");
}

function submitCallback(stuff) {
  axios
    .post(
      "/feedback/answers/" +
        selectedComment.value.id +
        "/add_trainer_comments/",
      {
        trainer_comment: selectedComment.value.trainer_comment,
      }
    )
    .then((resp) => {
      message.success("Comment updated");
      showModal.value = false;
      getData();
    })
    .catch((err) => {
      message.error("Error updating comment");
    });
}

function cancelCallback() {
  showModal.value = false;
  selectedComment.value = { trainee_comment: "" };
}

function rowColor(row) {
  var rowclass = row.chosen_option == 4 ? "redrow" : "";
  return rowclass;
}

const dataRows = ref([]);
const loaded = ref(false);

function loadData() {
  getData();
}

getData();

function getData() {
  loaded.value = false;
  var url =
    "/feedback/feedback/" +
    props.id +
    "/training_comments_data/?year=" +
    props.year +
    "&month=" +
    props.month;

  if (selectedTrainers.value.length) {
    url =
      url +
      "&" +
      selectedTrainers.value
        .map((n, index) => `trainers[${index}]=${n}`)
        .join("&");
  }

  if (selectedRFQTrainers.value.length) {
    url =
      url +
      "&" +
      selectedRFQTrainers.value
        .map((n, index) => `rfq_trainers[${index}]=${n}`)
        .join("&");
  }

  axios
    .get(url)
    .then((resp) => {
      dataRows.value = resp.data.comment_rows;

      // We only want this to load once
      if (!trainersLoaded.value) {
        trainers.value = resp.data.trainers;
        rfq_trainers.value = resp.data.rfq_trainers;
        show_rfq_trainers.value = resp.data.show_rfq_trainers;
      }

      trainersLoaded.value = true;

      loaded.value = true;
    })
    .catch((err) => {
      message.error("No data available");
    });
}
</script>

<style scoped>
.n-table td.redrow {
  background-color: #f9acac;
}
</style>
