<template>
  <n-config-provider>
    <n-dialog-provider>
      <n-message-provider>
        <comments-analysis :id="id" :month="month" :year="year" type="hotel" />
        <!-- <feedback-comments-view
          :id="id"
          :month="month"
          :year="year"
          :is-hotel="isHotel"
          :current-user="currentUser"
          :is-administrator="isAdministrator"
          :is-self-managed="isSelfManaged"
          :is-own-hotel="isOwnHotel"
          :is-prizable="isPrizable"
        /> -->
      </n-message-provider>
    </n-dialog-provider>
  </n-config-provider>
</template>

<script setup>
import { NConfigProvider, NDialogProvider, NMessageProvider } from "naive-ui";
// import FeedbackCommentsView from './feedback-comments-view.vue'
import commentsAnalysis from "./feedback-analysis.vue";

const props = defineProps({
  id: Number,
  month: Number,
  year: Number,
  isAdmin: Boolean,
  // isHotel: <PERSON><PERSON><PERSON>,
  // isAdministrator: Boolean,
  // isSelfManaged: <PERSON>olean,
  // isOwnHotel: Boolean,
  // isPrizable: Boolean,
  // currentUser: {
  //   type: Object,
  //   required: true
  // }
});
</script>
