<template>
  <n-space vertical>
    <n-card
      title="Choose Other Surveys To Merge Results With This One / Filter By JI"
      size="medium"
    >
      <n-form
        data-cy="mergeSurveyResponsesTable"
        ref="formRef"
        :model="formModel"
      >
        <n-grid x-gap="12" :cols="2">
          <n-gi>
            <n-form-item label="Choose Additional Surveys">
              <n-select
                data-cy="selectSurveys"
                v-model:value="selectedSurveys"
                multiple
                :options="surveys"
                clearable
                @update:value="updateFilters"
              />
            </n-form-item>
          </n-gi>

          <n-gi>
            <n-form-item label="Filter By JIs">
              <n-select
                data-cy="filterByJIs"
                v-model:value="selectedJIs"
                multiple
                :options="jis"
                label-field="title"
                value-field="id"
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>

        <n-grid x-gap="12" :cols="2">
          <n-gi v-if="trainers.length">
            <n-form-item label="Filter by Trainer(s)">
              <n-select
                data-cy="filterByTrainers"
                v-model:value="selectedTrainers"
                label-field="trainer_name"
                value-field="ids"
                multiple
                :options="trainers"
                clearable
              />
            </n-form-item>
          </n-gi>

          <n-gi>
            <n-form-item label="Filter by Group Code(s)">
              <n-select
                data-cy="filterByGroupCode"
                v-model:value="selectedGroupCodes"
                multiple
                :options="groupCodes"
                clearable
              />
            </n-form-item>
          </n-gi>

          <n-gi v-if="show_rfq_trainers">
            <n-form-item label="Filter by RFQ Trainer(s)">
              <n-select
                data-cy="filterByRFQTrainers"
                v-model:value="selectedRFQTrainers"
                label-field="name"
                value-field="id"
                multiple
                :options="rfq_trainers"
                clearable
              />
            </n-form-item>
          </n-gi>

          <n-gi>
            <n-form-item label="Filter by Question(s)">
              <n-select
                data-cy="filterByQuestions"
                v-model:value="selectedQuestions"
                label-field="name"
                value-field="ids"
                multiple
                :options="questions"
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>

        <n-grid x-gap="12" :cols="2">
          <n-gi>
            <n-form-item label="Filter by Dates (Booking Date)">
              <n-date-picker
                data-cy="filterByDates"
                value-format="yyyy-MM-dd"
                v-model:formatted-value="selectedDates"
                type="daterange"
                clearable
              />
            </n-form-item>
          </n-gi>

          <n-gi>
            <div style="display: flex; justify-content: flex-end">
              <n-button
                data-cy="mergeSurveysBtn"
                style="margin-top: 30px"
                round
                type="primary"
                @click="submitForm"
              >
                Merge Surveys / Filter
              </n-button>
            </div>
          </n-gi>
        </n-grid>
      </n-form>
    </n-card>
  </n-space>
</template>

<script setup>
import axios from "axios";
import { ref } from "vue";
import { useMessage } from "naive-ui";

import dayjs from "dayjs";

const message = useMessage();

const props = defineProps({ id: Number, month: Number, year: Number });

const emit = defineEmits(["change"]);

const startDate = new Date(props.year, props.month - 1, 1);
const beginingOfDatePeriod = dayjs(startDate)
  .startOf("month")
  .format("YYYY-MM-DD");
const endOfDatePeriod = dayjs(startDate).endOf("month").format("YYYY-MM-DD");

const selectedSurveys = ref([]);
const selectedTrainers = ref([]);
const selectedGroupCodes = ref([]);
const selectedRFQTrainers = ref([]);
const selectedQuestions = ref([]);
const selectedDates = ref([beginingOfDatePeriod, endOfDatePeriod]);

const formRef = ref(null);
const formModel = ref(null);

const surveys = ref([]);
const jis = ref([]);
const trainers = ref([]);
const rfq_trainers = ref([]);
const show_rfq_trainers = ref(false);
const groupCodes = ref([]);

const questions = ref([]);

const selectedJIs = ref([]);

getSurveys();

function submitForm() {
  emit("change", [
    selectedSurveys.value,
    selectedJIs.value,
    selectedTrainers.value,
    selectedGroupCodes.value,
    selectedRFQTrainers.value,
    selectedQuestions.value.flat(),
    selectedDates.value,
  ]);
}

function updateFilters(surveysToAdd = []) {
  selectedTrainers.value = [];
  selectedGroupCodes.value = [];
  selectedRFQTrainers.value = [];
  getSurveys(surveysToAdd);
}

function getSurveys(surveysToAdd = []) {
  var url =
    "/feedback/training_results/" +
    props.id +
    "?year=" +
    props.year +
    "&month=" +
    props.month;

  if (surveysToAdd.length) {
    url =
      url +
      "&" +
      surveysToAdd.map((n, index) => `surveys[${index}]=${n}`).join("&");
  }

  axios
    .get(url)
    .then((resp) => {
      surveys.value = resp.data.surveys;
      jis.value = resp.data.jis;
      trainers.value = resp.data.trainers;
      rfq_trainers.value = resp.data.rfq_trainers;
      groupCodes.value = resp.data.group_codes;
      show_rfq_trainers.value = resp.data.show_rfq_trainers;
      questions.value = resp.data.questions;
    })
    .catch((err) => {
      message.error("No surveys available");
    });
}
</script>
