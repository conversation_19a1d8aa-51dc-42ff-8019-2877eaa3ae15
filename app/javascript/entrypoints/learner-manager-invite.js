import { createApp } from 'vue';
import axios from 'axios';
import LearnerManagerInviteWrapper from '../business_units/learner-manager-invite-wrapper.vue';

let token = document.getElementsByName('csrf-token')[0].getAttribute('content');
axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] = 'no-cache,no-store,must-revalidate,max-age=-1,private';

const app = createApp();

app.component('LearnerManagerInviteWrapper', LearnerManagerInviteWrapper);

app.mount('#learner-manager-invite');
