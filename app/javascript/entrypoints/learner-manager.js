import { createApp } from 'vue'
import { Quasar, Dialog, Notify, Loading } from 'quasar';

import axios from 'axios';

import quasarLang from 'quasar/lang/en-GB'

// Import icon libraries
import '@quasar/extras/material-icons/material-icons.css'

// Import Quasar css
import 'quasar/src/css/index.sass'

let token = document.getElementsByName('csrf-token')[0].getAttribute('content');
axios.defaults.headers.common['X-CSRF-Token'] = token;
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Cache-Control'] = 'no-cache,no-store,must-revalidate,max-age=-1,private';

import LearnerManagerManagement from '../learner-managers/LearnerManagerManagement.vue';

const myApp = createApp();

myApp.use(Quasar, {
  plugins: [Dialog, Notify, Loading],
  lang: quasarLang,
  boot: [],
  config: {
    brand: {
      // primary: '#004F59', Using lighter shade of primary color as original made darker by quassaar
      primary: '#006572',
      secondary: '#b0bec5',
      accent: '#8c9eff',
      dark: '#1a237e',
      positive: '#21BA45',
      negative: '#C10015',
      info: '#31CCEC',
      warning: '#F2C037',
      // 'typography-font-family': 'Montserrat'
    }
  }
})

myApp.component('learner-manager-management', LearnerManagerManagement)

myApp.mount('#learner-manager')