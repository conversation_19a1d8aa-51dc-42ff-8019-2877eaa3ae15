<template>
    <q-layout view="hHh lpR fFf">
        <q-page-container>
            <q-page padding>
               
                <div class="q-gutter-lg">
                    <div v-if="form.programmeName != null" class="text-h5 text-primary text-bold">
                        {{ form.programmeName }} 
                    </div>
                    <div v-else class="text-h5 text-primary text-bold">
                        Please select a programme
                    </div>
                    
                    <!-- <div class="text-h6 text-primary text-weight-bolder">
                        "Programme Name"
                        <q-separator color="primary"/> 
                    </div> -->
                </div>

                <div class="q-pt-lg">
                    <q-card bordered class="FilterCard">

                        <q-card-section>
                            <div class="text-h4 text-primary text-weight-bolder">
                                    <q-btn :ripple="false" push round dense icon="perm_identity" size="25px" color="positive"/>
                                    Learner Managers
                                    <!-- <q-separator style="width: 218px;" color="primary"/>  -->
                            </div>
                        </q-card-section>
                        
                        <q-card-section class="q-pb-lg">
                            <q-card style="width: 100%; border-radius: 10px;" flat bordered>
                                
                                <q-card-section>
                                    <div class="text-h6 text-primary text-weight-bolder">
                                        Filters
                                        <q-separator style="width: 60px;" color="primary"/> 
                                    </div>
                                </q-card-section>

                                <div class="q-pb-md">
                                        <q-form class="q-pb-md row">
                                            <div class="col-6">
                                                <q-card-section style="width: 100%;">
                                                    <q-select class="q-pb-sm" clearable outlined v-model="form.programmeName" label="Programme Name" :options="programmeName" />
                                                    <q-select class="q-pb-sm" clearable outlined v-model="form.buOptions" label="Business Unit" :options="buOptions" /> 
                                                    <q-select class="q-pb-sm" clearable outlined v-model="form.groupCode" label="Group Code" :options="groupCode"/>
                                                    <q-select class="q-pb-sm" clearable outlined v-model="form.leftOptions" label="Left?" :options="leftOptions"/>
                                                    <q-select class="q-pb-sm" clearable outlined v-model="form.inviteSent" label="Invite Sent" :options="inviteSent"/>
                                                    <q-select class="q-pb-sm" clearable outlined v-model="form.registeredCRM" label="Registered to CRM" :options="registeredCRM" />
                                                </q-card-section>
                                            </div>

                                            <div class="col-6">
                                                <q-card-section style="width: 100%;">
                                                    <q-input class="q-pb-sm" outlined v-model="text" label="Manager First Name" />
                                                    <q-input class="q-pb-sm" outlined v-model="text" label="Manager Last Name" />
                                                    <q-input class="q-pb-sm" outlined v-model="text" label="Manager Email" />
                                                    <q-input class="q-pb-sm" clearable outlined v-model="date" type="date" label="Check In" />
                                                    <q-input class="q-pb-sm" clearable outlined v-model="date" type="date" label="Check Out" />
                                                </q-card-section>
                                            </div>
                                        </q-form>
                                    <div class="row justify-end">    
                                        <q-card-section>
                                            <q-btn type="submit" :ripple="false" push color="positive"  label="Apply Filters"/>
                                        </q-card-section>
                                    </div>
                                </div>
                            </q-card>
                        </q-card-section>

                        <q-card-section class="row">
                            <q-btn v-for="letter in letters"
                            class="q-ma-xs" push outline color="primary" round :label="letter"/>
                        </q-card-section>                        

                        <q-card-section class="q-pb-lg">
                                <div v-if="form.programmeName != null" class="text-h6 text-primary text-bold">
                                    {{ form.programmeName }} 
                                </div>
                                <div v-else class="text-h6 text-primary text-bold">
                                    All Programmes
                                </div>
                            <q-card style="width: 100%; border-radius: 10px;" flat bordered>
                                <!-- <q-table
                                    class="my-sticky-header-table"
                                    flat bordered
                                    title="Learner Managers"
                                    :rows="rows"
                                    :columns="IndexColumns"
                                    row-key="name"
                                /> -->
                                <q-table class="index_Table" :rows="rows" :columns="IndexColumns" row-key="name" :hide-pagination="true" wrap-cells :loading="isLoading" :rows-per-page-options="[0]" flat bordered separator="cell">
                                    <template v-slot:header="props">
                                        <q-tr>
                                            <q-th v-for = "col in props.cols">
                                                <div style="display: flex; align-items: center;">
                                                    {{ col.label }}
                                                </div>
                                            </q-th>
                                        </q-tr>
                                    </template>
                                </q-table>
                            </q-card>
                        </q-card-section>

                        <!-- <q-card-section class="q-pb-lg">
                            <div class="q-pa-md" v-if="pagination.count > 20">
                                <q-pagination v-model="pagination.page" :max="pagination.pages" :max-pages="10"
                                    @update:model-value="getBookings" direction-links boundary-numbers flat color="grey"
                                    active-color="primary" />
                            </div>
                        </q-card-section> -->

                    </q-card> 
                </div>

            </q-page>
        </q-page-container>
    </q-layout>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { columns } from 'element-plus/es/components/table-v2/src/common.mjs';

const formBaseValues = {
    programmeName: null,
    buOptions: null,
    groupCode: null,
    leftOptions: null,
    inviteSent: null,
    registeredCRM: null,
};

const form = ref(formBaseValues);

const buOptions = ["Arnold Clark Carlisle Ford", "AC Wakefield", "For more Business Units, pay for premium"];

const groupCode = ["357C7","75360🥳", "For more group codes, pay for premium"];

const programmeName = ["Remit", "SerVace", "For more programmes, pay for premium"];

const inviteSent = ["Yes", "No"];

const leftOptions = ["Yes", "No"];

const registeredCRM = ["Yes", "No"];

const IndexColumns= [
    { name: 'ref', label: 'Reference',  field: 'reference'},
    { name: 'BU', label: 'Business Unit',  field: 'business_unit'},
    { name: 'M_name', label: 'Learner Manager Full Name',  field: 'manager_name'},
    { name: 'M_email', label: 'Learner Manager Email',  field: 'manager_email'},
    { name: 'left', label: 'Left',  field: 'left'},
    { name: 'CRM', label: 'Registered to CRM',  field: 'crm'},
    { name: 'Invite', label: 'Invite Sent',  field: 'invite_sent'},
    { name: 'Actions', label: 'Actions',  field: 'actions'},    
]

const rows = [{
    reference:  'Reference',
    business_unit: 'Business Unit',
    manager_name: 'Manager Name',
    manager_email: 'Manager Email',
    left: 'Yes',
    crm: 'Yes',
    invite_sent: 'Yes',
    actions: 'Actions'
},
]

const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']

</script>

<style scoped>
    .FilterCard {
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }

    .index_Table {
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }
</style>