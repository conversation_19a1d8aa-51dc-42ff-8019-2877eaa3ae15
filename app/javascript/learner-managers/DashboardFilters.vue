<template>
  <div class="filters-container">
    <!-- Date Range Filter -->
    <div class="date-range-container">
      <div class="date-range-input-group">
        <q-input
          v-model="localDateFilter.from"
          type="date"
          dense
          borderless
          class="date-input from-date"
          @update:model-value="onDateChange"
        />

        <div class="date-separator q-pa-sm bg-grey-3">
          <q-icon name="arrow_forward" size="16px" color="grey-9" />
        </div>

        <q-input
          v-model="localDateFilter.to"
          type="date"
          dense
          borderless
          class="date-input to-date"
          @update:model-value="onDateChange"
        />
      </div>

      <div class="date-quick-filters">
        <q-btn
          unelevated
          label="Last 3 months"
          color="white"
          text-color="black"
          @click="setLast3Months"
        >
          <q-tooltip>Set date range to last 3 months</q-tooltip>
        </q-btn>

        <q-btn
          unelevated
          label="Next 3 months"
          color="white"
          text-color="black"
          @click="setNext3Months"
        >
          <q-tooltip>Set date range to next 3 months</q-tooltip>
        </q-btn>
      </div>

      <q-btn
        flat
        round
        icon="refresh"
        color="grey-7"
        size="md"
        @click="resetDateFilter"
      >
        <q-tooltip>Reset dates</q-tooltip>
      </q-btn>
    </div>

    <div class="filters-container">
      <!-- Status Filter -->
      <q-btn-dropdown
        flat
        label="Filter"
        color="grey-7"
        class="status-filter-btn"
      >
        <q-list>
          <q-item clickable v-close-popup @click="onStatusChange('all')">
            <q-item-section>
              <q-item-label>All Bookings</q-item-label>
            </q-item-section>
            <q-item-section side v-if="localStatusFilter === 'all'">
              <q-icon name="check" color="primary" />
            </q-item-section>
          </q-item>
          <q-item clickable v-close-popup @click="onStatusChange('confirmed')">
            <q-item-section>
              <q-item-label>Confirmed Only</q-item-label>
            </q-item-section>
            <q-item-section side v-if="localStatusFilter === 'confirmed'">
              <q-icon name="check" color="primary" />
            </q-item-section>
          </q-item>
          <q-item
            clickable
            v-close-popup
            @click="onStatusChange('unconfirmed')"
          >
            <q-item-section>
              <q-item-label>Unconfirmed Only</q-item-label>
            </q-item-section>
            <q-item-section side v-if="localStatusFilter === 'unconfirmed'">
              <q-icon name="check" color="primary" />
            </q-item-section>
          </q-item>
        </q-list>
      </q-btn-dropdown>

      <!-- Search Input -->
      <q-input
        v-model="localSearchValue"
        filled
        placeholder="Search bookings..."
        dense
        bg-color="white"
        class="search-input"
        @update:model-value="onSearchChange"
      >
        <template v-slot:prepend>
          <q-icon name="search" color="grey-6" />
        </template>
        <template v-slot:append>
          <q-icon
            v-if="localSearchValue"
            name="clear"
            color="grey-6"
            class="cursor-pointer"
            @click="clearSearch"
          />
        </template>
      </q-input>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import dayjs from "dayjs";

// Props
const props = defineProps({
  searchValue: {
    type: String,
    default: "",
  },
  dateFilter: {
    type: Object,
    default: () => ({
      from: dayjs().startOf("week").format("YYYY-MM-DD"),
      to: dayjs().startOf("week").add(6, "week").format("YYYY-MM-DD"),
    }),
  },
  statusFilter: {
    type: String,
    default: "all",
  },
});

// Emits
const emit = defineEmits([
  "update:searchValue",
  "update:dateFilter",
  "update:statusFilter",
]);

// Local reactive data
const localSearchValue = ref(props.searchValue);
const localDateFilter = ref({ ...props.dateFilter });
const localStatusFilter = ref(props.statusFilter);

// Watch for prop changes
watch(
  () => props.searchValue,
  (newVal) => {
    localSearchValue.value = newVal;
  }
);

watch(
  () => props.dateFilter,
  (newVal) => {
    localDateFilter.value = { ...newVal };
  },
  { deep: true }
);

watch(
  () => props.statusFilter,
  (newVal) => {
    localStatusFilter.value = newVal;
  }
);

// Methods
function onSearchChange() {
  emit("update:searchValue", localSearchValue.value);
}

function onDateChange() {
  emit("update:dateFilter", { ...localDateFilter.value });
}

function onStatusChange(status) {
  localStatusFilter.value = status;
  emit("update:statusFilter", status);
}

function clearSearch() {
  localSearchValue.value = "";
  onSearchChange();
}

function resetDateFilter() {
  const defaultWeeksAhead = 6;
  localDateFilter.value = {
    from: dayjs().startOf("week").format("YYYY-MM-DD"),
    to: dayjs()
      .startOf("week")
      .add(defaultWeeksAhead, "week")
      .format("YYYY-MM-DD"),
  };
  onDateChange();
}

function setLast3Months() {
  localDateFilter.value = {
    from: dayjs().subtract(3, "month").format("YYYY-MM-DD"),
    to: dayjs().format("YYYY-MM-DD"),
  };
  onDateChange();
}

function setNext3Months() {
  localDateFilter.value = {
    from: dayjs().format("YYYY-MM-DD"),
    to: dayjs().add(3, "month").format("YYYY-MM-DD"),
  };
  onDateChange();
}
</script>

<style scoped>
.filters-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-quick-filters {
  display: flex;
  gap: 8px;
  align-items: center;
}

.date-range-input-group {
  display: flex;
  align-items: stretch;
  background: white;
  border: 1px solid #f2f2f2;
  overflow: hidden;

  min-height: 40px;
}

.date-input {
  padding-left: 5px;
  padding-right: 5px;
}

.from-date {
  border-right: 1px solid #f2f2f2;
}

.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  border-left: 1px solid #f2f2f2;
  border-right: 1px solid #f2f2f2;
  min-width: 40px;
  flex-shrink: 0;
}

.to-date {
  border-left: 1px solid #f2f2f2;
}

.search-input {
  min-width: 250px;
}

.status-filter-btn {
  min-width: 120px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .date-range-container {
    justify-content: center;
    flex-direction: column;
    gap: 8px;
  }

  .date-quick-filters {
    justify-content: center;
    flex-wrap: wrap;
  }

  .date-range-input-group {
    width: 100%;
    max-width: 400px;
  }

  .status-filter-btn {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .date-range-input-group {
    flex-direction: column;
    border-radius: 8px;
    align-items: stretch;
  }

  .date-input {
    min-width: 100%;
    border-right: none !important;
    border-left: none !important;
  }

  .date-separator {
    min-height: 32px;
    width: 100%;
  }

  .date-separator .q-icon {
    transform: rotate(90deg);
  }
}
</style>
