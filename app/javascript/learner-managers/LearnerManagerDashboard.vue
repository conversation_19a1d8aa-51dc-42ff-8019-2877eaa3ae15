<template>
  <div class="q-px-md bg-grey-2">
    <div class="row q-col-gutter-md q q-py-sm">
      <!-- Dashboard Filters Component -->
      <div class="col-12">
        <DashboardFilters
          :search-value="filterBarValue"
          :date-filter="dateFilter"
          :status-filter="statusFilter"
          @update:search-value="filterBarValue = $event"
          @update:date-filter="onDateFilterChange"
          @update:status-filter="statusFilter = $event"
        />
      </div>

      <!-- Charts -->
      <div class="col-md-6 col-xs-12">
        <q-card flat>
          <q-card-section horizontal class="q-pa-md">
            <div class="text-h5 text-weight-bold">Booking Types</div>
          </q-card-section>
          <div>
            <v-chart
              :option="doughnutOption1"
              autoresize
              style="height: 20rem"
              @legendselectchanged="handleLegendSelectChanged"
            />
          </div>
        </q-card>
      </div>
      <div class="col-md-6 col-xs-12">
        <q-card flat>
          <q-card-section horizontal class="q-pa-md">
            <div class="text-h5 text-weight-bold">Booking Statuses</div>
          </q-card-section>
          <div>
            <v-chart
              :option="doughnutOption2"
              autoresize
              style="height: 20rem"
              @legendselectchanged="handleLegendSelectChanged"
            />
          </div>
        </q-card>
      </div>

      <!-- Table -->
      <div class="col-12">
        <q-card flat>
          <q-card-section class="q-pa-none q-ma-none">
            <q-table
              flat
              data-cy="table"
              :rows="filteredRows"
              :columns="tableColumns"
              row-key="id"
              :filter="filterBarValue"
              dense
            >
              <template #body-cell-status="props">
                <q-td :props="props">
                  <q-chip
                    data-cy="status-chip"
                    :color="props.row.confirmed_flag ? 'green' : 'red'"
                    text-color="white"
                    dense
                    class="text-weight-bolder"
                    square
                    >{{
                      props.row.confirmed_flag ? "Confirmed" : "Unconfirmed"
                    }}</q-chip
                  >
                </q-td>
              </template>

              <template #body-cell-actions="props">
                <q-td :props="props">
                  <div v-if="!props.row.confirmed_flag" class="flex col">
                    <q-btn
                      data-cy="confirm-booking-btn"
                      :label="'Confirm'"
                      class="text-weight-bolder full-width bg-green"
                      text-color="white"
                      dense
                      target="_blank"
                      :href="props.row.confirm_url"
                    />
                  </div>
                  <div v-else class="flex col">
                    <q-btn
                      data-cy="summary-btn"
                      :label="'VAT Summary'"
                      class="text-weight-bolder full-width bg-pink q-mb-xs"
                      text-color="white"
                      dense
                      target="_blank"
                      :href="`/acc_bookings/${props.row.id}/vat_summary_preview`"
                    />
                  </div>
                  <div class="flex col">
                    <q-btn
                      data-cy="joining-instruction-btn"
                      :label="'Joining Instruction'"
                      class="text-weight-bolder full-width bg-blue"
                      text-color="white"
                      dense
                      target="_blank"
                      :href="`/joining_instructions/${props.row.joining_instruction_id}/preview`"
                    />
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import dayjs from "dayjs";
import { use } from "echarts/core";
import VChart from "vue-echarts";
import { CanvasRenderer } from "echarts/renderers";
import { PieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
} from "echarts/components";
import DashboardFilters from "./DashboardFilters.vue";

use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
]);

const filterBarValue = ref("");
const defaultWeeksAhead = 6;
const dateFilter = ref({
  from: dayjs().startOf("week").format("YYYY-MM-DD"),
  to: dayjs()
    .startOf("week")
    .add(defaultWeeksAhead, "week")
    .format("YYYY-MM-DD"),
});
const statusFilter = ref("all");

function onDateFilterChange(newDateFilter) {
  dateFilter.value = newDateFilter;
  loadDashboardData();
}

// --- Data for dashboard ---
const learners = ref([]);
const bookings = ref([]);

// --- Chart data computed from bookings ---
const bookingTypes = ["EPA", "Virtual", "Subsistence", "Residential"];
const bookingStatuses = ["Confirmed", "Unconfirmed", "Cancelled"];

const doughnutData1 = computed(() => {
  // Count bookings by type
  const counts = bookingTypes.map((type) => ({
    name: type,
    value: bookings.value.filter((b) => b.type === type).length,
  }));
  return counts;
});

const doughnutData2 = computed(() => {
  // Count bookings by status
  const counts = bookingStatuses.map((status) => ({
    name: status,
    value: bookings.value.filter((b) => b.status === status).length,
  }));
  return counts;
});

// Helper to compute total
function getTotal(data) {
  return data.reduce((sum, item) => sum + item.value, 0);
}

const legendSelected1 = computed(() => {
  const obj = {};
  doughnutData1.value.forEach((item) => {
    obj[item.name] = item.value > 0;
  });
  return obj;
});

const legendSelected2 = computed(() => {
  const obj = {};
  doughnutData2.value.forEach((item) => {
    obj[item.name] = item.value > 0;
  });
  return obj;
});

// Chart options using computed doughnutData1 and doughnutData2
const doughnutOption1 = computed(() => ({
  legend: {
    data: doughnutData1.value.map((item) => item.name),
    top: "5%",
    left: "center",
    icon: "circle",
    selected: legendSelected1.value,
    formatter: function (name) {
      const item = doughnutData1.value.find((i) => i.name === name);
      return item ? `${item.value} ${name}`.toUpperCase() : name;
    },
  },
  series: [
    {
      name: "Total",
      type: "pie",
      radius: 0,
      label: {
        position: "inner",
        fontSize: 35,
      },
      labelLine: { show: false },
      data: [
        {
          name: "Total",
          value: getTotal(doughnutData1.value),
          itemStyle: { color: "transparent" },
          label: {
            position: "center",
            fontSize: 20,
            formatter: (params) => `${params.value} Total`,
          },
        },
      ],
    },
    {
      type: "pie",
      radius: ["55%", "65%"],
      labelLine: { show: false },
      label: false,
      itemStyle: {
        borderRadius: 5,
        borderColor: "#fff",
        borderWidth: 1,
      },
      data: doughnutData1.value.map((item) => ({
        ...item,
        itemStyle: {
          color:
            item.value === 0
              ? "#e0e0e0"
              : item.name === "EPA"
              ? "#00B2F0"
              : item.name === "Virtual"
              ? "#FFCC00"
              : item.name === "Subsistence"
              ? "#D91470"
              : item.name === "Residential"
              ? "#004F59"
              : "#c10015",
        },
      })),
    },
  ],
}));

const doughnutOption2 = computed(() => ({
  legend: {
    data: doughnutData2.value.map((item) => item.name),
    top: "5%",
    left: "center",
    icon: "circle",
    selected: legendSelected2.value,
    formatter: function (name) {
      const item = doughnutData2.value.find((i) => i.name === name);
      return item ? `${item.value} ${name}`.toUpperCase() : name;
    },
  },
  series: [
    {
      name: "Total",
      type: "pie",
      radius: 0,
      label: {
        position: "inner",
        fontSize: 35,
      },
      labelLine: { show: false },
      data: [
        {
          name: "Total",
          value: getTotal(doughnutData2.value),
          itemStyle: { color: "transparent" },
          label: {
            position: "center",
            fontSize: 20,
            formatter: (params) => `${params.value} Total`,
          },
        },
      ],
    },
    {
      type: "pie",
      radius: ["55%", "65%"],
      labelLine: { show: false },
      label: false,
      itemStyle: {
        borderRadius: 5,
        borderColor: "#fff",
        borderWidth: 1,
      },
      data: doughnutData2.value.map((item) => ({
        ...item,
        itemStyle: {
          color:
            item.value === 0
              ? "#e0e0e0"
              : item.name === "Confirmed"
              ? "#65b22e"
              : item.name === "Unconfirmed"
              ? "#c10015"
              : item.name === "Cancelled"
              ? "#ffcc00"
              : "#0069b5",
        },
      })),
    },
  ],
}));

async function loadDashboardData() {
  try {
    const fromDate = dateFilter.value.from;
    const toDate = dateFilter.value.to;
    const url = `/learner_managers/data.json?date_from=${fromDate}&date_to=${toDate}`;

    const res = await fetch(url);
    if (!res.ok) throw new Error("Failed to load dashboard data");
    const data = await res.json();
    learners.value = data.learners || [];
    bookings.value = data.bookings || [];

    // Update date filter to reflect server response if needed
    if (data.date_range) {
      dateFilter.value.from = data.date_range.from;
      dateFilter.value.to = data.date_range.to;
    }
  } catch (e) {
    console.error("Error loading dashboard data:", e);
  }
}

onMounted(() => {
  loadDashboardData();
});

// --- Table columns and data ---
const tableColumns = [
  { name: "id", label: "ID", field: "id", align: "left" },
  { name: "learner", label: "Learner", field: "learner", align: "left" },
  { name: "programme", label: "Programme", field: "programme", align: "left" },
  { name: "type", label: "Type", field: "type", align: "left" },
  {
    name: "checkin",
    label: "Check-in",
    field: "first_checkin_formatted",
    align: "left",
  },
  {
    name: "checkout",
    label: "Check-out",
    field: "last_checkout_formatted",
    align: "left",
  },
  { name: "status", label: "Status", field: "status", align: "left" },
  { name: "actions", label: "Actions", field: "actions", align: "left" },
];

// Use bookings from API for table data
const tableData = computed(() => bookings.value);

const filteredRows = computed(() => {
  let rows = tableData.value;

  // Filter by legend selection
  if (
    selectedLegend.value.length &&
    selectedLegend.value.length !== doughnutData1.value.length
  ) {
    rows = rows.filter(
      (row) =>
        selectedLegend.value.includes(row.type) ||
        selectedLegend.value.includes(row.status)
    );
  }

  // Filter by status
  if (statusFilter.value !== "all") {
    if (statusFilter.value === "confirmed") {
      rows = rows.filter((row) => row.confirmed_flag);
    } else if (statusFilter.value === "unconfirmed") {
      rows = rows.filter((row) => !row.confirmed_flag);
    }
  }

  if (!filterBarValue.value) return rows;

  // Filter by search text
  const val = filterBarValue.value.toLowerCase();
  return rows.filter((row) =>
    Object.values(row).some((field) =>
      String(field).toLowerCase().includes(val)
    )
  );
});

const getBaseUrl = () => {
  // Adjust as needed for your environment
  return "";
};

function getInstructionType(row) {
  if (row.epa_booking) {
    return 1;
  } else if (row.virtual_flag) {
    return 2;
  } else {
    return 0;
  }
}

const selectedLegend = ref(bookingTypes); // all enabled by default

function handleLegendSelectChanged(event) {
  // event.selected is an object: { [name]: true/false }
  selectedLegend.value = Object.entries(event.selected)
    .filter(([name, selected]) => selected)
    .map(([name]) => name);
}
</script>

<style scoped>
/* Use full width, remove max-width */
</style>
