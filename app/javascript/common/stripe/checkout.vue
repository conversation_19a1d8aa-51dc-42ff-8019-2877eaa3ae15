<template>
    <!-- <form id="payment-form" style="width: 100%;"> -->
        <div id="payment-element" style="width: 100%;">
            <!--Stripe.js injects the Payment Element-->
        </div>
        <q-btn v-if="payment_intent" :disabled="processing" @click="validate()" id="submit" :label="'Submit: ' + moneyFormatter(payment_intent.amount / 100)" color="primary" class="q-mt-md" />
        <div id="payment-message" class="hidden"></div>
    <!-- </form> -->
</template>

<script setup>
import {loadStripe} from '@stripe/stripe-js';
import axios from 'axios';
import { ref } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar()
const props = defineProps({
    stripeURL: String,
    amount: Number,
    booking: Object
});

const stripe = ref(null);
const elements = ref(null);
const emit = defineEmits(['success']);
const payment_intent = ref(null);
const processing = ref(false);


getStripeDetails();
function getStripeDetails() {
    axios.get(props.stripeURL, {
        params: {
            amount: props.amount,
            booking_id: props.booking.id
        }
    })
    .then((response) => {
        getStripe(response.data.public_key).then((stripe_response) => {
            stripe.value = stripe_response;
            let clientSecret = response.data.payment_intent.client_secret;      
            payment_intent.value = response.data.payment_intent;
            const options = { layout: 'tabs' };
            const appearance = { theme: 'stripe', locale: 'en' };
            const stripe_elements = stripe_response.elements({ clientSecret, appearance });
            const paymentElement = stripe_elements.create('payment', options);
            elements.value = stripe_elements;

            paymentElement.mount('#payment-element');
        });
    })
    .catch((error) => {
        console.log(error);
    });
}

async function getStripe(key) {
  return loadStripe(key);
}

function validate() {
    processing.value = true;
    elements.value.submit()
    .then(function(result) {
       submit();
    })
    .catch(function(error) {
        $q.notify({
            message: error.error.message,
            color: 'negative',
            position: 'top',
            timeout: 3000
        });
        processing.value = false;
    });
}

function submit() {
    stripe.value.confirmPayment({
        elements: elements.value,
        confirmParams: { return_url: 'https://localhost:3000/' }, redirect: 'if_required' }        
    ).then(function (result) {
        console.log(result);
        if (result.error) {
            $q.notify({
                message: result.error.message,
                color: 'negative',
                position: 'top',
                timeout: 3000
            });
            return;
        } else {
            $q.notify({
                message: 'Payment successful',
                color: 'positive',
                position: 'top',
                timeout: 3000
            });
        }
        emit('success');
    })
    .catch(function (error) {
        $q.notify({
            message: error.message,
            color: 'negative',
            position: 'top',
            timeout: 3000
        });
    })
    .finally(() => {
        processing.value = false;
    });
            
}

const numberOptions = {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
};


function moneyFormatter(value) {
    return "£" + (value).toLocaleString("en-UK", numberOptions);
}

</script>