<template>
  <div v-if="isImageValid">
    <q-img
      class="rounded-borders q-mb-sm col-4 align-right"
      :src="props.src"
      :fit="props.banner_label ? 'cover' : 'scale-down'"
      :style="{ maxHeight: props.maxHeight || '50rem' }"
      :ratio="16 / 9"
      :alt="props.banner_label"
    >
      <q-badge v-if="editing" @click="removeImage()" floating color="red" round>
        <q-icon name="cancel" />
      </q-badge>
    </q-img>
  </div>
  <div v-else>
    <q-uploader
      v-if="editing && !props.src"
      :label="props.label"
      style="max-width: 12.5rem"
      :url="props.upload_url"
      auto-upload
      :headers="headers"
      max-files="1"
      @failed="uploadFailed"
      @uploaded="uploadComplete"
    />
  </div>
</template>

<script setup>
import axios from "axios";
import { ref, onMounted, watch } from "vue";
import { useQuasar } from "quasar";
const emit = defineEmits(["imageRemoved", "imageUploaded"]);
const $q = useQuasar();
const props = defineProps({
  label: String,
  src: String,
  upload_url: String,
  remove_url: String,
  editing: {
    type: Boolean,
    default: false,
  },
  banner_label: String,
  maxHeight: String,
});
const headers = [
  {
    name: "X-CSRF-TOKEN",
    value: axios.defaults.headers.common["X-CSRF-Token"],
  },
];

const isImageValid = ref(false);

function checkImage(url) {
  const img = new Image();
  img.onload = () => {
    isImageValid.value = true;
  };
  img.onerror = () => {
    $q.notify({
      message: "Error getting image, removing image",
      color: "warning",
      position: "top",
    });
    isImageValid.value = false;
    removeImage();
  };
  img.src = url;
}

function removeImage() {
  axios
    .delete(props.remove_url)
    .then(() => {
      $q.notify({
        message: "Image removed",
        color: "positive",
        position: "top",
      });
      emit("imageRemoved");
    })
    .catch(() => {
      $q.notify({
        message: "Failed to remove image",
        color: "negative",
        position: "top",
      });
    });
}

function uploadFailed(info) {
  $q.notify({ message: info, color: "negative", position: "top" });
}

function uploadComplete(info) {
  $q.notify({ message: "Upload Complete", color: "positive", position: "top" });
  emit("imageUploaded");
}

onMounted(() => {
  if (props.src) {
    checkImage(props.src);
  }
});

watch(
  () => props.src,
  (newSrc) => {
    if (newSrc) {
      checkImage(newSrc);
    }
  }
);
</script>
