<template>
    <q-input :loading="props.loading" :disabled="props.loading" outlined :model-value="dateInput" placeholder="Please Select Dates"
        @click="dateProxy.show()" data-cy="date" class="border">
        <template v-slot:prepend>
            <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy ref="dateProxy" cover transition-show="scale"
                    transition-hide="scale">
                    <q-date v-model="newDate" :loading="props.loading" :readonly="props.loading"
                        @range-end="dateProxy.hide(); emitDates();" title="Dates of stay" range
                        :options="dateOptions">
                    </q-date>
                </q-popup-proxy>
            </q-icon>
        </template>
    </q-input>
</template>

<script setup>
import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import { defineProps, defineEmits } from 'vue';

const emit = defineEmits(['update']);
const dateProxy = ref(null);
const props = defineProps({
    date: {
        type: Object,
        default: () => ({ from: null, to: null }),
    },
    loading: {
        type: Boolean,
        default: false,
    },
});
const newDate = toRef({ from: dayjs(props.date.from).format('YYYY/MM/DD'), to: dayjs(props.date.to).format('YYYY/MM/DD') });

const dateInput = computed(() => {
    if (newDate.value == null) {
        return '';
    }
    return dayjs(newDate.value.from).format('DD/MM/YY').toString() + ' - ' + dayjs(newDate.value.to).format('DD/MM/YY').toString();
})

function emitDates() {
    emit('update', newDate.value);
}
</script>