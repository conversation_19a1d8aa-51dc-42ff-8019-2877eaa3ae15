<template>
    <q-card flat :class="isSmallScreen ? 'full-width' : 'desktop-width'">
        <q-form @submit="onSubmit" greedy autofocus>
            <!-- Guests Section -->
            <div class="row justify-center">
                <div class="text-h4 q-mb-md text-primary"><q-icon name=group class="q-px-sm text-h2" />
                    Guests
                </div>
            </div>
            <!-- Booking Information -->
            <div class="row justify-center">
                <p>
                    Creating booking<span v-if="props.number_of_guests > 1">s</span> for
                    <strong>{{ props.hotel.name }}</strong>
                    from <strong>{{ formatDateUK(props.date.from) }}</strong> to
                    <strong>{{ formatDateUK(props.date.to) }}</strong>
                    for <strong>{{ props.number_of_guests }}</strong> guest<span
                        v-if="props.number_of_guests > 1">s</span>.
                </p>
            </div>

            <div v-for="(guest, index) in guests" :key="index" class="row">
                <div :class="'text-black text-h5 q-my-md'" style="width: 100%;"><q-icon name=person class="text-h4" />
                    Guest {{ index + 1 }}
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="guest.forename" outlined label="Guest forename *" type="text" lazy-rules
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="guest.surname" outlined label="Guest surname *" type="text" lazy-rules
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="guest.email" outlined label="Guest email *" type="email" lazy-rules
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="guest.telephone" outlined label="Guest telephone *" type="text" lazy-rules
                        :rules="[val => val && /^[0-9]+$/.test(val) && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="guest.special_requirements" outlined label="Guest special requirements" type="text" lazy-rules />
                </div>
                    <div class="col-xs-11 col-sm-6 q-px-xs q-mt-xs">
                        <div class="row">

                        <q-select v-model="guest.rfq_business_unit_id" :options="business_units" @update:model-value="assignBuToGuest(guest)"
                        outlined label="Business unit *"
                        class="col-11" option-value="id" option-label="name" hide-bottom-space map-options emit-value/>
                        <div class="col-1">
                            <q-btn @click="guest.additional_company_fields = !guest.additional_company_fields" :icon="guest.additional_company_fields ? 'keyboard_arrow_up' : 'keyboard_arrow_down'" flat round unelevated fab class="q-pt-md q-pl-md no-hover-shadow"/>
                        </div>
                    </div>
                </div>
                
                <div v-if="guest.additional_company_fields" :class="inputColumnClasses()">
                    <q-input v-model="guest.company_name" outlined label="Guest Company Name *" type="text" lazy-rules
                    :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div v-if="guest.additional_company_fields" :class="inputColumnClasses()">
                    <q-input v-model="guest.company_address_1" outlined label="Guest Company Address1" type="text" lazy-rules
                    :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div v-if="guest.additional_company_fields" :class="inputColumnClasses()">
                    <q-input v-model="guest.company_address_2" outlined label="Guest Company Address 2" type="text" lazy-rules
                    :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div v-if="guest.additional_company_fields" :class="inputColumnClasses()">
                    <q-input v-model="guest.company_postcode" outlined label="Guest Company Postcode" type="text" lazy-rules
                    :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div v-if="guest.additional_company_fields" :class="inputColumnClasses()">
                    <q-input v-model="guest.company_town" outlined label="Guest Company Town" type="text" lazy-rules
                    :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div v-if="guest.additional_company_fields" :class="inputColumnClasses()">
                    <q-input v-model="guest.company_county" outlined label="Guest Company County" type="text" lazy-rules
                    :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
            </div>

            <q-separator spaced="xl" color="primary" inset />

            <div class="row justify-center">
                <div class="text-h4 q-mb-md text-primary"><q-icon name=apartment class="q-px-sm text-h2" />
                    Company
                </div>
            </div>
            <div class="row">
                <q-select v-if="business_units.length > 0" v-model="booker.rfq_business_unit_id"
                    @update:model-value="canHaveBusinessAccounts()" outlined label="Business unit *"
                    :options="business_units" :class="inputColumnClasses() + ' col-12'" option-value="id"
                    option-label="name" hide-bottom-space emit-value map-options/>

                <div class="col-12">
                    <div class="text-subtitle q-ml-sm">
                        Don't see your branch here? Please call <strong>ServAce</strong> on: <strong>0344
                            8223227</strong>.
                    </div>
                </div>

                <!-- Button in a new row -->
                <div class="col-12">
                    <q-btn :disabled="!booker.rfq_business_unit_id" @click="assignBUToBooker()" label="Assign To Company"
                        color="blue" class="q-ma-sm" />
                </div>

                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.booker_company_name" outlined label="Company name *" type="text" lazy-rules
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.booker_company_address_1" outlined label="Company address 1" type="text" />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.booker_company_address_2" outlined label="Company address 2" type="text" />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.booker_company_postcode" outlined label="Company postcode" type="text" />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.booker_company_town" outlined label="Company town" type="text" />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.booker_company_county" outlined label="Company county" type="text" />
                </div>
            </div>

            <!-- Booker Section -->
            <q-separator spaced="xl" color="primary" inset />

            <div class="row justify-center">
                <div class="text-h4 q-mb-md text-primary"><q-icon name=support_agent class="q-px-sm text-h2" />
                    Booker
                </div>
            </div>
            <div class="row">
                <!-- Button in a separate row above inputs -->
                <div class="col-12 q-ma-sm">
                    <q-btn @click="autofillBooker()" label="Copy from guest 1" color="blue" />
                </div>

                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.forename" outlined label="Booker forename *" type="text"
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.surname" outlined label="Booker surname *" type="text"
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.email" outlined label="Booker email *" type="email" lazy-rules
                        :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.telephone" outlined label="Booker telephone" type="text" lazy-rules />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.notes" rows="2" outlined label="Booker notes" type="text" />
                </div>
                <div :class="inputColumnClasses()">
                    <q-input v-model="booker.special_requirements" outlined label="Booker special requirements"
                        type="text" />
                </div>
                <div :class="inputColumnClasses()">
                    <q-select :readonly="payment_options.length == 1" v-model="booker.payment_method" outlined label="Booker payment method *"
                        :options="payment_options" lazy-rules :rules="[val => val && val.length > 0 || '']"
                        hide-bottom-space />
                </div>
                <div v-if="booker.payment_method == 'Business Account'" :class="inputColumnClasses()">
                    <q-input v-model="booker.account_number" outlined label="Credit Account Number *" type="text"
                        lazy-rules :rules="[val => val && val.length > 0 || '']" hide-bottom-space />
                </div>
            </div>

            <div class="row column justify-center q-mt-md">
                <q-checkbox class="col justify-center" v-model="booker.tcs_acceptance" label=""/></br>
                <a href="https://onestop-terms-and-conditions.s3.eu-west-1.amazonaws.com/Terms+and+Conditions+-+Academy.pdf" target="_blank" style="color: #004F59;">
                     <p class="row col justify-center">Do you accept the terms and conditions?</p>
                </a>
            </div>

            <!-- Submit Button -->
            <q-btn v-if="editing" :disabled="!requiredFieldsCompleted" @click="emitData();"
                label="Preview Confirmation" color="primary" class="full-width-btn" />


            <q-btn v-else label="Submit" type="submit" :disabled="!requiredFieldsCompleted" color="positive"
                class="full-width-btn q-my-md" />

            <q-btn @click="emit('goBack')" class="full-width-btn" color="blue" size="md" label="Back" />
        </q-form>
    </q-card>
</template>


<script setup>
import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import axios from 'axios';
import { useQuasar } from 'quasar';
const $q = useQuasar();

const isSmallScreen = computed(() => {
    return $q.screen.width < 650;
});

const props = defineProps({
    rfq_location_id: Number,
    hotel: {
        type: Object,
        default: { name: 'Test Hotel' }
    },
    room: {
        type: Object,
        default: { room_type: 'Single', pkg_type: 'DBB' }
    },
    date: {
        type: Object,
        default: { from: new Date(), to: new Date() }
    },
    number_of_guests: {
        type: Number,
        default: 1
    },
    editing: Boolean,
    total_nights: {
        type: Number,
        default: 1
    }
});

const emit = defineEmits(['goBack', 'success']);


const guests = ref([]);
const booker = ref({tcs_acceptance: false});
const business_units = ref([]);
const bu_account_numbers = ref([]);
const payment_options = ref([]);
const show_payment_options = ref(false);

onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'smooth' }) // Use 'auto' instead of 'smooth' if preferred
})

if (guests.value.length === 0) {
    generateGuests();
}
function generateGuests() {
    for (let i = 0; i < parseInt(props.number_of_guests); i++) {
        guests.value.push({additional_company_fields: false});
    }
}

getBusinessUnits();
function getBusinessUnits() {
    axios.get('/rfq_locations/' + parseInt(props.rfq_location_id) + '/adult_bookings/get_business_units')
        .then((response) => {
            business_units.value = response.data.business_units;
            bu_account_numbers.value = response.data.bu_account_numbers;
        })
        .catch((error) => {
            console.log(error);
        });
}

getPaymentOptions();
function getPaymentOptions() {
    axios.get('/rfq_locations/' + parseInt(props.rfq_location_id) + '/adult_bookings/get_academy_payment_options')
        .then((response) => {
            payment_options.value = response.data.payment_options;
            if (payment_options.value.length > 0) {
                booker.value.payment_method = payment_options.value[0];
            }
        })
        .catch((error) => {
            console.log(error);
        });
}

function assignBuToGuest(guest) {
    let business_unit = getSelectedBusinessUnit(guest.rfq_business_unit_id);
    guest.company_name = business_unit.name
    guest.company_address_1 = business_unit.address1
    guest.company_address_2 = business_unit.address2
    guest.company_postcode = business_unit.postcode
    guest.company_address_town = business_unit.town
    guest.company_address_county = business_unit.county
}

function assignBUToBooker() {
    let business_unit = getSelectedBusinessUnit(booker.value.rfq_business_unit_id);
    booker.value.booker_company_name = business_unit.name;
    booker.value.booker_company_address_1 = business_unit.address1;
    booker.value.booker_company_address_2 = business_unit.address2;
    booker.value.booker_company_town = business_unit.town;
    booker.value.booker_company_county = business_unit.county;
    booker.value.booker_company_postcode = business_unit.postcode;
}

function getSelectedBusinessUnit(bu_id) {
    return business_units.value.find(bu => bu.id === bu_id);
}

function canHaveBusinessAccounts() {
    // if BU is selected and BU is in the list of account numbers and the selected hotel is in the list of account numbers, show business account option
    if (booker.value.rfq_business_unit_id && 
        bu_account_numbers.value.map(a => a.rfq_business_unit_id).includes(booker.value.rfq_business_unit_id) &&
        bu_account_numbers.value.map(a => a.rfq_response_id).includes(props.hotel.rfq_response_id) ) {
            if (!payment_options.value.includes('Business Account')) {
                payment_options.value.push('Business Account');
            }
            show_payment_options.value = true;
    } else {
        if (payment_options.value == 'Business Account') {
            booker.value.payment_method = payment_options.value[0] || null;
        }
        payment_options.value = payment_options.value.filter(option => option != 'Business Account');
        booker.value.account_number = null;
        show_payment_options.value = false;
    }
}

function autofillBooker() {
    if (guests.value[0].forename && guests.value[0].surname) {
        booker.value.forename = guests.value[0].forename;
        booker.value.surname = guests.value[0].surname;
    }
    if (guests.value[0].telephone) {
        booker.value.telephone = guests.value[0].telephone;
    }
    if (guests.value[0].email) {
        booker.value.email = guests.value[0].email;
    }
}

function formatDateUK(date) {
    return dayjs(date).format('DD/MM/YYYY');
}


function onSubmit() {
    if (!requiredFieldsCompleted) {
        $q.notify({
            color: 'red-5',
            textColor: 'white',
            icon: 'warning',
            message: 'Please ensure all required fields are completed'
        })
    }
    else if (booker.value.payment_method == 'Business Account' && (!booker.value.account_number || getSelectedBookerBusinessUnit().acc_number != booker.value.account_number)) {
        $q.notify({
            color: 'red-5',
            textColor: 'white',
            icon: 'warning',
            message: 'Sorry, the account number you have entered does not match the account number for the selected business unit'
        })
    }
    else if (checkIfDuplicateExists(guests.value.map(el => el.email.trim().toLowerCase()))) {
        $q.notify({
            color: 'red-5',
            textColor: 'white',
            icon: 'warning',
            message: 'One or more of your guests has the same email address, please ensure all guests emails are unique'
        })
    }
    else {
        emitData();
    }
}

function emitData() {
    let payload = {guests: guests.value, booker: booker.value}
    emit('success', payload)
}

function inputColumnClasses() {
    return 'col-xs-12 col-sm-6 q-px-xs q-mt-xs'
}

const requiredFieldsCompleted = computed(() => {
    return (guests.value.every(guest => guest.forename && guest.surname && guest.telephone && guest.email) &&
        booker.value.booker_company_name &&
        booker.value.forename && booker.value.surname && booker.value.email && booker.value.payment_method && booker.value.tcs_acceptance);
})

function checkIfDuplicateExists(arr) {
    // Because each value in the Set has to be unique, the value equality will be checked.
    return new Set(arr).size !== arr.length
}

const colors = ['secondary', 'accent', 'info'];

const getGuestColor = (index) => {
    return colors[index % colors.length]; // Loops through colors
};
</script>

<style scoped>
/* remove input number arrows */
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type=number] {
    -moz-appearance: textfield;
}

.full-width {
    width: 100%;
    margin: auto;
}

.desktop-width {
    width: 70%;
    margin: auto;
}

.full-width-btn {
    width: 100%;
}

.q-col-gutter-md {
    gap: 12px;
    /* Adds spacing between elements */
}

.no-hover-shadow:hover {
  box-shadow: none !important;
}
</style>