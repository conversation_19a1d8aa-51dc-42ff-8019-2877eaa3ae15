<template>
    <q-btn @click="isOpen = true" class="q-ml-md" round color="secondary" icon="edit" size="xs" />
    <q-dialog v-model="isOpen" persistent>
      <q-card class="q-pa-md" style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">Edit Attendee</div>
        </q-card-section>
  
        <q-separator />
  
        <q-card-section>
          <q-form @submit.prevent="saveAttendee">
            <q-input
                class="q-mb-sm"
                v-model="form.forename"
                label="Forename"
                outlined
                dense
                required
            />
            <q-input
                class="q-mb-sm"
                v-model="form.surname"
                label="Surname"
                outlined
                dense
                required
            />
            <q-input
                class="q-mb-sm"
                v-model="form.email"
                label="Email"
                type="email"
                outlined
                dense
                required
            />
            <q-input
                class="q-mb-sm"
                v-model="form.telephone"
                label="Telephone"
                outlined
                dense
            />
            <q-select
                class="q-mb-sm"
                v-model="form.rfq_response_room_id"
                :options="roomTypes"
                :readonly="!props.editRoomType"
                label="Room Type"
                outlined
                dense
                option-value="id"
                option-label="name"
                emit-value
                map-options
            />
            <q-input
                class="q-mb-sm"
                v-model="form.company_name"
                label="Company Name"
                outlined
                dense
            />
          </q-form>
        </q-card-section>
  
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn label="Save" color="primary" @click="saveAttendee" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </template>
  
  <script setup>
    import { ref, watch, reactive } from 'vue'
    import axios from 'axios'
    import { useQuasar } from 'quasar';
    const $q = useQuasar();

  
    const props = defineProps({
        modelValue: { type: Boolean, required: true },
        attendee: {
            type: Object,
            required: true,
            default: () => ({
                forename: '',
                surname: '',
                email: '',
                telephone: '',
                rfq_response_room_id: '',
                company_name: ''
            })
        },
        editRoomType: {
            type: Boolean,
            default: false
        }
    })
    
    const emit = defineEmits(['update:modelValue', 'save'])
    const roomTypes = ref([])
    const isOpen = ref(props.modelValue)
    watch(() => props.modelValue, (val) => (isOpen.value = val))
    watch(isOpen, (val) => emit('update:modelValue', val))
    
    const form = reactive({
        forename: '',
        surname: '',
        email: '',
        telephone: '',
        rfq_response_room_id: '',
        company_name: ''
    })
    
    // Initialize form when attendee changes
    watch(
        () => props.attendee,
        (newVal) => {
        Object.assign(form, { ...newVal })
        },
        { immediate: true }
    )

    getRoomTypes()
    function getRoomTypes() {
        axios
        .get(`/academy/bookings/${props.attendee.booking_id}/get_available_rooms`)
        .then((response) => {
            roomTypes.value = response.data.rooms
        })
        .catch((error) => {
            console.error('Error fetching room types:', error)
        })
    }
  
    function saveAttendee() {
        axios
        .put(`/academy/bookings/${props.attendee.booking_id}/booking_attendees/${props.attendee.id}`, form)
        .then((response) => {
            emit('save', response.data)
            $q.notify({
                    message: 'Attendee updated successfully',
                    color: 'positive',
                    position: 'top'
                });
            isOpen.value = false
        })
        .catch((error) => {
            console.error('Error saving attendee:', error)
        })
    }
  </script>
  