<template>
  <q-layout view="lHh Lpr lFf">
    <q-page-container>
      <q-page class="q-pa-md">
        <q-card flat class="booking-card">
          <!-- Header Section -->
          <q-card-section class="row items-center justify-between q-pb-none">
            <div class="col-auto">
              <q-btn
                class="q-mb-md"
                color="primary"
                flat
                @click="goTo(`/academy/bookings`)"
                icon="arrow_back"
                label="Back to Bookings"
              />
              <h1 class="q-mt-none text-h5 text-primary text-weight-bold">
                Academy Booking #{{ props.booking.id }}
              </h1>
            </div>
            <div
              v-if="isAdmin && props.rfqRequest && props.rfqRequest.id"
              class="col-auto"
            >
              <q-btn
                color="primary"
                outline
                @click="goTo(`/admin/rfq_requests/${props.rfqRequest.id}`)"
                :label="'View RFQ #' + props.rfqRequest.id"
                icon="open_in_new"
              />
            </div>
          </q-card-section>

          <!-- Main Content Section -->
          <q-card-section>
            <div class="row q-col-gutter-md">
              <!-- Left Column: Booking Details and Booker Information -->
              <div class="col-12 col-md-6 col-lg-8">
                <!-- Booking Details -->
                <q-card flat class="details-card q-mb-md">
                  <q-card-section>
                    <h3 class="text-h6 q-mt-none text-primary text-weight-bold">
                      Booking Details
                    </h3>
                    <div
                      class="text-h6 text-weight-bold q-my-sm"
                      :class="bookingStatusClass()"
                    >
                      {{ bookingStatus() }}
                    </div>
                    <q-markup-table flat bordered>
                      <tbody>
                        <tr :class="bookingStatusClass()">
                          <td class="table-label">Status</td>
                          <td class="table-value">
                            {{ bookingStatus() }}
                            <span
                              v-if="props.booking.hotel_declined_at"
                              class="text-caption"
                              >
                                by {{props.booking.hotel_declined_by.name }} for {{ props.booking.hotel_decline_reason }}
                            </span>
                            <span
                              v-else-if="props.booking.cancelled_at"
                              class="text-caption"
                            >
                              by {{props.booking.cancelled_by.name }} for {{ props.booking.cancellation_reason }}

                              ({{ props.booking.cancellation_reason }})
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Booking ID</td>
                          <td class="table-value">#{{ props.booking.id }}</td>
                        </tr>
                        <tr>
                          <td class="table-label">Reservation Number</td>
                          <td class="table-value">
                            {{
                              props.booking.reservation_number || "Not assigned"
                            }}
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Hotel</td>
                          <td class="table-value">{{ props.hotel.name }}</td>
                        </tr>
                        <tr>
                          <td class="table-label">Booking Dates</td>
                          <td class="table-value" v-if="dateEditor">
                            <div class="row items-center no-wrap">
                              <date-picker
                                @update="saveDates"
                                :date="{
                                  from: props.booking.check_in,
                                  to: props.booking.check_out,
                                }"
                                :loading="loading"
                              />
                              <q-btn
                                @click="dateEditor = !dateEditor"
                                class="q-ml-sm"
                                round
                                color="primary"
                                icon="close"
                                size="sm"
                                flat
                              />
                            </div>
                          </td>
                          <td class="table-value" v-else>
                            <div class="date-range">
                              <q-icon name="event" class="q-mr-xs" />
                              {{ UKFormatter(props.booking.check_in) }} -
                              {{ UKFormatter(props.booking.check_out) }}
                              <q-btn
                                v-if="editable"
                                @click="dateEditor = !dateEditor"
                                class="q-ml-sm"
                                round
                                color="primary"
                                icon="edit"
                                size="sm"
                                flat
                              />
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Special Requirements</td>
                          <td class="table-value">
                            {{
                              props.booking.special_requirements ||
                              "None specified"
                            }}
                          </td>
                        </tr>
                      </tbody>
                    </q-markup-table>
                  </q-card-section>
                </q-card>

                <!-- Booker Information -->
                <q-card flat class="details-card q-mb-md">
                  <q-card-section>
                    <h6 class="text-h6 q-mt-none q-mb-md text-primary">
                      Booker Information
                    </h6>
                    <q-markup-table flat bordered>
                      <tbody>
                        <tr>
                          <td class="table-label">Date Booked</td>
                          <td class="table-value">
                            {{ UKFormatter(props.booking.created_at) }}
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Name</td>
                          <td class="table-value">
                            {{ props.booking.booker_forename }}
                            {{ props.booking.booker_surname }}
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Email</td>
                          <td class="table-value">
                            <a
                              :href="'mailto:' + props.booking.booker_email"
                              class="text-primary"
                            >
                              {{ props.booking.booker_email }}
                            </a>
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Telephone</td>
                          <td class="table-value">
                              {{ props.booking.booker_telephone }}
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Company</td>
                          <td class="table-value">
                            {{ props.booking.booker_company_name }}
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label">Address</td>
                          <td class="table-value">{{ booker_address }}</td>
                        </tr>
                        <tr v-if="props.booking.hotel_confirmed_at">
                          <td class="table-label">Hotel Confirmation</td>
                          <td class="table-value">
                            <q-badge
                              color="positive"
                              icon="check_circle"
                              class="q-mr-xs"
                            />
                            {{ UKFormatter(props.booking.hotel_confirmed_at) }}
                            by {{ props.booking.hotel_confirmed_by.name }}
                          </td>
                        </tr>
                      </tbody>
                    </q-markup-table>
                  </q-card-section>
                </q-card>

                <!-- Payment Information -->
                <q-card flat class="details-card">
                  <q-card-section>
                    <h6 class="text-h6 q-mt-none q-mb-md text-primary">
                      Payment Details
                    </h6>
                    <q-markup-table flat bordered>
                      <tbody>
                        <tr>
                          <td class="table-label">Payment Method</td>
                          <td class="table-value">
                            {{ props.booking.payment_method || "Not specified" }}
                          </td>
                        </tr>
                        <tr v-for="payment in props.payments" :key="payment.id">
                          <td class="table-label">Payment</td>
                          <td class="table-value">
                            <q-badge
                              :color="paymentStatus(payment)"
                              icon="payment"
                              class="q-mr-xs"
                            />
                            {{ UKFormatter(payment.date) }} -
                            {{ moneyFormatter(payment.amount / 100) }} -
                            ({{ (payment.status == 'refund' ? payment.stripe_refund_id : payment.stripe_charge_id) || "N/A" }})
                          </td>
                        </tr>
                        <tr>
                          <td class="table-label bg-primary text-white">
                            Total Cost
                          </td>
                          <td class="table-value">
                            <span class="text-h6 text-weight-bold">
                              {{
                                moneyFormatter(props.booking.total_cost / 100)
                              }}
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </q-markup-table>
                  </q-card-section>
                </q-card>
              </div>

              <!-- Right Column: Attendees -->
              <div class="col-12 col-md-6 col-lg-4">
                <q-card flat class="details-card">
                  <q-card-section>
                    <h3 class="text-h6 q-mt-none text-primary text-weight-bold">
                      Attendees ({{ nonCancelledAttendees.length }})
                    </h3>
                    <div class="attendees-container">
                      <div
                        v-for="(attendee, index) in displayedAttendees"
                        :key="attendee.id"
                        class="attendee-item q-mb-sm"
                      >
                        <q-card flat bordered class="attendee-card">
                          <q-card-section class="q-pa-md">
                            <div class="row items-center justify-between">
                              <div class="col">
                                <div class="text-subtitle2 text-primary">
                                  Attendee {{ index + 1 }}
                                </div>
                                <div class="text-body1 q-mt-xs">
                                  <q-icon name="person" class="q-mr-xs q-pb-xs" />
                                  {{ attendee.forename }} {{ attendee.surname }}
                                </div>
                                <div v-if="attendee.reservation_number" class="text-body2 text-grey-7 q-mt-xs">
                                  <q-icon name="pin" class="q-mr-xs q-pb-xs" />
                                  {{ attendee.reservation_number }}
                                </div>
                                <div class="text-body2 text-grey-7 q-mt-xs">
                                  <q-icon name="email" class="q-mr-xs q-pb-xs" />
                                  {{ attendee.email }}
                                </div>
                                <div class="text-body2 text-grey-7 q-mt-xs">
                                  <q-icon name="business" class="q-mr-xs q-pb-xs" />
                                  {{ attendee.company_name }}
                                </div>
                                <div class="text-body2 text-grey-7 q-mt-xs">
                                  <q-icon name="hotel" class="q-mr-xs q-pb-xs" />
                                  {{ attendee.room_name }}
                                </div>
                                <div v-if="attendee.special_requirements" class="text-body2 text-red q-mt-xs">
                                  <q-icon name="gpp_maybe" class="q-mr-xs q-pb-xs text-red" />
                                  {{ attendee.special_requirements }}
                                </div>
                              </div>
                              <div class="col-auto" v-if="!props.booking.cancelled_at">
                                <edit-booking-attendee
                                  :attendee="attendee"
                                  :model-value="false"
                                  :edit-room-type="editable"
                                />
                                <q-btn
                                  v-if="!attendee.cancelled_at && nonCancelledAttendees.length > 1"
                                  round
                                  size="xs"
                                  icon="close"
                                  color="negative"
                                  class="q-ml-sm"
                                  @click="cancelAttendeePrompt(attendee)"
                                />
                              </div>
                            </div>
                          </q-card-section>
                        </q-card>
                      </div>

                      <!-- Show More/Less Button -->
                      <div
                        v-if="nonCancelledAttendees.length > 5"
                        class="text-center q-mt-md"
                      >
                        <q-btn
                          @click="toggleAttendeesExpanded"
                          :label="
                            attendeesExpanded
                              ? 'Show Less'
                              : `Show ${nonCancelledAttendees.length - 5} More`
                          "
                          :icon="
                            attendeesExpanded ? 'expand_less' : 'expand_more'
                          "
                          color="primary"
                          flat
                          class="q-px-lg"
                        />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </q-card-section>

          <!-- Action Buttons Section -->
          <q-separator class="q-my-md" />
          <q-card-section>
            <div class="row q-gutter-md justify-center">
              <confirm-decline-booking
                v-if="!isClient &&
                  !(
                    props.booking.hotel_confirmed_at ||
                    props.booking.hotel_declined_at ||
                    props.booking.cancelled_at
                  )"
                v-slot="{ onClick }"
                :booking="props.booking"
                :is-admin="isAdmin"
                :is-supplier="isHotel"
                @processed="reloadPage"
              >
                <q-btn
                  @click="onClick"
                  color="positive"
                  icon="check_circle"
                  label="Confirm/Decline Booking"
                  class="q-px-lg"
                />
              </confirm-decline-booking>

              <cancel-booking
                v-if="!isHotel && !props.booking.cancelled_at"
                :booking="props.booking"
                v-slot="{ onClick }"
                :is-admin="isAdmin"
                @processed="reloadPage"
              >
                <q-btn
                  @click="onClick"
                  color="negative"
                  icon="cancel"
                  label="Cancel Booking"
                />
              </cancel-booking>
            </div>
          </q-card-section>
        </q-card>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { defineProps, computed, ref } from "vue";
import axios from "axios";
import dayjs from "dayjs";
import { useQuasar } from "quasar";

const $q = useQuasar();

import confirmDeclineBooking from "./confirm-decline-booking.vue";
import cancelBooking from "./cancel-booking.vue";
import EditBookingAttendee from "./edit-booking-attendee.vue";
import DatePicker from "../common/inputs/date-picker.vue";

const props = defineProps({
  booking: Object,
  payments: Array,
  attendees: Array,
  hotel: Object,
  rfqRequest: Object,
  userType: String,
});

const isAdmin = computed(() => props.userType === "Admin");
const isHotel = computed(() => props.userType === "Supplier");
const isClient = computed(() => props.userType === "Client");

const numberOptions = {
  style: "decimal",
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
};

const dateEditor = ref(false);
const attendeesExpanded = ref(false);
const loading = ref(false);

const displayedAttendees = computed(() => {
  if (attendeesExpanded.value || nonCancelledAttendees.length <= 5) {
    return nonCancelledAttendees.value;
  }
  return nonCancelledAttendees.value.slice(0, 5);
});

const booker_address = computed(() => {
  let address = "";

  if (props.booking.booker_company_address_1) {
    address += props.booking.booker_company_address_1 + ", ";
  }
  if (props.booking.booker_company_address_2) {
    address += props.booking.booker_company_address_2 + ", ";
  }
  if (props.booking.booker_company_town) {
    address += props.booking.booker_company_town + ", ";
  }
  if (props.booking.booker_company_county) {
    address += props.booking.booker_company_county + ", ";
  }
  if (props.booking.booker_company_postcode) {
    address += props.booking.booker_company_postcode;
  }
  if (address == "") {
    return "No address provided";
  }

  return address.trim().replace(/, $/, "");
});

function goTo(link) {
  window.location.href = link;
}

function UKFormatter(date) {
  return dayjs(date).format("DD/MM/YYYY");
}

function moneyFormatter(value) {
  // turn pence to pounds and add pound sign and formatting
  return "£" + parseFloat(value).toLocaleString("en-UK", numberOptions);
}

function bookingStatus() {
  if (props.booking.hotel_declined_at) {
    return "Declined";
  }  else if (props.booking.cancelled_at) {
    return "Cancelled";
  } else if (props.booking.confirmed_at) {
    return "Confirmed";
  } else {
    return "Unconfirmed";
  }
}

function bookingStatusClass() {
  const status = bookingStatus();
  return {
    "bg-orange-2": status === "Declined",
    "bg-red-2": status === "Cancelled",
    "bg-grey-2 text-primary": status === "Confirmed",
    "bg-green-2": status === "Unconfirmed",
  };
}

function saveDates(dates) {
  loading.value = true;
  console.log("Saving dates:", dates);
  axios
    .patch(`/academy/bookings/${props.booking.id}/update_dates`, {
      check_in: dates.from,
      check_out: dates.to,
    })
    .then(() => {
      props.booking.check_in = dates.from;
      props.booking.check_out = dates.to;
      $q.notify({
        message: "Booking dates updated successfully",
        color: "positive",
        position: "top",
      });
    })
    .catch((error) => {
      console.error("Error updating booking dates:", error);
      $q.notify({
        message: "Failed to update booking dates",
        color: "negative",
        position: "top",
      });
    })
    .finally(() => {
      dateEditor.value = false;
      loading.value = false;
    });
}

function toggleAttendeesExpanded() {
  attendeesExpanded.value = !attendeesExpanded.value;
}

function cancelAttendeePrompt(attendee) {
    $q.dialog({
      title: 'Cancel Attendee',
      message: 'Are you sure you want to cancel this attendee?',
      cancel: true,
    }).onOk(() => {
      cancelAttendee(attendee)
    }).onCancel(() => {
      
    })
}

function cancelAttendee(attendee) {
  axios
    .patch(`/academy/bookings/${props.booking.id}/booking_attendees/${attendee.id}/cancel_attendee`)
    .then(() => {
      $q.notify({
        message: "Attendee cancelled successfully",
        color: "positive",
        position: "top",
      });
      reloadPage();
    })
    .catch((error) => {
      console.error("Error cancelling attendee:", error);
      $q.notify({
        message: "Failed to cancel attendee",
        color: "negative",
        position: "top",
      });
    });
}

function reloadPage() {
  window.location.reload();
}

function paymentStatus(payment) {
  if (payment.status === 'paid') { 
    return "positive";
  } else if (payment.status === 'refund') { 
    return "blue";
  } else if (payment.status === 'pending') { 
    return "yellow";
  } else if (payment.status === 'failed') { 
    return "warning";
  } else {
    return "grey";
  }
}

const nonCancelledAttendees = computed(() => {
  return props.attendees.filter(
    (attendee) => !attendee.cancelled_at
  );
});

const editable = computed(() => {
  return isAdmin.value || (isClient.value && !props.booking.hotel_confirmed_at);
});
</script>

<style scoped>
.booking-card {
  max-width: 1200px;
  margin: 0 auto;
}

.details-card {
  background-color: #f9f9f9;
}

.table-label {
  font-weight: 600;
  color: #666;
  width: 40%;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-right: 1px solid #e0e0e0;
}

.table-value {
  padding: 12px 16px;
  color: #000;
}

.attendee-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.date-range {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.attendees-container {
  max-height: 880px;
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .booking-card {
    margin: 0;
  }

  .table-label {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .table-value {
    width: 100%;
  }

  .attendees-container {
    max-height: 400px;
  }
}

@media (max-width: 1024px) {
  .attendees-container {
    max-height: 500px;
  }
}
</style>
