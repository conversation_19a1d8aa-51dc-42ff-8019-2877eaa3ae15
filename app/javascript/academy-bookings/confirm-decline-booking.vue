<template>
    <slot :onClick="onClick">
        <q-btn class="q-mb-sm"  size="sm"
            color="secondary" rounded label="Confirm/Decline"
            v-if="
                  !isClient &&
                  !(
                    props.booking.hotel_confirmed_at ||
                    props.booking.hotel_declined_at ||
                    props.booking.cancelled_at
                  )
                "
            @click="onClick" >
            <q-tooltip anchor="bottom middle" self="top middle">
                Confirm/Decline Booking
            </q-tooltip>
        </q-btn>
    </slot>
    <q-dialog v-model="visible" persistent>
        <q-card class="bg-white text-black">
            <q-card-section class="q-pt-none">
                <div class="text-h6">Confirm/Decline Booking</div>
                <div class="q-mt-md">
                    <p>Are you sure you want to confirm/decline this booking?</p>
                    <p><strong>Booking ID:</strong> {{ props.booking.id }}</p>
                    <p v-if="isAdmin">This will charge the client {{ moneyFormatter(props.booking.total_cost / 100) }}</p>
                </div>
            </q-card-section>

            <q-card-actions>
                <q-btn label="Confirm" color="primary" @click="confirmDialogVisible = true;" />
                <q-btn label="Decline" color="negative" @click="declineDialogVisible = true" />
                <q-btn label="Cancel" color="grey" @click="visible = false" />
            </q-card-actions>
        </q-card>
    </q-dialog>

    <q-dialog v-model="confirmDialogVisible" persistent>
        <q-card class="bg-white text-black">
            <q-card-section class="q-pt-none">
                <div class="text-h6">Confirm Booking</div>
                <div class="q-mt-md">
                    <p>Are you sure you want to confirm this booking?</p>
                    <q-input
                        filled
                        v-model="props.booking.reservation_number"
                        label="Reservation Number"
                        :autofocus="true"
                        :rules="[val => !!val || 'Reservation Number is required']"
                    />
                    <p v-if="isAdmin">This will charge the client {{ moneyFormatter(props.booking.total_cost / 100) }}</p>
                </div>
            </q-card-section>

            <q-card-actions>
                <q-btn label="Confirm" color="primary" @click="submitConfirm()" />
                <q-btn label="Cancel" color="grey" @click="confirmDialogVisible = false" />
            </q-card-actions>
        </q-card>
    </q-dialog>

    <q-dialog v-model="declineDialogVisible" persistent>
        <q-card class="bg-white text-black">
            <q-card-section class="q-pt-none">
                <div class="text-h6">Decline Booking</div>
                <div class="q-mt-md">
                    <p>Are you sure you want to decline this booking?</p>
                    <q-input
                        filled
                        v-model="props.booking.hotel_decline_reason"
                        label="Decline Reason"
                        type="textarea"
                        :rows="3"
                        :autofocus="true"
                        :rules="[val => !!val || 'Decline reason is required']"
                    />
                </div>
            </q-card-section>

            <q-card-actions>
                <q-btn label="Decline" color="negative" @click="submitDecline()" />
                <q-btn label="Close" color="grey" @click="declineDialogVisible = false" />
            </q-card-actions>
        </q-card>
    </q-dialog>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { useQuasar } from 'quasar';
const $q = useQuasar();
const props = defineProps({
    booking: {
        type: Object,
        required: true
    },
    isSupplier: {
        type: Boolean,
        default: false
    },
    isAdmin: {
        type: Boolean,
        default: false
    },
});

const visible = ref(false);
const emit = defineEmits(['processed']);
const confirmDialogVisible = ref(false);
const declineDialogVisible = ref(false);
const numberOptions = {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
};

function onClick() {
    visible.value = true;
}


function submitConfirm() {
    if (!props.booking.reservation_number) {
        // If the reservation number is not provided, show an error
        $q.notify({
            message: 'Please provide a reservation number.',
            color: 'negative',
            position: 'top'
        });
        return;
    }
    confirmBooking(props.booking);
}

function submitDecline() {
    if (!props.booking.hotel_decline_reason) {
        // If the decline reason is not provided, show an error
        $q.notify({
            message: 'Please provide a decline reason.',
            color: 'negative',
            position: 'top'
        });
        return
    }
    declineBooking();
}

function confirmBooking() {
    axios.patch('/academy/bookings/' + props.booking.id + '/hotel_confirm_booking',
        {
            reservation_number: props.booking.reservation_number
        }
    )
        .then(response => {
            closeAllDialogs();
            $q.notify({
                message: 'Booking confirmed',
                color: 'positive',
                position: 'top'
            });
            emit('processed');
        })
        .catch(error => {
            console.error(error);
            $q.notify({
                message: 'Error confirming booking',
                color: 'negative',
                position: 'top'
            });
        });
}

function declineBooking() {
    axios.patch('/academy/bookings/' + props.booking.id + '/hotel_decline_booking',
        {
            hotel_decline_reason: props.booking.hotel_decline_reason
        }
    )
        .then(response => {
            closeAllDialogs();
            $q.notify({
                message: 'Booking declined',
                color: 'positive',
                position: 'top'
            });
            emit('processed');
        })
        .catch(error => {
            console.error(error);
            $q.notify({
                message: 'Error declining booking',
                color: 'negative',
                position: 'top'
            });
        });
}

function moneyFormatter(value) {
  // turn pence to pounds and add pound sign and formatting
  return "£" + (parseFloat(value)).toLocaleString("en-UK", numberOptions);
}

function closeAllDialogs() {
    visible.value = false;
    confirmDialogVisible.value = false;
    declineDialogVisible.value = false;
}


</script>