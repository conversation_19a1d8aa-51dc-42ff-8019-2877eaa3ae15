<template>
    <q-layout view="hHh lpR fFf">
        <q-page-container class="row justify-center page-container">
            <div class="col-md-2 edge-col" />
            <q-page padding class="col-12 col-xl-8">
                <q-card flat>
                    <q-btn v-if="props.editing" @click="backToRfqRequest()" label="Back" color="blue" class="q-my-md"
                        data-cy="back-to-rfq-btn" />
                    <q-card-section>
                        <div class="row items-center justify-between q-gutter-md no-wrap"
                            style="max-height: 8rem !important;">
                            <q-img class="col-3" src="../images/adult-bookings/ServaceAcademyHub.png" fit="scale-down"
                                data-cy="servace-accom-logo" />
                            <q-space />
                            
                            <removable-image :src="partner_logo_url" :label="'Partner Logo'"
                                :upload_url="'/rfq_locations/' + selected_rfq_location_id + '/adult_bookings/create_partner_logo'"
                                :remove_url="'/rfq_locations/' + selected_rfq_location_id + '/adult_bookings/delete_partner_logo'"
                                :editing="editing" @imageRemoved="removePartnerLogo" @imageUploaded="getPartnerLogo"
                                class="items-end" data-cy="client-logo" />
                        </div>
                    </q-card-section>
                </q-card flat>

                <q-stepper v-model="step" ref="stepper" :header-nav="props.editing" done-color="green"
                    active-color="blue" inactive-color="primary" animated alternative-labels class="no-shadow"
                    data-cy="q-stepper" :contracted="isSmallScreen">
                    <q-step :name="1" title="Select Dates & Hotel" icon="event_available" :done="step > 1"
                        data-cy="date-and-hotels">
                        <q-card flat>
                            <q-card-section class="row no-wrap items-center q-gutter-md q-ma-none q-pa-none"
                                data-cy="welcome-message-container">

                                <!-- Welcome Message Section -->
                                <div class="q-ma-none q-pa-md col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                    <edit-rich-html v-if="editing" :html="detail.welcome_msg"
                                        :save_url="'/admin/rfq_locations/' + selected_rfq_location_id + '/adult_accomm_detail/update_welcome_message'"
                                        @changeSuccessful="updateWelcomeMessage" data-cy="welcome-msg-editor" />
                                    <span v-html="detail.welcome_msg" data-cy="welcome-msg" />
                                </div>
                                
                                <!-- Image Section -->
                                <div class="row items-center justify-between q-gutter-md no-wrap">   
                                    <q-toggle v-if="editing" v-model="upload_welcome_toggle" color="primary" keep-color label="editing/removing own welcome image toggle"/>
                                    <removable-image v-if="editing && (welcome_image_url == null || welcome_image_url == '') && upload_welcome_toggle" :src="welcome_image_url" :label="'Welcome Image (in place of bell)'"
                                    :upload_url="'/rfq_locations/' + selected_rfq_location_id + '/adult_bookings/create_welcome_image'"
                                    :remove_url="'/rfq_locations/' + selected_rfq_location_id + '/adult_bookings/delete_welcome_image'"
                                    :editing="editing" @imageRemoved="removeWelcomeImage" @imageUploaded="getWelcomeImage"
                                    class="items-end" data-cy="welcome-logo" />
                                    <q-badge v-if="editing && upload_welcome_toggle" @click="removeWelcomeImage()" floating color="red" round>
                                        <q-icon name="cancel" />
                                    </q-badge>
                                </div>
                                
                                <div v-if="(welcome_image_url == null || welcome_image_url == '') && !upload_welcome_toggle"
                                    class="q-ma-none q-pa-none col-xs-12 col-sm-12 col-md-6 col-lg-6 welcome-image-wrapper">
                                        <q-img class="welcome-image" src="../images/adult-bookings/HotelBell.jpg"
                                        fit="scale-down" data-cy="welcome-image" position="right" />
                                </div>

                                <q-img v-else class="welcome-image" :src="welcome_image_url" fit="scale-down" data-cy="welcome-image" position="right" />

                            </q-card-section>
                            

                            <q-card-actions class="row justify-between q-py-md">
                                <!-- RFQ Location Select -->
                                <q-select v-if="rfq_location_options.length > 1" v-model="selected_rfq_location_id"
                                    :options="rfq_location_options" @update:model-value="getHotels()"
                                    label="Select RFQ Location" data-cy="rfq-location-select"
                                    map-options emit-value option-value="id" option-label="label" outlined
                                    :class="inputColumnClasses()">
                                    <template v-slot:prepend>
                                        <q-icon name="location_on" />
                                    </template>
                                </q-select>

                                <!-- Guest Input -->
                                <q-input v-model="number_of_guests" @update:model-value="stopNegatives()"
                                    :readonly="!props.multiple_bookings" outlined label="How many guests?" type="number"
                                    data-cy="number-of-guests"
                                    :class="inputColumnClasses()">
                                    <template v-slot:prepend>
                                        <q-icon name="people" />
                                    </template>
                                </q-input>

                                <!-- Date Input -->
                                <q-input outlined :model-value="dateInput" placeholder="Please Select Dates" readonly
                                    @click="dateProxy.show()" data-cy="date" :class="inputColumnClasses()" class="border">
                                    <template v-slot:prepend>
                                        <q-icon name="event" class="cursor-pointer">
                                            <q-popup-proxy ref="dateProxy" cover transition-show="scale"
                                                transition-hide="scale">
                                                <q-date v-model="date" @update:model-value="getHotels();"
                                                    @range-end="dateProxy.hide()" title="Dates of stay" range
                                                    :options="dateOptions">
                                                    <div class="row items-center justify-end">
                                                        <q-btn v-close-popup label="Close" color="primary" flat />
                                                    </div>
                                                </q-date>
                                            </q-popup-proxy>
                                        </q-icon>
                                    </template>
                                </q-input>

                                <!-- Nights Selected Text -->
                                <q-input v-model=total_nights outlined readonly label="Nights selected"
                                    data-cy="nights-sel"
                                    :class="inputColumnClasses()">
                                    <template v-slot:prepend>
                                        <q-icon name="nights_stay" />
                                    </template>
                                </q-input>
                            </q-card-actions>
                        </q-card>

                        <q-btn v-if="props.editing" @click="getAllHotels()" label="Get All Hotels" color="blue"
                            class="q-my-md" />

                        <q-separator spaced="xl" color="primary" inset />

                        <div v-if="hotels.length > 0" class="row q-gutter-sm q-mt-md">
                            <hotel-card v-for="hotel in hotels" :hotel="hotel" :rfq_location_id="selected_rfq_location_id"
                                :editing="props.editing" :number_of_guests="number_of_guests"
                                @hotelSelected="assignHotelAndRoom" class="col-12 col-lg-6 col-xl-6 q-my-md"
                                data-cy="hotel-card" />
                        </div>
                        <q-img src="../images/adult-bookings/BookYourHotelHeader.png" fit="cover" class="q-my-md"
                            data-cy="booking-steps-banner" />
                    </q-step>

                    <!-- TODO: wiser header-nav -->
                    <q-step :name="2" title="Guest Details" icon="people" :done="step > 2">
                        <div>
                            <booking-form :rfq_location_id="selected_rfq_location_id" :hotel="selected_hotel"
                                :room="selected_room" :date="date" :number_of_guests="number_of_guests"
                                :editing="props.editing" :total_nights="total_nights" @goBack="changeStep(1)"
                                @success="populateGuests" />
                        </div>
                    </q-step>

                    <q-step :name="3" title="Payment" icon="payment" :done="step > 3">
                        <div style="width: 50%; margin: auto;">
                            <stripe-checkout :stripeURL="'/get_stripe_details'" :amount="total_cost" 
                                                :booking="booking"
                                @cancel="changeStep(2)" @success="changeStep(4)"/>
                        </div>
                    </q-step>

                    <!-- TODO: larger on screenszie and change colours to servace colours -->
                    <q-step :name="4" title="Review Confirmation" icon="recommend">
                        <booking-confirmation :rfq_location_id="selected_rfq_location_id" :hotel="selected_hotel"
                            :room="selected_room" :date="date" :guests="guests" :editing="props.editing"
                            :total_nights="total_nights" @goBack="changeStep(3)" />
                    </q-step>
                </q-stepper>
                <!-- TODO: put the banner image back in if they want it or put it as the stepper BG again -->
            </q-page>
            <div class="col-md-2 edge-col" />
        </q-page-container>
        <ServaceSlimFooter :files="footerFiles" />
    </q-layout>
</template>

<script setup>
import HotelCard from './hotel-card.vue';
import { useQuasar } from 'quasar';
import { ref } from 'vue';
import axios from 'axios';
import dayjs from 'dayjs';
import BookingForm from './booking-form.vue';
import bookingConfirmation from './booking-confirmation.vue';
import EditRichHtml from '../common/edit/edit-rich-html.vue';
import RemovableImage from '../common/image/removable-image.vue';
import ServaceSlimFooter from '../servace-core/servace-slim-footer.vue';
import StripeCheckout from '../common/stripe/checkout.vue';


const $q = useQuasar();
const props = defineProps({ isAdmin: Boolean, rfq_location_id: Number, start_date: String, end_date: String, detail: Object, editing: { type: Boolean, default: false }, multiple_bookings: { type: Boolean, default: false } });
const rfq_location_options = ref([]);
const selected_rfq_location_id = ref(props.rfq_location_id);
const step = ref(1);
const hotels = ref([]);
const date = ref();
const dateProxy = ref(null);
const number_of_guests = ref(1);
const loading = ref(false);
const selected_hotel = ref();
const selected_room = ref();
const partner_logo_url = ref('');
const welcome_image_url = ref('');
const upload_welcome_toggle = ref(false);
const detail = toRef(props.detail);
const guests = ref([]);
const booker = ref();
const company = ref();
const booking = ref();
const footerFiles = [
  { name: "Terms+and+Conditions+-+Academy", label: "Terms & Conditions" },
  { name: "Diversity+and+Equality+Policy", label: "Diversity & Equality" }
];

const inputColumnClasses = () => {
    let classes = 'col col-xs-12'

    if (rfq_location_options.value.length > 1) {
        classes = classes + ' col-md-3'
    } else {
        classes = classes + ' col-md-4'
    }

    if (isSmallScreen.value) {
        classes = classes +' q-py-xs'
    } else {
        classes = classes +' q-px-xs'
    }
    return classes;
}

getRfqLocations();
function getRfqLocations() {
    let url = '/rfq_locations/' + selected_rfq_location_id.value + '/adult_bookings/get_rfq_locations'
    axios.get(url)
    .then((response) => {
        rfq_location_options.value = response.data.rfq_locations;
    })
    .catch((error) => {
        console.log(error);
    });
}

function getHotels() {
    if (!date.value) {
        return;
    }
    hotels.value = [];
    loading.value = true;
    let url = '/rfq_locations/' + selected_rfq_location_id.value + '/adult_bookings/get_proposed_hotels'
    axios.get(url, { params: { date: date.value, number_of_guests: number_of_guests.value } })
        .then((response) => {
            hotels.value = response.data.hotels;
            if (hotels.value.length === 0) {
                $q.notify({
                    color: 'red-4',
                    textColor: 'white',
                    icon: 'error',
                    message: 'No hotels available for the selected dates'
                })
            }

        })
        .catch((error) => {
            // $q.notify({
            //     color: 'red-4',
            //     textColor: 'white',
            //     icon: 'error',
            //     message: error.response.data.message
            // })
        })
        .finally(() => {
            loading.value = false;
        });
}

function getAllHotels() {
    loading.value = true;
    let url = '/rfq_locations/' + selected_rfq_location_id.value + '/adult_bookings/get_all_hotels'
    axios.get(url)
        .then((response) => {
            hotels.value = response.data.hotels;
            if (hotels.value.length === 0) {
                $q.notify({
                    color: 'red-4',
                    textColor: 'white',
                    icon: 'error',
                    message: 'No hotels available for the selected dates'
                })
            }

        })
        .catch((error) => {
            // $q.notify({
            //     color: 'red-4',
            //     textColor: 'white',
            //     icon: 'error',
            //     message: error.response.data.message
            // })
        })
        .finally(() => {
            loading.value = false;
        });
}

getPartnerLogo();
function getPartnerLogo() {
    axios.get('/rfq_locations/' + selected_rfq_location_id.value + '/adult_bookings/get_partner_logo')
        .then((response) => {
            partner_logo_url.value = response.data.url;
        })
        .catch((error) => {
            console.log(error);
        });
}

function removePartnerLogo() {
    partner_logo_url.value = '';
}

getWelcomeImage();  
function getWelcomeImage() {
    axios.get('/rfq_locations/' + selected_rfq_location_id.value + '/adult_bookings/get_welcome_image')
        .then((response) => {
            welcome_image_url.value = response.data.url;
        })
        .catch((error) => {
            console.log(error);
        });
}

function removeWelcomeImage() {
    welcome_image_url.value = '';
}



function assignHotelAndRoom(payload) {
    selected_hotel.value = payload.hotel;
    selected_room.value = payload.room;
    changeStep(2);
}

function dateOptions(date) {
    let start_date = dayjs(props.start_date);

    // do not allow past dates
    // if (start_date < dayjs()) {
    //     start_date = dayjs()
    // }
    return date >= start_date.format('YYYY/MM/DD') && date <= dayjs(props.end_date).format('YYYY/MM/DD')
}

function changeStep(value) {
    step.value = value;
}

function updateWelcomeMessage(value) {
    detail.value.welcome_msg = value
}

function stopNegatives() {

    if (parseInt(number_of_guests.value) < 1) {
        number_of_guests.value = 1;
    }
}

function pulsateBorder() {
    if (!date.value) {
        return 'border-pulsate';
    }
    return '';
}

function populateGuests(payload) {
    guests.value = [];
    payload.guests.forEach(guest => {
        guests.value.push(guest);
    });
    booker.value = payload.booker;
    company.value = payload.company;

    createBookings();
}

function formatDateUK(date) {
    return dayjs(date).format('DD/MM/YYYY');
}

function backToRfqRequest() {
    window.location.href = '/admin/rfq_requests/' + selected_rfq_location_id.value;
}

const dateInput = computed(() => {
    if (date.value == null) {
        return '';
    }
    return dayjs(date.value.from).format('DD/MM/YY').toString() + ' - ' + dayjs(date.value.to).format('DD/MM/YY').toString();
})

const total_nights = computed(() => {
    if (date.value == null) {
        return 0;
    }
    return dayjs(date.value.to).diff(dayjs(date.value.from) - 1, 'days');
})

const total_cost = computed(() => {
    if (!selected_room.value || number_of_guests.value == 0 || total_nights.value == 0) {
        return 0;
    }
    return (parseFloat(selected_room.value.exc_trans.price) * number_of_guests.value) * total_nights.value
})

const isSmallScreen = computed(() => {
    return $q.screen.width < 1080;
});

function createBookings() {
    axios.post('/rfq_locations/' + parseInt(props.rfq_location_id) + '/adult_bookings/create_bookings', {
        rfq_proposed_hotel_id: selected_hotel.value.id,
        room_id: selected_room.value.exc_trans.room_id,
        date: date.value,
        guests: guests.value,
        booker: booker.value,
        total_nights: total_nights.value,
        total_cost: total_cost.value
    })
        .then((response) => {
            booking.value = response.data.booking;
            $q.notify({
                color: 'green-4',
                textColor: 'white',
                icon: 'cloud_done',
                message: 'Submitted'
            })
            // if paid via credit account then no card payment needed
            if (booker.value.payment_method === 'Card') {
                changeStep(3);
            } else {
                changeStep(4);
            }
        })
        .catch((error) => {
            $q.notify({
                color: 'red-4',
                textColor: 'white',
                icon: 'error',
                message: error.response.data.message
            })
            guests.value = [];
        });
}
</script>

<style scoped>
.card-header {
    border-radius: 1.875rem 1.875rem;
}

.black_and_white_image {
    -webkit-filter: grayscale(100%);
    /* Safari 6.0 - 9.0 */
    filter: grayscale(100%);
}

@keyframes border-pulsate {
    0% {
        border-color: rgb(0, 101, 114);
        border-radius: 0.25rem;
    }

    50% {
        border-color: rgba(0, 255, 255, 0);
        border-radius: 0.25rem;
    }

    100% {
        border-color: rgba(0, 101, 114, 1);
        border-radius: 0.25rem;
    }
}

.border-pulsate {
    border: 0.0625rem solid cyan;
    animation: border-pulsate 2s infinite;
}

.welcome-image-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.welcome-image {
    max-width: 100%;
    max-height: 18.75rem;
    object-fit: cover;
}

.welcome-message-container {
    background: rgba(0, 0, 0, 0.47);
}

::v-deep(.stepper-header) {
    background: url('../images/adult-bookings/ServAce-Transparent-Header-BG.png') no-repeat center center;
    background-size: cover;

}

.edge-col {
    /* Probably a bit whiter then this really but can change later */
    background-color: #efefef;
}

.q-field .q-field--readonly .q-field__control {
          border-bottom-style: solid;
}

/* :root { */
/* --q-primary: #E74B0F !important; */
/* } */
/*  */
/* .text-primary { */
/* color: var(--q-primary) !important; */
/* } */
</style>