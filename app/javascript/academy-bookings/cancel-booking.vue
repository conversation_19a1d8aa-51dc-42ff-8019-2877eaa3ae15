<template>
  <slot :onClick="onClick">
    <q-btn
      class="q-mb-sm"
      size="sm"
      color="negative"
      rounded
      label="Cancel"
      @click="onClick"
    >
      <q-tooltip anchor="bottom middle" self="top middle">
        Cancel Booking
      </q-tooltip>
    </q-btn>
  </slot>
  <q-dialog v-model="visible" persistent>
    <q-card class="bg-white text-black">
      <q-card-section class="q-pt-none">
        <div class="text-h6">Cancel Booking</div>
        <div class="q-mt-md">
          <p>Are you sure you want to cancel this booking?</p>
          <p><strong>Booking ID:</strong> {{ props.booking.id }}</p>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn label="Close" outline @click="visible = false" />
        <q-btn
          label="Cancel"
          color="negative"
          @click="cancelDialogVisible = true"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <q-dialog v-model="cancelDialogVisible" persistent>
    <q-card class="bg-white text-black">
      <q-card-section class="q-pt-none">
        <div class="text-h6">Cancel Booking</div>
        <div class="q-mt-md">
          <p>Are you sure you want to cancel this booking?</p>
          <q-input
            filled
            v-model="props.booking.cancellation_reason"
            label="Cancel Reason"
            type="textarea"
            :rows="3"
            :autofocus="true"
            :rules="[(val) => !!val || 'Cancellation reason is required']"
          />
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn label="Close" outline @click="cancelDialogVisible = false" />
        <q-btn label="Cancel" color="negative" @click="submitCancel()" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref } from "vue";
import axios from "axios";
import { useQuasar } from "quasar";
const $q = useQuasar();
const props = defineProps({
  booking: {
    type: Object,
    required: true,
  },
  isSupplier: {
    type: Boolean,
    default: false,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
});

const visible = ref(false);
const emit = defineEmits(["processed"]);
const cancelDialogVisible = ref(false);
const numberOptions = {
  style: "decimal",
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
};

function onClick() {
  visible.value = true;
}

function submitCancel() {
  if (!props.booking.cancellation_reason) {
    return;
  }
  cancelBooking();
}

function cancelBooking() {
  axios
    .patch("/academy/bookings/" + props.booking.id + "/cancel_booking", {
      cancellation_reason: props.booking.cancellation_reason,
    })
    .then((response) => {
      $q.notify({
        message: "Booking cancelled",
        color: "positive",
        position: "top",
      });
      emit("processed");
      closeAllDialogs();
    })
    .catch((error) => {
      console.error(error);
      $q.notify({
        message: "Error cancelling booking",
        color: "negative",
        position: "top",
      });
    });
}

function moneyFormatter(value) {
  // turn pence to pounds and add pound sign and formatting
  return "£" + parseFloat(value).toLocaleString("en-UK", numberOptions);
}

function closeAllDialogs() {
  visible.value = false;
  cancelDialogVisible.value = false;
}
</script>
