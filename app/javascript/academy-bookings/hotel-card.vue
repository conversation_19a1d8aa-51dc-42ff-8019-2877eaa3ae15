<template>
  <q-card class="bg-grey-1 full-width" flat bordered>
    <div>KATE HELLLLOOO</div>
    <q-card-section :horizontal="!isSmallScreen" class="q-gutter-md">
      <!-- Image Section -->
      <q-card-section class="col-xs-12 col-md-5">
        <removable-image
          :src="hotel.display_image"
          :label="'Hotel Image'"
          :upload_url="
            '/rfq_locations/' +
            props.rfq_location_id +
            '/adult_bookings/create_hotel_display_image?rfq_proposed_hotel_id=' +
            hotel.id
          "
          :remove_url="
            '/rfq_locations/' +
            props.rfq_location_id +
            '/adult_bookings/delete_hotel_display_image?rfq_proposed_hotel_id=' +
            hotel.id
          "
          :editing="editing"
          :banner_label="hotel.name"
          @imageRemoved="removeHotelImage"
          @imageUploaded="getHotelImage"
          data-cy="hotel-image"
        />
      </q-card-section>

      <!-- Description & Room Selection -->
      <q-card-section class="col-xs-6 col-sm-7">
        <div class="card-contents">
          <p class="text-h5 text-primary">{{ hotel.name }}</p>
          <q-separator spaced />
          <edit-rich-html
            v-if="editing"
            :html="hotel.booking_text"
            :save_url="
              '/admin/rfq_locations/' +
              props.rfq_location_id +
              '/adult_accomm_detail/update_proposed_hotel_description?rfq_proposed_hotel_id=' +
              hotel.id
            "
            @changeSuccessful="updateWelcomeMessage"
            data-cy="hotel-desc-editor"
          />
          <div v-html="hotel.booking_text" data-cy="hotel-desc" />
        </div>

        <div class="table-wrapper">
          <q-markup-table
            flat
            bordered
            class="q-mt-lg card-contents"
            data-cy="hotel-room-table"
          >
            <thead>
              <tr>
                <th class="text-left wrap-column">Room Type</th>
                <th class="text-left wrap-column cost-column">
                  {{
                    isSmallScreen
                      ? "Cost p/n\n-inc VAT"
                      : "Cost per night inc. VAT"
                  }}
                </th>
                <th class="text-left"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="room in hotel.rooms" :key="room.room_type">
                <td class="wrap-column">
                  {{ room.room_type }} - {{ room.pkg_type }}
                </td>
                <td class="wrap-column cost-column">
                  {{ moneyFormatter(room.exc_trans.price || 0) }}
                </td>
                <td>
                  <q-btn
                    push
                    label="Book"
                    color="blue"
                    @click="
                      $emit('hotelSelected', { hotel: hotel, room: room })
                    "
                    data-cy="hotel-book-btn"
                  />
                </td>
              </tr>
            </tbody>
          </q-markup-table>
        </div>
      </q-card-section>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { toRef, computed } from "vue";
import axios from "axios";
import { useQuasar } from "quasar";
import editRichHtml from "../common/edit/edit-rich-html.vue";
import removableImage from "../common/image/removable-image.vue";

const props = defineProps({
  hotel: Object,
  rfq_location_id: Number,
  editing: Boolean,
  number_of_guests: Number,
});

const emit = defineEmits(["hotelSelected"]);
const hotel = toRef(props.hotel);
const $q = useQuasar();

// Responsive property for small screens
const isSmallScreen = computed(() => {
  return $q.screen.width < 1080;
});

function updateWelcomeMessage(value) {
  hotel.value.booking_text = value;
}

function removeHotelImage() {
  hotel.value.display_image = null;
}

function getHotelImage() {
  axios
    .get(
      "/rfq_locations/" +
        props.rfq_location_id +
        "/adult_bookings/get_hotel_display_image",
      {
        params: { rfq_proposed_hotel_id: hotel.value.id },
      }
    )
    .then((response) => {
      hotel.value.display_image = response.data.url;
    })
    .catch(() => {
      $q.notify({
        message: "Failed to get hotel image",
        color: "negative",
        position: "top",
      });
    });
}

function moneyFormatter(value) {
  return (
    "£" +
    parseInt(value).toLocaleString("en-UK", {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  );
}
</script>

<style scoped>
.full-width {
  width: 100%;
}

.card-contents {
  width: 95%;
}

.wrap-column {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 9.375rem;
}

.cost-column {
  text-align: left;
}

@media (max-width: 450px) {
  .q-tableth,
  .q-table td {
    padding: 0.4rem;
  }
}
</style>
