<template>
    <q-card class="q-pa-md q-pt-md">
            <q-card-section>
                <h2 class="text-h6"><q-icon color="primary" size="32px" name="filters"></q-icon>Filters</h2>
            </q-card-section>
            <q-card-section>
                <div class="row q-pb-sm">
                    <q-input filled v-model="filters.booking_id" clearable label="Booking ID" class="q-mr-md q-mb-md col-2" />
                    <q-input filled v-model="filters.reservation_number" clearable label="Reservation Number" class="q-mr-md q-mb-md col-2" />
                    <q-select filled v-model="filters.programme_id" :options="programmes" label="Programme" class="q-mr-md col-2" clearable option-label="name" option-value="id" emit-value map-options />
                    <q-select v-if="!isSupplier" filled v-model="filters.hotel_id" :options="hotels" label="Hotel" class="q-mr-md col-2" clearable option-label="name" option-value="id" emit-value map-options />
                    <q-select filled v-model="filters.status" :options="statuses" label="Status" class="q-mr-md col-2" clearable option-label="name" option-value="value" emit-value map-options/>
                    <q-input filled v-model="filters.booker_surname" clearable label="Booker Surname" class="q-mr-md q-mb-md col-2" />
                    <q-input filled 
                    v-model="filters.attendee_surname" clearable label="Attendee Surname" class="q-mr-md q-mb-md col-2" />
   
                    <q-input filled v-model="filters.check_in" type="date" label="Check In Date" class="q-mr-md col-2" />
                    <q-input filled v-model="filters.check_out" type="date" label="Check Out Date" class="q-mr-md col-2" />
                </div>
                <!-- <div class="row q-pb-sm">
                   
                </div> -->
                <q-btn
                    data-cy="filterBookingsBtn" label="Filter" color="primary" class="q-mt-md"
                    @click="getBookings()" :loading="isLoading" :disable="isLoading" size="md"
                />
            </q-card-section>
    </q-card>
    <q-card class="q-pa-md q-mt-md">
        <q-card-section horizontal>
            <q-card-section>
                <h2 class="text-h6"><q-icon color="primary" size="32px" name="book_online"></q-icon>Academy Bookings List</h2>
            </q-card-section>
            <q-space></q-space>
            <q-card-section vertical>
                <p data-cy="bookingsTotal" class="text-weight-bold float-right">Found: {{ pagination.count }} bookings</p>
            </q-card-section>
        </q-card-section>

        <q-card-section>
            <div class="q-py-md q-gutter-md">
                <q-badge unelevated rounded color="grey-2" label="CONFIRMED" text-color="grey-10"
                    style="font-size: 1.2em; padding: 0.8rem;" />
                <q-badge unelevated rounded color="orange-2" label="DECLINED" text-color="grey-10"
                    style="font-size: 1.2em; padding: 0.8rem;" />
                <q-badge unelevated rounded color="red-2" label="CANCELLED" text-color="grey-10"
                    style="font-size: 1.2em; padding: 0.8rem;" />
                <q-badge unelevated rounded color="green-2" label="UNCONFIRMED" text-color="grey-10"
                    style="font-size: 1.2em; padding: 0.8rem;" />
            </div>

            <div class="q-pa-md" v-if="pagination.count > 20">
                <q-pagination data-cy="pageTabsTop" v-model="pagination.page" :max="pagination.last" :max-pages="10"
                    @update:model-value="getBookings" direction-links boundary-numbers flat color="grey"
                    active-color="primary" />
            </div>

            <q-table data-cy="bookingsTable" :rows="bookings" :columns="columns" row-key="id" :hide-pagination="true"
                wrap-cells :loading="isLoading" :rows-per-page-options="[0]" flat bordered separator="cell">

                <template v-slot:header="props">
                    <q-tr :props="props">
                        <q-th v-for="col in props.cols" @click="sortColumn(col)" :key="col.name" :props="props"
                            :style="col.sortableServer ? 'cursor: pointer' : ''">
                            <div style="display: flex; align-items: center;">
                                {{ col.label }}
                                <q-icon v-if="col.sortableServer"
                                    :name="col.sortDirection == 'asc' ? 'south' : 'north'" />
                            </div>
                        </q-th>
                    </q-tr>
                </template>

                <template v-slot:body-cell="props">
                    <q-td :props="props" :class="[rowClass(props.row)]" class="q-ma-md" style="vertical-align: top">
                        <template v-if="props.col.name === 'ref'">
                            ID:
                            <a :href="`/academy/bookings/${props.row.id}`" class="text-primary">
                                {{ props.row.id }}
                            </a> <br />
                            <p>Booker Name: {{ props.row.booker_name }}</p>
                            <p>Booker Telephone: {{ props.row.booker_telephone }}</p>
                            <p>Booker Email: {{ props.row.booker_email }}</p>
                            <p v-if="props.row.attendees.length > 1" >
                                {{ props.row.attendees.length }} Attendees
                                <br />
                                <information-display :background-color="props.row.special_requirements ? 'red' : 'primary'">
                                   <q-list dense class="q-pa-sm">
                                    <p class="text-bold no-margin">Attendee Information</p>

                                    <q-item v-for="attendee in props.row.attendees">
                                        <q-item-section>
                                            {{ attendee.forename }} {{ attendee.surname }}
                                        </q-item-section>
                                    </q-item>
                                    <q-item v-if="props.row.special_requirements">
                                        <q-item-section>
                                            <span class="text-bold text-red">Special Requirements:</span> {{ props.row.special_requirements }}
                                        </q-item-section>
                                    </q-item>
                                   </q-list>
                                </information-display>
                            </p>
                            <p v-else>
                                <p>Attendee Name: {{ props.row.attendees[0].forename }} {{ props.row.attendees[0].surname }}</p>
                                <p v-if="props.row.special_requirements">Special Requirements: {{ props.row.special_requirements || 'None' }} </p>
                            </p>
                            <p>Created: {{dayjs(props.row.created_at).format('DD/MM/YYYY HH:mm') }}</p>
                            <p v-if="props.row.reservation_number">Reservation Number: {{ props.row.reservation_number }}</p>
                        </template>
                        <template v-else-if="props.col.name === 'programme_name'">
                            {{ props.row.programme_name }}
                        </template>
                        <template v-else-if="props.col.name === 'hotel_name'">
                            {{ props.row.hotel_name }}
                        </template>
                        <template v-else-if="props.col.name === 'check_in'">
                                {{ UKFormat(props.row.check_in) }}
                        </template>
                        <template v-else-if="props.col.name === 'check_out'">
                               {{ UKFormat(props.row.check_out) }}
                        </template>
                        <template v-else-if="props.col.name === 'stay_cost'">
                            {{ moneyFormatter(props.row.stay_cost / 100) }}
                        </template>
                        <template v-else-if="props.col.name === 'confirmation_info'">
                            <q-list v-if="props.row.confirmation_info.length > 0" dense>
                                <q-item v-for="item in props.row.confirmation_info">
                                    {{ item }}
                                </q-item>
                            </q-list>
                        </template>

                        <template v-else-if="props.col.name === 'actions'">
                            <div v-if="isSupplier || isAdmin">
                                <confirm-decline-booking-dialog v-if="!(props.row.hotel_confirmed_at || props.row.hotel_declined_at)" :booking="props.row" :is-supplier="isSupplier"
                                    :is-admin="isAdmin" @processed="getBookings" />
                            </div>
                            <div v-if="!props.row.cancelled_at && !props.row.hotel_declined_at">
                                <cancel-booking-dialog v-if="!isSupplier" :booking="props.row"
                                    :is-admin="isAdmin" 
                                    @processed="getBookings" />
                            </div>
                            
                            <q-btn-dropdown v-if="isAdmin" color="primary" label="Resend" size="sm">
                                <q-list>                                    
                                    <q-item v-if="props.row.hotel_confirmed_at && !props.row.hotel_declined_at && !props.row.cancelled_at && props.row.payment_method =='BACS / Credit Card (Paid to Hotel)'" clickable v-close-popup @click="resendProforma(props.row)">
                                    <q-item-section>
                                        <q-item-label>Proforma</q-item-label>
                                    </q-item-section>
                                    </q-item>

                                    <q-item v-if="!props.row.hotel_declined_at && !props.row.cancelled_at && props.row.payment_method =='Card'" clickable v-close-popup @click="resendStripeReceipt(props.row)">
                                    <q-item-section>
                                        <q-item-label>Stripe Receipt</q-item-label>
                                    </q-item-section>
                                    </q-item>

                                    <q-item v-if="props.row.hotel_confirmed_at && !props.row.cancelled_at" clickable v-close-popup @click="resendConfirmation(props.row)">
                                    <q-item-section>
                                        <q-item-label>Confirmation</q-item-label>
                                    </q-item-section>
                                    </q-item>
                                </q-list>
                                </q-btn-dropdown>
                        </template>
                    </q-td>
                </template>

                <template v-slot:no-data="{ icon, message, filter }">
                    <div class="full-width row flex-center text-accent q-gutter-sm">
                        <q-icon size="2em" name="sentiment_dissatisfied" />
                        <span>
                            Sorry, no bookings found
                        </span>
                        <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
                    </div>
                </template>
            </q-table>

            <div class="q-pa-md" v-if="pagination.count > 20">
                <q-pagination v-model="pagination.page" :max="pagination.last" :max-pages="10"
                    @update:model-value="getBookings" direction-links boundary-numbers flat color="grey"
                    active-color="primary" />
            </div>

        </q-card-section>


    </q-card>
</template>

<script setup>
import axios from 'axios';
import { ref, inject, computed } from 'vue';
import dayjs from 'dayjs';
import confirmDeclineBookingDialog from './confirm-decline-booking.vue';
import cancelBookingDialog from './cancel-booking.vue';
import InformationDisplay from '../common/info-display/InformationDisplay.vue';

import { useQuasar } from 'quasar';
const $q = useQuasar();

const bus = inject('bus');

const bookings = ref([]);
const hotels = ref([]);
const programmes = ref([]);
const statuses = ref([
    { name: 'Confirmed', value: 'confirmed' },
    { name: 'Declined', value: 'declined' },
    { name: 'Cancelled', value: 'cancelled' },
    { name: 'Unconfirmed', value: 'unconfirmed' },
]);
const filters = ref({
    programme_id: null,
    hotel_id: null,
    status: null,
    check_in: dayjs().format('YYYY-MM-DD'),
    check_out: dayjs().add(1, 'month').format('YYYY-MM-DD'),
});

const props = defineProps({ isAdmin: Boolean, userType: String, clientId: Number, initialFilters: String, isSupplier: Boolean });

const columns = ref([
    { name: 'ref', label: 'Booking Ref', align: 'center', field: 'ref' },
    { name: 'programme_name', label: 'Programme', align: 'left', field: 'programme_name' },
    { name: 'hotel_name', label: 'Hotel', align: 'left', field: 'hotel_name' },
    { name: 'check_in', label: 'Check In', align: 'left', field: 'check_in', sortableServer: true, sortDirection: 'desc' },
    { name: 'check_out', label: 'Check Out', align: 'left', field: 'check_out', sortableServer: true, sortDirection: 'desc' },
    { name: 'stay_cost', label: 'Stay Cost', align: 'left', field: 'stay_cost' },
    { name: 'confirmation_info', label: 'Confirmations', align: 'left', field: 'confirmation_info' },
    // { name: 'booked_amend_cancel_info', label: 'Booked/ Amended/ Cancelled/ Declined/ Early Non', align: 'left', field: 'booked_amend_cancel_info' },
    { name: 'actions', label: 'Actions', align: 'center' }
])


const pagination = ref({
    page: 1,
    count: 0,
    last: 1
});

const numberOptions = {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
};

const sortUrlParams = ref('');
const filterUrlParams = ref('');
const filterUrlParamsJSON = ref({});

const isLoading = ref(false);


getBookings(true);


function sortColumn(col) {
    // Needs to do it this way for reactive purposes
    let sortableCol = columns.value.find(c => c.name == col.name);

    // Clears other columns sort direction
    let nonSortableCols = columns.value.filter(c => c.name != col.name && c.sortableServer);
    nonSortableCols.forEach(c => c.sortDirection = 'asc');

    // TODO perhaps change the direction when the server has completed the sort
    if (col.sortDirection === 'desc') {
        sortableCol.sortDirection = 'asc';
    } else {
        sortableCol.sortDirection = 'desc';
    }

    sortUrlParams.value = `&sortcol=${sortableCol.name}&sortdirection=${sortableCol.sortDirection}`;
    getBookings();
}


function rowClass(row) {
    if (row.row_class === 'declined') {
        return 'bg-orange-2';
    } else if (row.row_class === 'cancelled') {
        return 'bg-red-2';
    } else if (row.row_class === 'confirmed') {
        return 'bg-grey-2';
    } else if (row.row_class === 'unconfirmed') {
        return 'bg-green-2';
    }
}

populateFilters()
function populateFilters() {
    axios.get('/academy/bookings/populate_filters')
        .then(response => {
            hotels.value = response.data.hotels;
            programmes.value = response.data.programmes;
        })
        .catch(error => {
            console.error(error);
        });
}

function getBookings(initial = false) {
    isLoading.value = true;

    let url = "";

    if (initial && props.initialFilters) {
        // add filters into the url if they are present, at the moment this is only coming from the manager dashboard
        for (const [key, value] of Object.entries(props.initialFilters)) {
            url += '&' + key + '=' + value;
        }
    } else {
        url = "nofilter=1"
    }

    if (filterUrlParams.value) {
        url = '&' + filterUrlParams.value;
    }

    if (sortUrlParams.value) {
        url += '&' + sortUrlParams.value;
    }


    bookings.value = [];// clear the bookings array so the table is empty while loading

    axios.get('/academy/bookings?page=' + pagination.value.page + "&" + url, { 
        params: { filters: filters.value } 
    })
        .then(response => {
            bookings.value = response.data.bookings;
            pagination.value = response.data.pagy;
            isLoading.value = false;
        })
        .catch(error => {
            console.error(error);
            isLoading.value = false;
        });
}

function resendProforma(booking) {
    axios.post('/academy/bookings/' + booking.id + '/resend_proforma')
        .then(response => {
            $q.notify({
                message: 'Proforma resent',
                color: 'positive',
                position: 'top'
            });
        })
        .catch(error => {
            console.error(error);
            $q.notify({
                message: 'Error resending proforma',
                color: 'negative',
                position: 'top'
            });
        });
}

function resendStripeReceipt(booking) {
    axios.post('/academy/bookings/' + booking.id + '/resend_stripe_receipt')
        .then(response => {
            $q.notify({
                message: 'Stripe receipt resent',
                color: 'positive',
                position: 'top'
            });
        })
        .catch(error => {
            console.error(error);
            $q.notify({
                message: 'Error resending Stripe receipt',
                color: 'negative',
                position: 'top'
            });
        });
}

function resendConfirmation(booking) {
    axios.post('/academy/bookings/' + booking.id + '/resend_confirmation')
        .then(response => {
            $q.notify({
                message: 'Confirmation resent',
                color: 'positive',
                position: 'top'
            });
        })
        .catch(error => {
            console.error(error);
            $q.notify({
                message: 'Error resending confirmation',
                color: 'negative',
                position: 'top'
            });
        });
}


function UKFormat(date) {
    return dayjs(date).format('DD/MM/YYYY');
}

function moneyFormatter(value) {
  // turn pence to pounds and add pound sign and formatting
  return "£" + (parseFloat(value)).toLocaleString("en-UK", numberOptions);
}

const bulkConfirmableBookings = computed(() => {
    if (bookings.value.length == 0 || filterUrlParamsJSON.value.group_code == undefined) {
        return [];
    }
    return bookings.value.filter(booking => (booking.card_pay == false && booking.confirmed_at == null && booking.cancelled_at == null));
});
</script>

<style scoped></style>