<template>
  <q-card class="q-pb-md">
    <q-card-section horizontal>
      <q-card-section>
        <h2 data-cy="myBookingsText" class="text-h5">
          <q-icon color="primary" size="32px" name="book_online"></q-icon>My
          Bookings
        </h2>
      </q-card-section>
      <q-space></q-space>
      <q-card-section>
        <q-btn
          data-cy="showFiltersBtn"
          @click="toggleFilters"
          :label="showFilters ? 'Hide Filters' : 'Show Filters'"
          color="primary"
          :icon="showFilters ? 'remove_red_eye' : 'filter_none'"
          caption="Filters"
        />
      </q-card-section>
    </q-card-section>
    <q-card-section v-if="showFilters">
      <div class="text-h6">Filters</div>

      <div class="q-pa-md" v-if="isExporting">
        <q-linear-progress
          size="50px"
          :value="exportProgress"
          color="success"
          track-color="blue"
        >
          <div class="absolute-full flex flex-center">
            <q-badge
              v-if="exportProgress < 1"
              color="white"
              text-color="black"
              label="Please Wait For Your File To Be Generated"
            />

            <q-btn
              v-else
              color="primary"
              text-color="white"
              outline
              :label="fileName"
              :href="fileUrl"
              :download="fileName"
            >
              <q-tooltip anchor="bottom middle" self="top middle">
                Click to view file
              </q-tooltip>
            </q-btn>
          </div>
        </q-linear-progress>
      </div>

      <q-form @submit.prevent="filterBookings">
        <div class="row">
          <div class="col q-gutter-md q-ma-md">
            <q-select
              data-cy="clientDropdown"
              clearable
              @clear="clearClient"
              @update:model-value="changeClient"
              option-label="name"
              option-value="id"
              outlined
              dense
              v-model="form.client_id"
              :options="filter_data.clients"
              emit-value
              map-options
              label="Client"
              v-show="props.isAdmin"
            />

            <q-select
              data-cy="programmeDropdown"
              clearable
              @clear="clearRfqProgramme"
              @update:model-value="changeRfqProgramme"
              option-label="name"
              option-value="id"
              outlined
              dense
              v-model="form.rfq_programme_id"
              emit-value
              map-options
              :options="filter_data.programmes"
              label="Rfq programme"
            />

            <q-select
              data-cy="courseName"
              clearable
              outlined
              dense
              v-model="form.course_name_like"
              :options="filter_data.course_names"
              label="Course Name"
            />

            <q-select
              data-cy="buDropdown"
              clearable
              option-label="name"
              option-value="id"
              outlined
              dense
              v-model="form.business_unit"
              :options="filter_data.business_units"
              emit-value
              map-options
              label="Business unit"
            />

            <q-select
              data-cy="groupCodeDropdown"
              outlined
              dense
              v-model="form.group_code"
              clearable
              :options="filter_data.group_codes"
              emit-value
              map-options
              label="Group Code"
              :hint="form.rfq_programme_id ? '' : 'Select Programme First'"
            />

            <q-input
              data-cy="groupBookingID"
              outlined
              dense
              v-model="form.booking_reference"
              label="Group Booking ID"
            />
            <q-input
              data-cy="bookingID"
              outlined
              dense
              v-model="form.booking_id"
              label="Booking ID"
            />
            <q-select
              data-cy="bookingTypeDropdown"
              outlined
              dense
              v-model="form.booking_type"
              :options="bookingTypes"
              label="Booking type"
            />

            <q-select
              data-cy="rfqTypeDropdown"
              outlined
              dense
              v-model="form.rfq_type"
              :options="rfqTypes"
              v-if="!props.isSupplier"
              label="Rfq type"
            />

            <q-select
              data-cy="jiDropdown"
              clearable
              option-label="name"
              option-value="id"
              outlined
              dense
              v-model="form.joining_instruction_id"
              :options="filter_data.joining_instructions"
              emit-value
              map-options
              :hint="form.rfq_programme_id ? '' : 'Select Programme First'"
              v-if="!isSupplier"
              label="Joining Instruction"
            />
          </div>

          <div class="col q-gutter-md q-ma-md">
            <q-select
              data-cy="virtualTrackingDropdown"
              outlined
              dense
              v-model="form.click"
              v-if="!isSupplier"
              :options="virtualLinkOptions"
              label="Virtual Link Tracking"
            />

            <q-input
              data-cy="checkinStart"
              outlined
              dense
              v-model="form.checkin_start"
              label="Checkin start"
              type="date"
              clearable
            />
            <q-input
              data-cy="checkinEnd"
              outlined
              dense
              v-model="form.checkin_end"
              label="Checkin end"
              type="date"
              clearable
            />
            <!-- <q-select
              data-cy="requestYearDropdown"
              outlined
              dense
              v-model="form.checkin_year"
              label="Request Year"
              :options="requestYears"
              emit-value
              map-options
            /> -->
            <q-input
              data-cy="createdAfterDate"
              outlined
              dense
              v-model="form.created_on_after"
              label="Created on after"
              type="date"
              clearable
            />
            <q-input
              data-cy="cancelledAmendedAfterDate"
              outlined
              dense
              v-model="form.amended_after"
              label="Cancelled or Amended after"
              type="date"
              clearable
            />

            <q-select
              data-cy="lateBookedDropdown"
              clearable
              outlined
              dense
              v-model="form.late_booked"
              label="Late booked"
              :options="lateBookedOptions"
            />
            <q-select
              data-cy="epaBookingDropdown"
              clearable
              outlined
              dense
              v-model="form.epa_booking"
              label="Epa booking"
              :options="epaBookingOptions"
            />
            <q-select
              data-cy="tagsDropdown"
              clearable
              outlined
              dense
              v-model="form.tags"
              v-if="!isSupplier"
              label="Tags"
              :options="filter_data.tags"
              option-label="name"
              :hint="form.client_id ? '' : 'Select Client First'"
              option-value="id"
              emit-value
              multiple
              map-options
            />
          </div>

          <div class="col q-gutter-md q-ma-md">
            <q-select
              data-cy="hotelDropdown"
              clearable
              v-model="form.hotel_id"
              option-label="name"
              option-value="id"
              :options="filter_data.hotels"
              label="Hotel"
              class="q-mb-md"
              outlined
              v-if="(!isSupplier || isChain)"
              dense
              emit-value
              map-options
            />

            <q-select
              data-cy="hotelConfirmedDropdown"
              clearable
              outlined
              dense
              v-model="form.hotel_confirmed"
              label="Hotel confirmed"
              :options="hotelConfirmedOptions"
            />

            <q-select
              data-cy="roomTypeDropdown"
              clearable
              v-model="form.room_type"
              :options="filter_data.room_types"
              label="Room Type"
              class="q-mb-md"
              outlined
              dense
            />
            <q-input
              data-cy="surnameField"
              v-model="form.person_surname_like"
              label="Person Surname"
              class="q-mb-md"
              outlined
              dense
            />
            <q-select
              data-cy="personTypeDropdown"
              clearable
              v-model="form.person_type"
              :options="personTypes"
              label="Person Type"
              class="q-mb-md"
              emit-value
              map-options
              outlined
              dense
            />
            <q-select
              data-cy="paymentMethodDropdown"
              clearable
              v-model="form.payment_method"
              :options="paymentMethods"
              label="Payment Method"
              class="q-mb-md"
              outlined
              use-input
              input-debounce="0"
              dense
            />
            <q-select
              data-cy="paymentStatusDropdown"
              clearable
              v-model="form.payment_status"
              :options="paymentStatuses"
              label="Payment Status"
              class="q-mb-md"
              outlined
              use-input
              input-debounce="0"
              dense
            />
            <q-input
              data-cy="managerField"
              v-model="form.manager_like"
              label="Manager"
              class="q-mb-md"
              outlined
              dense
            />
            <q-checkbox
              v-if="!props.isSupplier"
              data-cy="onlySubsistence"
              v-model="form.subsistence_only"
              label="Only Subsistence"
            />
            <q-checkbox
              v-if="!props.isSupplier"
              data-cy="withoutSubsistence"
              v-model="form.without_subsistence"
              label="Without Subsistence"
            />
          </div>
        </div>
        <div class="row">
          <div class="col"></div>
          <div class="col">

            <q-btn
              data-cy="filterBookingsBtn"
              class="float-right q-mx-md"
              label="Filter Bookings"
              color="primary"
              type="submit"
            />
            
            <q-btn
              data-cy="exportToExcelBtn"
              class="float-right"
              label="Export to Excel"
              color="green"
              @click="exportToExcel"
            />

            <q-btn
              v-if="props.isAdmin || props.isSupplier"
              data-cy="exportHotelInvoicelBtn"
              class="float-right q-mr-md"
              label="Export Hotel Invoice Data"
              color="green"
              @click="exportHotelInvoice"
            />

            <q-btn
              v-if="!props.isAdmin && !props.isSupplier"
              data-cy="notifyLearnerBtn"
              :disable="disableNotify"
              class="float-right q-mx-md"
              label="Notify Learners"
              color="orange"
              @click="notifyLearners"
            >
              <q-tooltip
                v-if="disableNotify"
                anchor="bottom middle"
                self="top middle"
              >
                Select programme, and hotel first, as well as any additional
                filters to enable</q-tooltip
              >
            </q-btn>
          </div>
        </div>
      </q-form>
    </q-card-section>

    <q-card-section v-if="showFilters">
      <div class="text-h6">Quick Filters</div>

      <div class="q-pa-md row items-start q-gutter-md">
        <q-card class="">
          <q-card-section>
            <div class="text-h6">Trainers</div>
          </q-card-section>

          <q-separator />

          <q-card-section>
            <q-btn
              data-cy="declinedQF"
              @click="quickFilter('declined')"
              label="Declined - Not Resolved"
            />
            <q-btn
              data-cy="trainerHotelNotConfirmedQF"
              @click="quickFilter('not_confirmed')"
              label="Hotel Not Confirmed"
            />
          </q-card-section>
        </q-card>

        <q-card class="my-card">
          <q-card-section>
            <div class="text-h6">Apprentices</div>
          </q-card-section>

          <q-separator />

          <q-card-section>
            <q-btn
              v-if="props.isAdmin"
              data-cy="managerNotConfirmedQF"
              @click="quickFilter('manager_not_confirmed')"
              label="Client Manager Not Confirmed - 14 days to go"
            />
            <q-btn
              data-cy="learnerDeclinedQF"
              @click="quickFilter('learner_declined')"
              label="Learner Declined Booking"
            />
            <q-btn
              data-cy="apprenticeHotelNotConfirmedQF"
              @click="quickFilter('hotel_not_confirmed')"
              label="Hotel Not Confirmed - 6 weeks to go"
            />
            <q-btn
              data-cy="urgentBookingsQF"
              v-if="props.isAdmin"
              @click="quickFilter('bookings_not_confirmed')"
              label="Urgent Bookings Not Confirmed"
            />
          </q-card-section>
        </q-card>
      </div>
      <q-separator />
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, inject, onBeforeUnmount, computed } from "vue";
import { useQuasar } from "quasar";

import axios from "axios";
import dayjs from "dayjs";

import omitBy from "lodash/omitBy";

const bus = inject("bus");

const loaded = ref(false);

const $q = useQuasar();

const props = defineProps({
  isAdmin: Boolean,
  isChain: Boolean,
  clientId: Number,
  initialFilters: Object,
  isSupplier: Boolean,
});

const startOfMonth = dayjs().startOf("month").format("YYYY-MM-DD");
const endOfMonth = dayjs().endOf("month").format("YYYY-MM-DD");

const isExporting = ref(false);
const exportProgress = ref(0);
const backDropId = ref(null);
const timeoutId = ref(null);
const fileName = ref("");
const fileUrl = ref("");

const formBaseValues = {
  client_id: null,
  rfq_programme_id: null,
  course_name_like: "",
  business_unit: null,
  group_code: null,
  booking_reference: null,
  booking_id: "",
  booking_type: null,
  rfq_type: "All",
  click: "All",
  subsistence_only: false,
  without_subsistence: false,
  checkin_start: startOfMonth,
  checkin_end: endOfMonth,
  checkin_year: "",
  created_on_after: "",
  cancelled_or_amended_after: "",
  hotel_confirmed: "",
  late_booked: "",
  epa_booking: "",
  tags: [],
  hotel_id: null,
  room_type: null,
  person_surname_like: "",
  person_type: null,
  payment_method: null,
  manager_like: "",
  joining_instruction_id: null,
};

const form = ref(formBaseValues);

const filter_data = ref({
  clients: [],
  programmes: [],
  businessUnits: [],
  courseNames: [],
});

const hotelConfirmedOptions = [
  "Confirmed",
  "Declined (Resolved)",
  "Declined (Unresolved)",
  "Unprocessed",
];
const lateBookedOptions = ["Not Late Booked", "Late Booked"];
const epaBookingOptions = ["EPA", "Non-EPA"];

const rfqTypes = ["All", "Virtual", "Residential"];
const virtualLinkOptions = ["All", "Clicked", "Not Clicked"];
const bookingTypes = [
  "All",
  "Confirmed - Paid",
  "Not Confirmed - Unpaid",
  "Provisional",
  "Provisional - No Name",
  "Provisional - Name",
  "Amended",
  "Cancelled",
  "Not Cancelled",
  "Declined - Not Resolved",
  "Declined - Resolved",
];

const start = dayjs().subtract(5, "year").year();
const end = dayjs().add(2, "year").year();

// const requestYears = [];
// for (let i = start; i <= end; i++) {
//   requestYears.push({ label: i, value: i });
// }

const disableNotify = computed(() => {
  return !form.value.rfq_programme_id || !form.value.hotel_id;
});

const personTypes = [
  { label: "Adult Learners", value: "ADU" },
  { label: "Apprentice Learners", value: "APP" },
  { label: "All Learners", value: "LEA" },
  { label: "Provisional", value: "PRO" },
  { label: "Provisional and Learners", value: "LEA & PRO" },
  { label: "Trainers", value: "TRA" },
];

const filterAdditionalUrlParams = ref("");

if (!props.isAdmin && props.clientId) {
  // Don't set form.value.client_id too early
  // form.value.client_id = props.clientId;
  filterAdditionalUrlParams.value = `?client_id=${props.clientId}`;
}

const paymentMethods = [
  "Stripe Card",
  "Stripe BACS",
  "Stripe Card/BACS",
  "Non Stripe",
  "Credit Account",
];

const paymentStatuses = ["Pending", "Not Pending", "Failed"];

const showFilters = ref(false);

const toggleFilters = () => {
  showFilters.value = !showFilters.value;
  if (showFilters.value) {
    getFilterData();
  }
};

function exportToExcel() {
  let urlCleaned = convertFormToQueryParams();
  let url = "/apprentice/bookings/export?" + urlCleaned;
  doExport(url);
}

function exportHotelInvoice() {
  let urlCleaned = convertFormToQueryParams();
  let url = "/apprentice/bookings/export_hotel_invoice_data?" + urlCleaned;
  doExport(url);
}

const filterBookings = () => {
  let urlCleaned = convertFormToQueryParams();
  bus.emit("filter-bookings", urlCleaned);
};

const notifyLearners = () => {
  let urlCleaned = convertFormToQueryParams();
  bus.emit("notify-learners", urlCleaned);
};

function convertFormToQueryParams() {
  let queryParams = form.value;
  const urlParams = new URLSearchParams(omitBy(queryParams, omitValue));
  let urlCleaned = urlParams.toString();
  return urlCleaned;
}

function omitValue(val) {
  return val == "" || val == null || val == [];
}

function clearClient() {
  // Handle client clear
  form.value.rfq_programme_id = null;
  form.value.business_unit = null;
  form.value.hotel_id = null;
  form.value.joining_instruction_id = null;
  filterAdditionalUrlParams.value = "";
  changeClient(null);
}

function clearRfqProgramme() {
  // Handle RFQ Programme clear
  form.value.business_unit = null;
  form.value.group_code = null;
  form.value.hotel_id = null;
  form.value.joining_instruction_id = null;
  changeRfqProgramme(null);
}

function changeClient(client) {
  // Handle client change
  if (!client) {
    filterAdditionalUrlParams.value = "";
  } else {
    filterAdditionalUrlParams.value = `?client_id=${client}`;
  }

  form.value.rfq_programme_id = null;
  form.value.business_unit = null;
  form.value.hotel_id = null;
  // Add debounce in case of multiple requests in quick succession

  getFilterData(true);
}

function changeRfqProgramme(rfqProgramme) {
  // Handle RFQ Programme change
  filterAdditionalUrlParams.value = "";

  if (form.value.client_id) {
    filterAdditionalUrlParams.value += `?client_id=${form.value.client_id}`;
  } else {
    filterAdditionalUrlParams.value = "";
  }

  if (rfqProgramme) {
    if (form.value.client_id) {
      filterAdditionalUrlParams.value += `&rfq_programme_id=${rfqProgramme}`;
    } else {
      filterAdditionalUrlParams.value = `?rfq_programme_id=${rfqProgramme}`;
    }
  }

  form.value.business_unit = null;
  form.value.group_code = null;
  form.value.hotel_id = null;

  // Add debounce in case of multiple requests in quick succession

  getFilterData(true);
}

function getFilterData(refilter = false) {
  if (!refilter && loaded.value) {
    return;
  }

  $q.loading.show();

  var url = "/apprentice/bookings/filter_data";
  var url = url + filterAdditionalUrlParams.value;

  // var startTime = performance.now();

  axios
    .get(url)
    .then((response) => {
      filter_data.value = response.data.filter_data;

      loaded.value = true;

      if (!props.isAdmin && props.clientId) {
        // Set here after the data has loaded
        form.value.client_id = props.clientId;
      }

      // var endTime = performance.now()
      // alert('Time taken: ' + (endTime - startTime) + 'ms');

      $q.loading.hide();
    })
    .catch((error) => {
      console.error(error);
      $q.loading.hide();
    });
}

function quickFilter(type) {
  // Clears any existing filters
  // form.value = JSON.parse(JSON.stringify(formBaseValues));

  form.value.client_id = null;
  form.value.rfq_programme_id = null;
  form.value.course_name_like = "";
  form.value.business_unit = null;
  form.value.group_code = null;
  form.value.booking_reference = null;
  form.value.booking_id = "";
  form.value.booking_type = null;
  form.value.rfq_type = "All";
  form.value.click = "All";
  form.value.subsistence_only = false;
  form.value.without_subsistence = false;
  form.value.checkin_start = startOfMonth;
  form.value.checkin_end = endOfMonth;
  form.value.checkin_year = "";
  form.value.created_on_after = "";
  form.value.cancelled_or_amended_after = "";
  form.value.hotel_confirmed = "";
  form.value.late_booked = "";
  form.value.epa_booking = "";
  form.value.tags = [];
  form.value.hotel_id = null;
  form.value.room_type = null;
  form.value.person_surname_like = "";
  form.value.person_type = null;
  form.value.payment_method = null;
  form.value.manager_like = "";
  form.value.joining_instruction_id = null;
  form.value.payment_status = null;
  form.value.booking_type= null;

  switch (type) {
    case "declined":
      form.value.booking_type = "Declined - Not Resolved";
      form.value.checkin_start = dayjs().format("YYYY-MM-DD");
      form.value.checkin_end = dayjs().add(1, "month").format("YYYY-MM-DD");
      form.value.person_type = "TRA";
      break;

    case "not_confirmed":
      form.value.confirmation_like = "trainer hotel not confirmed";
      form.value.checkin_start = dayjs().format("YYYY-MM-DD");
      form.value.checkin_end = dayjs().add(6, "weeks").format("YYYY-MM-DD");
      form.value.hotel_confirmed = "Unprocessed";
      form.value.person_type = "TRA";
      break;

    case "manager_not_confirmed":
      form.value.person_type = "LEA & PRO";
      form.value.checkin_start = dayjs().format("YYYY-MM-DD");
      form.value.checkin_end = dayjs().add(14, "days").format("YYYY-MM-DD");
      form.value.booking_type = "Not Confirmed - Unpaid";
      form.value.without_subsistence = true;
      form.value.rfq_type = "Residential";
      break;

    case "learner_declined":
      form.value.person_type = "LEA & PRO";
      form.value.checkin_start = dayjs().format("YYYY-MM-DD");
      form.value.checkin_end = dayjs().add(1, "month").format("YYYY-MM-DD");
      form.value.hotel_confirmed = "Declined (Unresolved)";
      form.value.without_subsistence = true;
      form.value.rfq_type = "Residential";
      break;

    case "hotel_not_confirmed":
      form.value.person_type = "LEA & PRO";
      form.value.checkin_start = dayjs().format("YYYY-MM-DD");
      form.value.checkin_end = dayjs().add(6, "weeks").format("YYYY-MM-DD");
      form.value.booking_type = "Not Confirmed - Unpaid";
      form.value.without_subsistence = true;
      form.value.rfq_type = "Residential";
      form.value.hotel_confirmed = "Unprocessed";
      break;

    case "bookings_not_confirmed":
      form.value.person_type = "LEA & PRO";
      form.value.late_booked = "Late Booked";
      form.value.booking_type = "Not Confirmed - Unpaid";
      form.value.without_subsistence = true;
      form.value.rfq_type = "Residential";
      form.value.hotel_confirmed = "Unprocessed";
      break;
  }

  filterBookings();
}

function doExport(url) {
  clearExportVariables();

  axios
    .get(url)
    .then((response) => {
      isExporting.value = true;
      exportProgress.value = parseInt(response.data.progress || 0);
      backDropId.value = response.data.backdrop_id;

      timeoutId.value = setInterval(function () {
        pollForExportFile();
      }, 5000);
    })
    .catch((error) => {
      console.error(error);
    });
}

function clearExportVariables() {
  isExporting.value = false;
  exportProgress.value = 0;
  backDropId.value = null;
  fileName.value = "";
  fileUrl.value = "";
}

function pollForExportFile() {
  axios
    .get("/apprentice/bookings/" + backDropId.value + "/poll_for_export/")
    .then((response) => {
      // progress is 0-100 from backend, convert to 0-1 for q-linear-progress
      exportProgress.value = Math.min(Math.max((response.data.progress || 0) / 100, 0), 1);
      if (response.data.fileUrl) {
        clearInterval(timeoutId.value);
        fileName.value = response.data.fileName;
        fileUrl.value = response.data.fileUrl;
        exportProgress.value = 1;
      }
    })
    .catch((error) => {
      clearInterval(timeoutId.value);
    });
}

if (props.initialFilters) {
  setInitialFilters();
}
function setInitialFilters() {
  if (props.initialFilters) {
    for (const [key, value] of Object.entries(props.initialFilters)) {
      form.value[key] = value;
    }
  }
}

onBeforeUnmount(() => {
  clearInterval(timeoutId.value);
});
</script>

<style scoped>
.q-banner {
  margin-top: 16px;
}
</style>
