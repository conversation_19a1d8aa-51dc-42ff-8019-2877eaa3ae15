<template>
    <q-card class="q-pa-md">
        <q-card-section horizontal>
            <q-card-section>
                <h2 data-cy="myBookingsText" class="text-h5"><q-icon color="primary" size="32px"
                        name="book_online"></q-icon>Conference Bookings</h2>
            </q-card-section>
            <q-space></q-space>
            <q-card-section>
                <q-btn data-cy="showFiltersBtn" @click="toggleFilters"
                    :label="showFilters ? 'Hide Filters' : 'Show Filters'" color="primary"
                    :icon="showFilters ? 'remove_red_eye' : 'filter_none'" caption="Filters" />
            </q-card-section>
        </q-card-section>
        <q-card-section v-if="showFilters">
            <div class="text-h6">Filters</div>

            <div class="q-pa-md" v-if="isExporting">
                <q-linear-progress size="50px" :value="exportProgress" color="success" track-color="blue">
                    <div class="absolute-full flex flex-center">
                        <q-badge v-if="exportProgress < 1" color="white" text-color="black"
                            label="Please Wait For Your File To Be Generated" />

                        <q-btn v-else color="primary" text-color="white" outline :label="fileName" :href="fileUrl"
                            :download="fileName">
                            <q-tooltip anchor="bottom middle" self="top middle">
                                Click to view file
                            </q-tooltip>
                        </q-btn>
                    </div>
                </q-linear-progress>
            </div>

            <q-form @submit.prevent="filterBookings">
                <div class="row">
                    <div class="col q-gutter-md q-ma-md">

                        <q-select data-cy="headerTypeDropdown" v-model="form.header_type"
                            :options="['All', 'Group', 'Adult', 'Conferma']" label="Header Type" class="q-mb-md" outlined
                            dense />

                        <q-select data-cy="clientDropdown" clearable @clear="clearClient"
                            @update:model-value="changeClient" option-label="name" option-value="id" outlined dense
                            v-model="form.client_id" :options="filter_data.clients" emit-value map-options
                            label="Client" v-show="props.isAdmin" />

                        <q-input data-cy="bookingRef" outlined dense v-model="form.booking_reference"
                            label="Booking Reference" />

                        <q-input data-cy="reservationNo" outlined dense v-model="form.reservation_number"
                            label="Reservation Number" />


                        <q-select data-cy="bookingTypeDropdown" v-model="form.booking_type"
                            :options="['Confirmed', 'Not Confirmed', 'Provisional', 'Amended', 'Cancelled', 'Declined - Not Resolved', 'Declined - Resolved']" label="Booking Type" class="q-mb-md" outlined
                            dense />

                        <!-- <q-select data-cy="confirmationLikeDropdown" clearable v-model="form.confirmation_like"
                            :options= "['Hotel Confirmed', 'Hotel Not Confirmed']" label="Confirmation Like" class="q-mb-md" outlined
                            dense /> -->
                    </div>

                    <div class="col q-gutter-md q-ma-md">
                        <q-input data-cy="checkinStart" outlined dense v-model="form.checkin_start"
                            label="Checkin start" type="date" clearable />
                        <q-input data-cy="checkinEnd" outlined dense v-model="form.checkin_end" label="Checkin end"
                            type="date" clearable />

                        <q-input data-cy="cancelledAmendedAfterDate" outlined dense v-model="form.amended_after"
                            label="Cancelled or Amended after" type="date" clearable />

                        <q-input data-cy="createdAfterDate" outlined dense v-model="form.created_on_after"
                            label="Booked After" type="date" clearable />

                        <q-input data-cy="createdBeforeDate" outlined dense v-model="form.created_on_before"
                            label="Booked before" type="date" clearable />
                    </div>

                    <div class="col q-gutter-md q-ma-md">
                        <q-select data-cy="hotelDropdown" clearable v-model="form.hotel_id" option-label="name"
                            option-value="id" :options="filter_data.hotels" label="Hotel" class="q-mb-md" outlined dense
                            emit-value map-options />

                        <!-- <q-select data-cy="hotelConfirmedDropdown" clearable outlined dense
                            v-model="form.hotel_confirmed" label="Hotel confirmed" :options="hotelConfirmedOptions" /> -->

                        <q-select data-cy="roomTypeDropdown" clearable v-model="form.room_type"
                            :options="filter_data.room_types" label="Room Type" class="q-mb-md" outlined dense />

                        <q-input data-cy="surnameField" v-model="form.person_surname_like" label="Person Surname"
                            class="q-mb-md" outlined dense />

                        <q-select data-cy="personTypeDropdown" clearable v-model="form.person_type"
                            :options="personTypes" label="Person Type" class="q-mb-md" emit-value map-options outlined
                            dense />

                        <q-select data-cy="paymentTypeDropdown" clearable v-model="form.payment_type"
                            :options=" ['HG to settle', 'Client to settle']" label="Payment Status" class="q-mb-md" outlined use-input
                            input-debounce="0" dense />

                    </div>
                </div>
                <div class="row">
                    <div class="col"></div>
                    <div class="col">
                        <q-btn data-cy="filterBookingsBtn" class="float-right q-mx-md" label="Filter Bookings"
                            color="primary" type="submit" />
                        <q-btn data-cy="exportToExcelBtn" class="float-right" label="Export to Excel" color="green"
                            @click="exportToExcel" />
                    </div>
                </div>
            </q-form>
        </q-card-section>

        <q-card-section v-if="showFilters">
            <div class="text-h6">Quick Filters</div>

            <div class="q-pa-md row items-start q-gutter-md">
                <q-card class="">
                    <q-card-section>
                        <div class="text-h6">Group</div>
                    </q-card-section>

                    <q-separator />

                    <q-card-section>
                        <q-btn data-cy="declinedQF" @click="quickFilter('declined')" label="Declined - Not Resolved" />
                        <q-btn data-cy="notConfirmedQF" @click="quickFilter('not_confirmed')" label="Not Confirmed" />
                        <q-btn data-cy="notConfirmedQF" @click="quickFilter('no_names_added')" label="No Names Added 21 Days to Go" />
                    </q-card-section>
                </q-card>

                <q-card class="my-card">
                    <q-card-section>
                        <div class="text-h6">Adult</div>
                    </q-card-section>

                    <q-separator />

                    <q-card-section>
                        <q-btn v-if="props.isAdmin" data-cy="adultNotConfirmed"
                            @click="quickFilter('adult_not_confirmed')"
                            label="Not Confirmed" />
                        <q-btn data-cy="pendingAdultBookings" @click="quickFilter('pending_adult_bookings')"
                            label="Pending Adult Bookings 14 Days to Go" />
                    </q-card-section>
                </q-card>
            </div>
            <q-separator />
        </q-card-section>
    </q-card>
</template>

<script setup>
import { ref, inject, onBeforeUnmount, computed } from 'vue';
import { useQuasar } from 'quasar';

import axios from 'axios';
import dayjs from 'dayjs';

import omitBy from 'lodash/omitBy';

const bus = inject('bus');

const loaded = ref(false);

const $q = useQuasar();

const props = defineProps({ isAdmin: Boolean, clientId: Number, initialFilters: Object });

const startOfMonth = dayjs().startOf('month').format('YYYY-MM-DD');
const endOfMonth = dayjs().endOf('month').format('YYYY-MM-DD');

const isExporting = ref(false);
const exportProgress = ref(0);
const backDropId = ref(null);
const timeoutId = ref(null);
const fileName = ref("");
const fileUrl = ref("");

const formBaseValues = {
    header_type: 'All',
    client_id: null,
    booking_reference: null,
    reservation_number: null,
    confirmation_like: null,
    booking_type: null,
    rfq_type: 'All',
    click: 'All',
    checkin_start: startOfMonth,
    checkin_end: endOfMonth,
    checkin_year: '',
    created_on_after: null,
    created_on_after: null,
    // cancelled_or_amended_after: '',
    hotel_confirmed: null,
    late_booked: '',
    hotel_id: null,
    room_type: null,
    person_surname_like: '',
    person_type: null,
    payment_type: null,
};

const form = ref(formBaseValues);

const filter_data = ref({
    clients: [],
    programmes: [],
    businessUnits: [],
    courseNames: [],
});

const hotelConfirmedOptions = ["Confirmed", "Declined (Resolved)", "Declined (Unresolved)", "Unprocessed"];

const personTypes = [
    { label: 'Named', value: 'AGB' },
    { label: 'Provisional', value: 'PGB' }
]

const filterAdditionalUrlParams = ref('');

if (!props.isAdmin && props.clientId) {
    // Don't set form.value.client_id too early
    // form.value.client_id = props.clientId;
    filterAdditionalUrlParams.value = `?client_id=${props.clientId}`;
}

// const paymentMethods = [
//     'Stripe Card',
//     'Stripe BACS',
//     'Stripe Card/BACS',
//     'Non Stripe',
//     'Credit Account'
// ]

const paymentStatuses = [
    'Pending',
    'Not Pending',
    'Failed'
]

const showFilters = ref(false);

const toggleFilters = () => {
    showFilters.value = !showFilters.value;
    if (showFilters.value) {
        getFilterData();
    }
};

function exportToExcel() {
    let urlCleaned = convertFormToQueryParams();
    doExport(urlCleaned);

}

const filterBookings = () => {
    let urlCleaned = convertFormToQueryParams()
    bus.emit('filter-bookings', urlCleaned)
}

const notifyLearners = () => {
    let urlCleaned = convertFormToQueryParams()
    bus.emit('notify-learners', urlCleaned)
}


function convertFormToQueryParams() {
    let queryParams = form.value
    const urlParams = new URLSearchParams(omitBy(queryParams, omitValue));
    let urlCleaned = urlParams.toString();
    return urlCleaned;
}

function omitValue(val) {
    return (val == '' || val == null || val == [])
}

function clearClient() {
    // Handle client clear
    form.value.hotel_id = null
    filterAdditionalUrlParams.value = '';
    changeClient(null);
}


function changeClient(client) {
    // Handle client change
    if (!client) {
        filterAdditionalUrlParams.value = '';
    } else {
        filterAdditionalUrlParams.value = `?client_id=${client}`;
    }

    form.value.hotel_id = null
    // Add debounce in case of multiple requests in quick succession

    getFilterData(true);
}

function getFilterData(refilter = false) {
    if (!refilter && loaded.value) {
        return;
    }

    $q.loading.show();

    var url = '/conferences_new/bookings/filter_data';
    var url = url + filterAdditionalUrlParams.value;

    // var startTime = performance.now();

    axios.get(url)
        .then(response => {
            filter_data.value = response.data.filter_data;
            loaded.value = true;

            if (!props.isAdmin && props.clientId) {
                // Set here after the data has loaded
                form.value.client_id = props.clientId;
            }

            $q.loading.hide();
        })
        .catch(error => {
            console.error(error);
            $q.loading.hide();
        });
}

function quickFilter(type) {
    switch (type) {
        case 'declined':
            form.value.header_type = 'Group';
            form.value.checkin_start = dayjs().format('YYYY-MM-DD');
            form.value.checkin_end = dayjs().add(6, 'weeks').format('YYYY-MM-DD');
            form.value.booking_type = 'Declined - Not Resolved';
            break;

        case 'not_confirmed':
            form.value.booking_type = 'Not Confirmed'
            form.value.header_type = 'Group';
            form.value.checkin_start = dayjs().format('YYYY-MM-DD');
            form.value.checkin_end = dayjs().add(6, 'weeks').format('YYYY-MM-DD');
            form.value.confirmation_like = 'Hotel Not Confirmed';
            break;

        case 'no_names_added':
            form.value.booking_type = 'Not Confirmed';
            form.value.person_type = 'PGB';
            form.value.checkin_start = dayjs().format('YYYY-MM-DD');
            form.value.checkin_end = dayjs().add(21, 'days').format('YYYY-MM-DD');
            break;

        case 'adult_not_confirmed':
            form.value.header_type = 'Adult';
            form.value.checkin_start = dayjs().format('YYYY-MM-DD');
            form.value.checkin_end = dayjs().add(6, 'weeks').format('YYYY-MM-DD');
            form.value.booking_type = 'Not Confirmed';
            break;

        case 'pending_adult_bookings':
            form.value.header_type = 'Adult';
            form.value.booking_type = 'Not Confirmed';
            form.value.checkin_start = dayjs().format('YYYY-MM-DD');
            form.value.checkin_end = dayjs().add(14, 'days').format('YYYY-MM-DD');
            break;
    }

    filterBookings();
}

function doExport(urlCleaned) {
    let url = '/conferences_new/bookings/export?' + urlCleaned;

    clearExportVariables();

    axios.get(url)
        .then(response => {
            isExporting.value = true;
            exportProgress.value = parseInt(response.data.progress || 0);
            backDropId.value = response.data.backdrop_id
            pollForExportFile();

            timeoutId.value = setInterval(function () {
                pollForExportFile();
            }, 5000);

        })
        .catch(error => {
            console.error(error);
        });
}

function clearExportVariables() {
    isExporting.value = false;
    exportProgress.value = 0;
    backDropId.value = null;
    fileName.value = "";
    fileUrl.value = "";
}

function pollForExportFile() {
    axios.get("/conferences_new/bookings/" + backDropId.value + "/poll_for_export/")
        .then(response => {
            // progress is 0-100 from backend, convert to 0-1 for q-linear-progress
            exportProgress.value = Math.min(Math.max((response.data.progress || 0) / 100, 0), 1);
            if (response.data.fileUrl) {
                fileName.value = response.data.fileName;
                fileUrl.value = response.data.fileUrl;
                exportProgress.value = 1;
                clearInterval(timeoutId.value);
            }
        })
        .catch(error => {
            clearInterval(timeoutId.value);
            console.error(error);
        });
}

if (props.initialFilters) {
    setInitialFilters();
}
function setInitialFilters() {
    if (props.initialFilters) {
        for (const [key, value] of Object.entries(props.initialFilters)) {
            form.value[key] = value;
        }
    }
}

onBeforeUnmount(() => {
    clearInterval(timeoutId.value);
});

</script>

<style scoped>
.q-banner {
    margin-top: 16px;
}
</style>