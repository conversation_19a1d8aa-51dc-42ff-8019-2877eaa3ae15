#!/usr/bin/env ruby

require 'fileutils'
require 'socket'

# path to your application root.
APP_ROOT = File.expand_path('..', __dir__)

def system!(*args)
  system(*args, exception: true)
end

FileUtils.chdir APP_ROOT do
  ENV['RAILS_ENV'] = 'test'

  # Check Ruby version
  def check_ruby_version
    required_ruby_version = File.read('.ruby-version').strip.gsub('ruby-', '')
    current_ruby_version = `ruby -v`.match(/ruby (\d+\.\d+\.\d+)/)[1]

    return unless required_ruby_version != current_ruby_version

    puts "== Ruby version mismatch: .ruby-version is #{required_ruby_version}, but current version is #{current_ruby_version} =="

    unless system('which ruby-install')
      abort('== ruby-install is not installed. Please install ruby-install and chruby, then try again. ==')
    end

    unless system("ruby-install --no-reinstall #{required_ruby_version}")
      abort("== Failed to install Ruby #{required_ruby_version} with ruby-install. ==")
    end

    puts "== Ruby #{required_ruby_version} installed. please use chruby to switch Ruby =="
    exit 0
  end

  puts '== Checking Ruby version =='
  check_ruby_version

  puts '== Installing Ruby dependencies =='
  system! 'gem install bundler --conservative'
  system('bundle check') || system!('bundle install')

  puts '== Installing NODE dependencies =='
  system! 'npm i'
  system('npm ls cypress') || system!('npm install')

  puts '== Checking PSQL CLI status =='
  if system('which psql')
    puts '== PSQL CLI is installed =='
  else
    puts '== PSQL CLI is not installed =='
    if File.exist?('/Applications/Postgres.app/Contents/Versions/latest/bin/psql')
      system! 'echo \'export PATH="/Applications/Postgres.app/Contents/Versions/latest/bin:$PATH"\' >> ~/.zshrc'
      system! 'source ~/.zshrc'
      puts '== Added PSQL CLI to PATH =='
    else
      postgresql_paths = Dir.glob('/opt/homebrew/opt/postgresql@*/bin/psql')
      if postgresql_paths.any?
        highest_version_path = postgresql_paths.max_by { |path| path[/postgresql@(\d+)/, 1].to_i }
        system! "echo 'export PATH=\"#{File.dirname(highest_version_path)}:$PATH\"' >> ~/.zshrc"
        system! 'source ~/.zshrc'
        puts "== Added PSQL CLI to PATH from #{highest_version_path} =="
      else
        puts '== Error: PSQL CLI is not installed. Please install Postgres App or Postgres with Homebrew. =='
        exit 1
      end
    end
  end

  # Stripe CLI check and install
  puts '== Checking Stripe CLI status =='
  if system('which stripe')
    puts '== Stripe CLI is installed =='
  else
    puts '== Stripe CLI is not installed =='
    if system('which brew')
      puts '== Installing Stripe CLI with Homebrew =='
      system! 'brew install stripe/stripe-cli/stripe'
      if system('which stripe')
        puts '== Stripe CLI installed successfully =='
      else
        puts '== Error: Stripe CLI installation failed. Please install manually. =='
        exit 1
      end
    else
      puts '== Error: Homebrew is not installed. Please install Stripe CLI manually. =='
      exit 1
    end
  end

  # Stripe CLI check and install
  puts '== Checking Stripe CLI status =='
  if system('which stripe')
    puts '== Stripe CLI is installed =='
  else
    puts '== Stripe CLI is not installed =='
    if system('which brew')
      puts '== Installing Stripe CLI with Homebrew =='
      system! 'brew install stripe/stripe-cli/stripe'
      if system('which stripe')
        puts '== Stripe CLI installed successfully =='
      else
        puts '== Error: Stripe CLI installation failed. Please install manually. =='
        exit 1
      end
    else
      puts '== Error: Homebrew is not installed. Please install Stripe CLI manually. =='
      exit 1
    end
  end

  puts '== Checking PostgreSQL status =='
  postgres_running = system('pg_isready')
  unless postgres_running
    puts '== Starting PostgreSQL =='
    system! 'pg_start'
  end

  db_exists = system('psql -lqt | grep onestop_tests')
  puts "db_exists: #{db_exists}" # Debug statement to inspect db_exists
  if db_exists == false
    puts '== Creating Database =='
    system! "createdb #{ENV['TEST_DB_NAME'] || 'onestop_tests'} -O postgres"
  end

  no_db_flag = ARGV.include?('-d') || ARGV.include?('--no-db')
  if no_db_flag == true
    puts '== Downloading Database =='
    db_exists = system('psql -lqt | grep onestop_tests')
    puts "db_exists: #{db_exists}" # Debug statement to inspect db_exists
    if db_exists == false
      puts '== Creating Database =='
      system! "createdb #{ENV['TEST_DB_NAME'] || 'onestop_tests'} -O postgres"
    end
    system! 'heroku login && heroku pg:backups:download --app hg-large-staging'
    puts '== Loading Database data =='
    puts 'This will take a while... logging output to log/pg_restore.log'
    system! 'pg_restore --verbose --clean --no-acl --no-owner -h localhost -d onestop_tests latest.dump > log/pg_restore.log 2>&1'
    system! 'rm latest.dump'
    system! 'bundle exec rake db:migrate'
  end

  puts "\n== Seeding Database =="
  system! 'bin/rails db:schema:load'
  system! 'bin/rails db:seed'
  seed_files = Dir.glob(File.expand_path('bin/*.sql', APP_ROOT))
  if seed_files.any?
    seed_files.each do |seed_file|
      puts "== Loading seed file: #{seed_file} =="
      system! "psql onestop_tests < #{seed_file}"
    end
  else
    puts "== No .sql seed files found in bin/ =="
  end

  # puts "\n== Running Rails Tests =="
  # system! 'bin/rails test'

  ENV['CYPRESS_RAILS_HOST'] = 'http://localhost'
  ENV["RUBY_WEB_PORT"] = '3025'
  ENV['CYPRESS_RAILS_PORT'] = ARGV.include?('-p') ? ARGV[ARGV.index('-p') + 1] : ENV["RUBY_WEB_PORT"]

  puts '== Starting Delayed Jobs Worker =='
  thread = Thread.new do
    `bin/rails jobs:work`
  end

  # Start a shell thread to tail the test log
  puts '== Tailing log/test.log =='
  tail_thread = Thread.new do
    system('tail -f log/test.log')
  end

  def port_open?(port)
    TCPSocket.new('localhost', port).close
    true
  rescue Errno::ECONNREFUSED, Errno::EHOSTUNREACH
    false
  end

  puts '== Starting Rails Test Server =='
  unless port_open?(ENV['CYPRESS_RAILS_PORT'])
    system! "bin/rails server -d -p #{ENV['CYPRESS_RAILS_PORT']} --pid tmp/pids/test_server.pid &"
  end

  # Start Stripe CLI forwarding to Rails server port
  stripe_port = ENV['CYPRESS_RAILS_PORT']
  puts "== Starting Stripe CLI forwarding to http://localhost:#{stripe_port} =="

  # Check if Stripe CLI API key is expired and run `stripe login` if needed
  require 'open3'
  listen_cmd = "stripe listen --forward-to localhost:#{stripe_port}/webhooks"
  stdout_str, stderr_str, status = Open3.capture3(listen_cmd + " --print-secret")
  if stdout_str.include?("api_key_expired") || stderr_str.include?("api_key_expired") || stdout_str.include?("Expired API Key") || stderr_str.include?("Expired API Key")
    puts "== Stripe CLI API key expired or invalid. Running 'stripe login'... =="
    system! "stripe login"
  end

  # Now start the actual listen process
  system! "mkdir -p log" # Ensure the log directory exists
  system! "touch log/stripe_cli.log" # Ensure the log file exists
  stripe_pid = spawn("stripe listen --forward-to localhost:#{stripe_port}/webhooks", out: 'log/stripe_cli.log', err: 'log/stripe_cli.log')
  sleep 2 # Give Stripe CLI a moment to start

  # puts "\n== Starting Browser =="
  # system! "bin/browser localhost #{ENV['CYPRESS_RAILS_PORT']}" if ARGV.include?('-o') || ARGV.include?('--open')

  cypress_command = ARGV.include?('-o') || ARGV.include?('--open') ? 'open' : 'run'
  puts "\n== #{cypress_command.capitalize}ing Cypress =="
  system! "npx cypress #{cypress_command} --config baseUrl=#{ENV['CYPRESS_RAILS_HOST']}:#{ENV['CYPRESS_RAILS_PORT']}"

  # Parse keep-db flag
  keep_db_flag = ARGV.include?("--keep-db") || ARGV.include?("-kdb")

  at_exit do
    puts "\n== Kill Rails Test Server =="
    system 'kill -9 $(cat tmp/pids/test_server.pid)' if File.exist?('tmp/pids/test_server.pid')

    puts "\n== Kill Jobs Worker =="
    thread.terminate

    puts "\n== Kill Tail log/test.log =="
    tail_thread.kill if tail_thread

    puts "\n== Kill Stripe CLI =="
    Process.kill('TERM', stripe_pid) if stripe_pid

    unless keep_db_flag
      puts "\n== Drop Test DB =="
      system! 'dropdb onestop_tests -f'
    else
      puts "\n== Keeping Test DB (flag set) =="
    end
  end
end
