const { defineConfig } = require("cypress");
const { addCucumberPreprocessorPlugin } = require("@badeball/cypress-cucumber-preprocessor");
const webpack = require("@cypress/webpack-preprocessor");
const pg = require('pg');

module.exports = defineConfig({
  setupNodeEvents(on, config) {
    // This is required for the preprocessor to be able to generate JSON reports after each run, and more,
    addCucumberPreprocessorPlugin(on, config);
  
    const options = {
      webpackOptions: {
        resolve: {
          extensions: [".js", ".jsx"],
        },
        module: {
          rules: [
            {
              test: /\.feature$/,
              use: [
                {
                  loader: "@badeball/cypress-cucumber-preprocessor/webpack",
                  options: config,
                },
              ],
            },
            {
              test: /\.ts$/,
              exclude: [/node_modules/],
              use: [
                {
                  loader: "js-loader",
                },
              ],
            },
          ],
        },
      },
    };
  
    on("file:preprocessor", webpack(options));
  },
  experimentalInteractiveRunEvents: true,
  experimentalStudio: true,
  video: false,
  videoCompression: false,
  trashAssetsBeforeRuns: true,
  chromeWebSecurity: false,
  viewportHeight: 1080,
  viewportWidth: 1920,
  env: {
    DB: {
      user: process.env.USER,
      host: "localhost",
      database: "onestop_tests",
      port: 5432,
    },
    admin_dashboard_url: "/admin/dashboard",
    login_url: "/users/sign_in",
    reset_pw_url: "/users/password/new",
    booking_url: "/bookings",
    group_booking_url: "/acc_group_bookings/:id",
    feedback_url: "/feedback/feedback",
    new_bookings_url: "/apprentice/bookings",
    stripe_payment_url: "/acc_booking_confirmations/dfe487443599a861e7dd/payment_info",
    use_hardcoded_data: false,
    use_stripe_payment : true,
  },
  fixturesFolder: 'test/fixtures',
  downloadsFolder: 'test/downloads',
  e2e: {
    setupNodeEvents(on, config) {
      // Register the DBSERVICE task
      on('task', {
        DBSERVICE({ dbConfig, sql }) {
          // Implement the logic to read from the database using dbConfig and sql
          // For example, using a database client like pg for PostgreSQL
          const { Client } = require('pg');
          const client = new Client(dbConfig);

          return client.connect()
            .then(() => client.query(sql))
            .then((res) => {
              client.end();
              return res.rows;
            })
            .catch((err) => {
              client.end();
              throw err;
            });
        }
      });
    },
    specPattern: "cypress/e2e/*_spec.cy.js",
    supportFile: "cypress/support/index.js",
    includeShadowDom: true,
  },
  component: {
    specPattern: "cypress/component/**/*.spec.js",
    supportFile: false,
  },
  feature: {
    specPattern: "features/**/*.feature",
    supportFile: "cypress/support/index.js",
  }
});