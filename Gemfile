source 'https://rubygems.org'
ruby '3.3.5'

gem 'rails', '~>8.0'

gem 'rake'

gem 'rollbar'

gem 'puma', group: [:production]

gem "json", ">= 2.10.2"
gem 'pg'
gem 'thin', group: %i[development test]
# gem 'newrelic_rpm'
gem 'delayed_job_active_record'
# gem "workless" # :git => "https://github.com/davidakachaos/workless_revived.git"

gem 'scenic'

gem 'rack-uri_sanitizer'
gem "rack", ">= 2.2.13"
gem "nokogiri", ">= 1.18.4"

# gem 'rack-dev-mark'
# for convenience on a few rake tasks
# gem 'heroku'

gem 'coffee-rails'
gem 'development_ribbon'
gem 'sass-rails'
gem 'uglifier'

# To use ActiveModel has_secure_password
# gem 'bcrypt-ruby', '~> 3.0.0'

# fixing these versions because of autocomplete issues.
gem 'jquery-rails'
gem 'jquery-ui-rails'

gem 'select2-rails'

# ES Modernise and make more like the laravel project
# gem 'inertia_rails'

# TODO: update this later!!
# gem 'rails3-jquery-autocomplete', :git => "git://github.com/gfaraj/rails3-jquery-autocomplete.git"

# gem "therubyracer", :group => [:development, :test]
# gem 'therubyracer-heroku', '0.8.1.pre3', :group => [:production]   # no longer required for heroku (http://devcenter.heroku.com/articles/rails31_heroku_cedar)

# gem "exception_notification"

gem 'sprockets-rails', require: 'sprockets/railtie'

gem 'pagy' # More performant pagination, ES
gem 'will_paginate'

gem 'prawn'
gem 'prawn-table'
# gem 'grover'

# devtools
gem "debug", :group => [:development]
gem 'web-console', :group => [:development]

# testing stuff

gem 'rspec-activemodel-mocks', group: %i[development test]
gem 'rspec-rails', group: %i[development test]

# gem "rails-controller-testing"

# gem "ZenTest","=4.11.1", :group => [:development, :test]
gem 'factory_bot_rails', group: %i[test development staging]
gem 'faker', group: %i[development test staging]
# gem "capybara", :group => :test
# gem "selenium-webdriver", :group => :test
gem 'database_cleaner', group: :test
gem 'email_spec', group: :test
gem 'launchy', group: :test
gem 'shoulda-matchers', '~> 6.0', group: :test
# gem 'ruby-debug19', :group => [:test, :development]

# application stuff
gem 'devise', '~> 4.3'

gem 'state_machines'
gem 'state_machines-activerecord'

# gem 'devise_security_extension', git: 'https://github.com/phatworx/devise_security_extension.git'
gem 'devise-security' # New replacement from devise_security_extension

gem 'doorkeeper'
gem 'wine_bouncer'
# gem 'rails_email_validator'
gem 'acts_as_list'

gem 'inherited_resources'

gem 'yajl-ruby', require: 'yajl'
# gem 'paperclip'
gem 'aws-sdk-core'
gem 'aws-sdk-s3'
gem 'dragonfly', '1.4.0'
gem 'dragonfly-s3_data_store'
gem 'has_scope'
gem 'image_processing', '~> 1.2'
# gem 'fog'
gem 'geocoder'
gem 'icalendar', '2.6.1'
gem 'ice_cube', '=0.16.2'
gem 'mail_safe', group: %i[staging qa test]
gem 'recaptcha', require: 'recaptcha/rails'
gem 'unf' # apparently required by fog but doesn't make it a dependency - odd!

# gem "acts_as_paranoid"
gem 'bootsnap', require: false, group: %i[development staging production qa]
gem 'paranoia' # does not work in the same way, acts as paranoid 0.5 handles rails 4 and 5

gem 'mime-types'
gem 's3_form_presenter'
# gem "quiet_assets", :group => :development
gem 'csv_builder'
gem 'dalli'
gem 'memcachier'
gem 'ransack', '~>4.2'
# gem "activerecord-typedstore" not working on heroku!
# gem 'rack-mini-profiler'

# WAS: gem "acts_as_audited", "2.0.0.rc7"
# BUT: this doesn't work with rails 3.1; below is a tmp fix
# gem "acts_as_audited", :git => "git://github.com/ineu/acts_as_audited.git", :ref => 'ba013fbc206f18f78a808d76de7a4371144b93c0'
# gem "audited-activerecord"
gem 'audited'
gem 'rails-observers' # , github: 'rails/rails-observers'

gem 'outfielding-jqplot-rails'
gem 'weekdays'

# TODO: these three need updating

gem 'bootstrap-datepicker-rails'
gem 'cells'
gem 'cells-erb'
gem 'cells-rails'

gem 'rails-jquery-autocomplete'

gem 'simple_form'
gem 'spreadsheet'
gem 'writeexcel'

gem 'caxlsx'
gem 'caxlsx_rails'

gem 'figaro'
gem 'roo'
gem 'rrule'
gem 'rubyzip'
gem 'savon'
gem 'stripe'
gem 'zip-zip'

gem 'awesome_print'
gem 'kgio'
gem 'platform-api'
gem 'rack-cache'

# gem 'turbolinks'

gem 'rails_12factor', group: %i[staging production qa]

group :test do
  gem 'cucumber-rails', require: false
  gem 'simplecov', require: false
end

group :development do
  gem 'better_errors'
  gem 'binding_of_caller'
  gem 'bullet'
  gem 'lol_dba'
  gem 'ruby-lsp', '~> 0.20'
  gem 'ruby-lsp-rails', require: false
  gem 'ruby-lsp-rspec', require: false
  gem 'solargraph'
  gem 'wkhtmltopdf-binary'
end

gem 'rswag'

gem 'byebug'

gem 'active_record_query_trace', group: %i[development staging qa test]
gem 'grape'
gem 'grape-entity'
gem 'rack-cors'
# gem 'jquery-tablesorter'

gem 'pry'
gem 'pry-nav'

# support for rails5
gem 'record_tag_helper'

# gem 'webpacker'
gem 'font-awesome-rails'
gem 'jbuilder' # , github: 'rails/jbuilder'

# For calls to hgfeedback api
gem 'httparty'
gem 'jwt'

# Needed for Stripe
gem 'omniauth'
# Fixed as breaks with invalid_credentials error on 1.4, test again intermittently
gem 'omniauth-oauth2'
gem 'omniauth-stripe-connect'

gem 'listen'

gem 'deep_cloneable'

# ActiveRecord session store
gem 'activerecord-session_store'

gem 'scout_apm'

gem 'vite_rails'

gem 'matrix', '~> 0.4.2'

# Feedback gems

gem 'responders'

gem 'kaminari'
# gem 'rack-dev-mark'

gem 'repost' # Allows redirect to a post

# # Auth
# gem 'omniauth'
# gem 'omniauth-oauth2'
# gem 'omniauth-rails_csrf_protection'

# Useful way of holding environment variables
# See docs for deployment to heroku

gem 'wicked_pdf'

# QR codes
gem 'rqrcode-with-patches'

gem 'jquery-validation-rails'

# Auditing
gem 'paper_trail'
gem 'pundit'

# TODO: see which I need

gem 'nested_form'

gem 'recipient_interceptor'

# Lock this to fix the build
gem 'ffi', '< 1.17.0'

gem "barnes"

#AI STUFF
gem 'gemini-ai', '~> 4.2.0'
