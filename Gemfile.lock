GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.1)
      actionpack (= 8.0.1)
      activesupport (= 8.0.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.1)
      actionpack (= 8.0.1)
      activejob (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      mail (>= 2.8.0)
    actionmailer (8.0.1)
      actionpack (= 8.0.1)
      actionview (= 8.0.1)
      activejob (= 8.0.1)
      activesupport (= 8.0.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.1)
      actionview (= 8.0.1)
      activesupport (= 8.0.1)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.1)
      actionpack (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.1)
      activesupport (= 8.0.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record_query_trace (1.8.3)
      activerecord (>= 6.0.0)
    activejob (8.0.1)
      activesupport (= 8.0.1)
      globalid (>= 0.3.6)
    activemodel (8.0.1)
      activesupport (= 8.0.1)
    activerecord (8.0.1)
      activemodel (= 8.0.1)
      activesupport (= 8.0.1)
      timeout (>= 0.4.0)
    activerecord-session_store (2.1.0)
      actionpack (>= 6.1)
      activerecord (>= 6.1)
      cgi (>= 0.3.6)
      multi_json (~> 1.11, >= 1.11.2)
      rack (>= 2.0.8, < 4)
      railties (>= 6.1)
    activestorage (8.0.1)
      actionpack (= 8.0.1)
      activejob (= 8.0.1)
      activerecord (= 8.0.1)
      activesupport (= 8.0.1)
      marcel (~> 1.0)
    activesupport (8.0.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    akami (1.3.3)
      base64
      gyoku (>= 0.4.0)
      nokogiri
    ast (2.4.2)
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)
    awesome_print (1.9.2)
    aws-eventstream (1.3.1)
    aws-partitions (1.1057.0)
    aws-sdk-core (3.219.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.99.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.182.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    backport (1.2.0)
    barnes (0.0.9)
      multi_json (~> 1)
      statsd-ruby (~> 1.1)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    bootstrap-datepicker-rails (********)
      railties (>= 3.0)
    builder (3.3.0)
    bullet (8.0.1)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (11.1.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    cells (4.1.8)
      declarative-builder (~> 0.2.0)
      declarative-option (< 0.2.0)
      tilt (>= 1.4, < 3)
      uber (< 0.2.0)
    cells-erb (0.1.0)
      cells (~> 4.0)
      erbse (>= 0.1.1)
    cells-rails (0.1.6)
      actionpack (>= 5.0)
      cells (>= 4.1.6, < 5.0.0)
    cgi (0.4.2)
    childprocess (5.1.0)
      logger (~> 1.5)
    chunky_png (1.4.0)
    coderay (1.1.3)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    crass (1.0.6)
    csv (3.3.2)
    csv_builder (2.1.3)
      actionpack (>= 3.0.0)
    cucumber (9.2.1)
      builder (~> 3.2)
      cucumber-ci-environment (> 9, < 11)
      cucumber-core (> 13, < 14)
      cucumber-cucumber-expressions (~> 17.0)
      cucumber-gherkin (> 24, < 28)
      cucumber-html-formatter (> 20.3, < 22)
      cucumber-messages (> 19, < 25)
      diff-lcs (~> 1.5)
      mini_mime (~> 1.1)
      multi_test (~> 1.1)
      sys-uname (~> 1.2)
    cucumber-ci-environment (10.0.1)
    cucumber-core (13.0.3)
      cucumber-gherkin (>= 27, < 28)
      cucumber-messages (>= 20, < 23)
      cucumber-tag-expressions (> 5, < 7)
    cucumber-cucumber-expressions (17.1.0)
      bigdecimal
    cucumber-gherkin (27.0.0)
      cucumber-messages (>= 19.1.4, < 23)
    cucumber-html-formatter (21.9.0)
      cucumber-messages (> 19, < 28)
    cucumber-messages (22.0.0)
    cucumber-rails (3.1.1)
      capybara (>= 3.11, < 4)
      cucumber (>= 5, < 10)
      railties (>= 5.2, < 9)
    cucumber-tag-expressions (6.1.2)
    daemons (1.4.1)
    dalli (3.2.8)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    declarative-builder (0.2.0)
      trailblazer-option (~> 0.1.0)
    declarative-option (0.1.0)
    deep_cloneable (3.2.1)
      activerecord (>= 3.1.0, < 9)
    delayed_job (4.1.13)
      activesupport (>= 3.0, < 9.0)
    delayed_job_active_record (4.1.11)
      activerecord (>= 3.0, < 9.0)
      delayed_job (>= 3.0, < 5)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    development_ribbon (0.2.0)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-security (0.18.0)
      devise (>= 4.3.0)
    diff-lcs (1.6.0)
    docile (1.4.1)
    doorkeeper (5.8.1)
      railties (>= 5)
    dragonfly (1.4.0)
      addressable (~> 2.3)
      multi_json (~> 1.0)
      rack (>= 1.3)
    dragonfly-s3_data_store (1.3.0)
      dragonfly (~> 1.0)
      fog-aws
    drb (2.2.1)
    dry-cli (1.2.0)
    email_spec (2.3.0)
      htmlentities (~> 4.3.3)
      launchy (>= 2.1, < 4.0)
      mail (~> 2.7)
    erbse (0.1.4)
      temple
    erubi (1.13.1)
    erubis (2.7.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    event_stream_parser (1.0.0)
    eventmachine (1.2.7)
    excon (1.2.5)
      logger
    execjs (2.10.0)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    faraday-typhoeus (1.1.0)
      faraday (~> 2.0)
      typhoeus (~> 1.4)
    ffi (1.16.3)
    figaro (1.2.0)
      thor (>= 0.14.0, < 2)
    fog-aws (3.30.0)
      base64 (~> 0.2.0)
      fog-core (~> 2.6)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    font-awesome-rails (4.7.0.9)
      railties (>= 3.2, < 9.0)
    formatador (1.1.0)
    gemini-ai (4.2.0)
      event_stream_parser (~> 1.0)
      faraday (~> 2.10)
      faraday-typhoeus (~> 1.1)
      googleauth (~> 1.8)
      typhoeus (~> 1.4, >= 1.4.1)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-cloud-env (2.2.1)
      faraday (>= 1.0, < 3.a)
    google-logging-utils (0.1.0)
    googleauth (1.13.1)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    grape (1.2.5)
      activesupport
      builder
      mustermann-grape (~> 1.0.0)
      rack (>= 1.3.0)
      rack-accept
      virtus (>= 1.0.0)
    grape-entity (1.0.1)
      activesupport (>= 3.0.0)
      multi_json (>= 1.3.2)
    gyoku (1.4.0)
      builder (>= 2.1.2)
      rexml (~> 3.0)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashie (5.0.0)
    heroics (0.1.3)
      base64
      erubis (~> 2.0)
      excon
      moneta
      multi_json (>= 1.9.2)
      webrick
    htmlentities (4.3.4)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpi (4.0.4)
      base64
      mutex_m
      nkf
      rack (>= 2.0, < 4)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    icalendar (2.6.1)
      ice_cube (~> 0.16)
    ice_cube (0.16.2)
    ice_nine (0.11.2)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (2.1.0)
      actionpack (>= 7.0)
      has_scope (>= 0.6)
      railties (>= 7.0)
      responders (>= 2)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jaro_winkler (1.6.0)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-ui-rails (7.0.0)
      railties (>= 3.2.16)
    jquery-validation-rails (1.19.0)
    json (2.10.2)
    json-schema (5.1.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kgio (2.11.4)
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.6)
    lol_dba (2.4.0)
      actionpack
      activerecord
      railties
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mail_safe (0.3.4)
      actionmailer (>= 3.0.0)
    marcel (1.0.4)
    matrix (0.4.2)
    memcachier (0.0.2)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0220)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.4)
    moneta (1.0.0)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_test (1.1.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mustermann-grape (1.0.2)
      mustermann (>= 1.0.0)
    mutex_m (0.3.0)
    nested_form (0.3.2)
    net-http (0.6.0)
      uri
    net-imap (0.5.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nkf (0.2.0)
    nokogiri (1.18.5-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.5-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.5-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.5-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.5-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.5-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.5-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.5-x86_64-linux-musl)
      racc (~> 1.4)
    nori (2.7.1)
      bigdecimal
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    observer (0.1.2)
    omniauth (1.9.2)
      hashie (>= 3.4.6)
      rack (>= 1.6.2, < 3)
    omniauth-oauth2 (1.7.3)
      oauth2 (>= 1.4, < 3)
      omniauth (>= 1.9, < 3)
    omniauth-stripe-connect (2.10.1)
      omniauth (~> 1.3)
      omniauth-oauth2 (~> 1.4)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    outfielding-jqplot-rails (1.0.9)
    pagy (9.3.3)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.26.3)
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pg (1.5.9)
    platform-api (3.7.0)
      heroics (~> 0.1.1)
      moneta (~> 1.0.0)
      rate_throttle_client (~> 0.1.0)
    pp (0.6.2)
      prettyprint
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    prettyprint (0.2.0)
    prism (1.3.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-nav (1.0.0)
      pry (>= 0.9.10, < 0.15)
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.4.0)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (2.2.13)
    rack-accept (0.4.5)
      rack (>= 0.4)
    rack-cache (1.17.0)
      rack (>= 0.4)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-proxy (0.7.7)
      rack
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rack-uri_sanitizer (0.0.2)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (8.0.1)
      actioncable (= 8.0.1)
      actionmailbox (= 8.0.1)
      actionmailer (= 8.0.1)
      actionpack (= 8.0.1)
      actiontext (= 8.0.1)
      actionview (= 8.0.1)
      activejob (= 8.0.1)
      activemodel (= 8.0.1)
      activerecord (= 8.0.1)
      activestorage (= 8.0.1)
      activesupport (= 8.0.1)
      bundler (>= 1.15.0)
      railties (= 8.0.1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-jquery-autocomplete (1.0.5)
      rails (>= 3.2)
    rails-observers (0.1.5)
      activemodel (>= 4.0)
    rails_12factor (0.0.3)
      rails_serve_static_assets
      rails_stdout_logging
    rails_serve_static_assets (0.0.5)
    rails_stdout_logging (0.0.5)
    railties (8.0.1)
      actionpack (= 8.0.1)
      activesupport (= 8.0.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rate_throttle_client (0.1.2)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbs (3.8.1)
      logger
    rdoc (6.12.0)
      psych (>= 4.0.0)
    recaptcha (5.19.0)
    recipient_interceptor (0.3.3)
      mail
    record_tag_helper (1.0.1)
      actionview (>= 5)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    repost (0.4.2)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    reverse_markdown (3.0.0)
      nokogiri
    rexml (3.4.1)
    rollbar (3.6.1)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rouge (4.5.1)
    rqrcode-with-patches (0.6.0)
      chunky_png
    rrule (0.6.0)
      activesupport (>= 2.3)
    rspec-activemodel-mocks (1.2.1)
      activemodel (>= 3.0)
      activesupport (>= 3.0)
      rspec-mocks (>= 2.99, < 4.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    rswag (2.16.0)
      rswag-api (= 2.16.0)
      rswag-specs (= 2.16.0)
      rswag-ui (= 2.16.0)
    rswag-api (2.16.0)
      activesupport (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.16.0)
      actionpack (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rubocop (1.73.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.38.1)
      parser (>= *******)
    ruby-lsp (0.23.11)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 4)
      sorbet-runtime (>= 0.5.10782)
    ruby-lsp-rails (0.4.0)
      ruby-lsp (>= 0.23.0, < 0.24.0)
    ruby-lsp-rspec (0.1.22)
      ruby-lsp (~> 0.23.0)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    s3_form_presenter (0.0.7)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    savon (2.15.1)
      akami (~> 1.2)
      builder (>= 2.1.2)
      gyoku (~> 1.2)
      httpi (>= 4, < 5)
      mail (~> 2.5)
      nokogiri (>= 1.8.1)
      nori (~> 2.4)
      wasabi (>= 3.7, < 6)
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scout_apm (5.6.1)
      parser
    securerandom (0.4.1)
    select2-rails (4.0.13)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_form (5.3.1)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    solargraph (0.51.2)
      backport (~> 1.2)
      benchmark
      bundler (~> 2.0)
      diff-lcs (~> 1.4)
      jaro_winkler (~> 1.6)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      logger (~> 1.6)
      observer (~> 0.1)
      ostruct (~> 0.6)
      parser (~> 3.0)
      rbs (~> 3.0)
      reverse_markdown (>= 2.0, < 4)
      rubocop (~> 1.38)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
    sorbet-runtime (0.5.11874)
    spreadsheet (1.3.3)
      bigdecimal
      ruby-ole
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    state_machines (0.6.0)
    state_machines-activemodel (0.9.0)
      activemodel (>= 6.0)
      state_machines (>= 0.6.0)
    state_machines-activerecord (0.9.0)
      activerecord (>= 6.0)
      state_machines-activemodel (>= 0.9.0)
    statsd-ruby (1.5.0)
    stringio (3.1.5)
    stripe (13.5.0)
    sys-uname (1.3.1)
      ffi (~> 1.1)
    temple (0.10.3)
    thin (1.8.2)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      rack (>= 1, < 3)
    thor (1.3.2)
    thread_safe (0.3.6)
    tilt (2.6.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unf (0.2.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uniform_notifier (1.16.0)
    uri (1.0.3)
    useragent (0.16.11)
    version_gem (1.1.6)
    virtus (2.0.0)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
    vite_rails (3.0.19)
      railties (>= 5.1, < 9)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.9.1)
      dry-cli (>= 0.7, < 2)
      logger (~> 1.6)
      mutex_m
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    wasabi (5.1.0)
      addressable
      faraday (>= 1.9, < 3)
      nokogiri (>= 1.13.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webrick (1.9.1)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    weekdays (1.0.2)
    wicked_pdf (2.8.2)
      activesupport
      ostruct
    will_paginate (4.0.1)
    wine_bouncer (1.0.4)
      doorkeeper (>= 1.4, < 6.0)
      grape (>= 0.10, < 1.3)
    wkhtmltopdf-binary (********)
    writeexcel (1.0.9)
      nkf
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yajl-ruby (1.4.3)
    yard (0.9.37)
    zeitwerk (2.7.2)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)

PLATFORMS
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  arm64-darwin-24
  x86_64-darwin
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  active_record_query_trace
  activerecord-session_store
  acts_as_list
  audited
  awesome_print
  aws-sdk-core
  aws-sdk-s3
  barnes
  better_errors
  binding_of_caller
  bootsnap
  bootstrap-datepicker-rails
  bullet
  byebug
  caxlsx
  caxlsx_rails
  cells
  cells-erb
  cells-rails
  coffee-rails
  csv_builder
  cucumber-rails
  dalli
  database_cleaner
  debug
  deep_cloneable
  delayed_job_active_record
  development_ribbon
  devise (~> 4.3)
  devise-security
  doorkeeper
  dragonfly (= 1.4.0)
  dragonfly-s3_data_store
  email_spec
  factory_bot_rails
  faker
  ffi (< 1.17.0)
  figaro
  font-awesome-rails
  gemini-ai (~> 4.2.0)
  geocoder
  grape
  grape-entity
  has_scope
  httparty
  icalendar (= 2.6.1)
  ice_cube (= 0.16.2)
  image_processing (~> 1.2)
  inherited_resources
  jbuilder
  jquery-rails
  jquery-ui-rails
  jquery-validation-rails
  json (>= 2.10.2)
  jwt
  kaminari
  kgio
  launchy
  listen
  lol_dba
  mail_safe
  matrix (~> 0.4.2)
  memcachier
  mime-types
  nested_form
  nokogiri (>= 1.18.4)
  omniauth
  omniauth-oauth2
  omniauth-stripe-connect
  outfielding-jqplot-rails
  pagy
  paper_trail
  paranoia
  pg
  platform-api
  prawn
  prawn-table
  pry
  pry-nav
  puma
  pundit
  rack (>= 2.2.13)
  rack-cache
  rack-cors
  rack-uri_sanitizer
  rails (~> 8.0)
  rails-jquery-autocomplete
  rails-observers
  rails_12factor
  rake
  ransack (~> 4.2)
  recaptcha
  recipient_interceptor
  record_tag_helper
  repost
  responders
  rollbar
  roo
  rqrcode-with-patches
  rrule
  rspec-activemodel-mocks
  rspec-rails
  rswag
  ruby-lsp (~> 0.20)
  ruby-lsp-rails
  ruby-lsp-rspec
  rubyzip
  s3_form_presenter
  sass-rails
  savon
  scenic
  scout_apm
  select2-rails
  shoulda-matchers (~> 6.0)
  simple_form
  simplecov
  solargraph
  spreadsheet
  sprockets-rails
  state_machines
  state_machines-activerecord
  stripe
  thin
  uglifier
  unf
  vite_rails
  web-console
  weekdays
  wicked_pdf
  will_paginate
  wine_bouncer
  wkhtmltopdf-binary
  writeexcel
  yajl-ruby
  zip-zip

RUBY VERSION
   ruby 3.3.5p100

BUNDLED WITH
   2.6.3
