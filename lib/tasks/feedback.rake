namespace :feedback do
  desc "Add Rfq Request to all Surveys which don't have it but have a response id"
  task add_rfq_request_to_all_surveys: :environment do

    Survey.where("rfq_request_id is null and rfq_response_id is not null and rfq_response_id != 0").each do |survey|
          response_id = survey.rfq_response_id
          rfq_request_id = RfqResponse.find(response_id).rfq_location.rfq_request_id
          if rfq_request_id.present?
            p "Updating survey #{survey.id} with rfq_request_id #{rfq_request_id}"
            survey.update_attribute(:rfq_request_id, rfq_request_id)
          end
    end

  end

  desc "Add rfq trainers as adhoc trainers to new surveys missing them"
  task add_adhoc_trainers_to_surveys: :environment do
    surveys = Survey.where("created_at >= '2024-08-01'")
    surveys.each do |survey|
      unless survey.trainers.any?
        p "Adding adhoc trainers to survey #{survey.id}"
        rfq_trainers = survey.rfq_programme.rfq_trainers.current
        rfq_trainers.each do |rfq_trainer|
          p "Adding trainer #{rfq_trainer.full_name_email}"
          name = "#{rfq_trainer.full_name_email}, (RFQ ID: #{rfq_trainer.id})"
          trainer = Trainer.create!(name: name, questionnaire_id: survey.id)
          p "Trainer #{trainer.id} created"
        end
      end
    end
  end

  desc "Clear feedback for a task linked from an rfq_response"
  task :clear_feedback_and_rfq_task, [:feedback_id]  => :environment do |t, args|
    puts "Starting"
    survey = Questionnaire.find(args[:feedback_id])

    rfq_response_id = survey.rfq_response_id

    puts "Deleting survey #{survey.id}"
    survey.destroy!
    puts "Survey deleted"

    rfq_task = RfqTask.where(rfq_response_id: rfq_response_id, name: "Feedback Setup").first

    if rfq_task.blank?
      puts "No task found for rfq_response_id #{rfq_response_id}"
    else
      puts "Clearing feedback for task #{rfq_task.id}"
      rfq_task.update_attribute(:completed_at, nil)
    end
    puts "Finished"
  end


  desc "Send feedback link to adult learners whose bookings are complete and end date is today"
  # rake feedback:send_feedback_link_to_adult_learners
  task :send_feedback_link_to_adult_learners => :environment do
    bookings = AccBooking.includes('hotel').joins(acc_booking_header: {rfq_location: :rfq_request})
               .where("acc_bookings.last_check_out = ? and acc_booking_headers.booking_type = 'ADULT' and rfq_requests.send_adult_survey_link = true", Date.today)
               .where.not(confirmed_at: nil).where(cancelled_at: nil)

    bookings.each do |booking|
        hotel_name = booking.hotel&.name
        survey = booking.acc_booking_header.rfq_location&.rfq_request&.survey
        trainee = booking.acc_booking_person
        trainee_name = trainee.forename + " " + trainee.surname
        trainee_email = trainee.email

        if survey.present?
          Feedback::SurveyMailer.send_adult_link(hotel_name, survey, trainee_name, trainee_email).deliver_later
        end
    end
  end

  desc "Send feedback links for the last week for a specific rfq_request and survey ids"
  task :send_feedback_links_for_adults => :environment do
    rfq_request_id = 1700

    p "Sending feedback links for rfq_request_id #{rfq_request_id}"

    bookings = AccBooking.includes('hotel').joins(acc_booking_header: {rfq_location: :rfq_request})
               .where("acc_bookings.last_check_out < ? AND acc_booking_headers.booking_type = 'ADULT' AND rfq_requests.id = ? AND rfq_requests.send_adult_survey_link = true", Date.today, rfq_request_id)
               .where("DATE(acc_bookings.last_check_out) BETWEEN ? AND ?", Date.new(Date.today.year, 2, 1), Date.new(Date.today.year, 3, 31))
               .where.not(confirmed_at: nil).where(cancelled_at: nil)

    if bookings.empty?
      p "No bookings found for rfq_request_id #{rfq_request_id}"
    end

    bookings.each do |booking|

      p "Booking ID: #{booking.id}"

        hotel_name = booking.hotel&.name
        trainee = booking.acc_booking_person
        trainee_name = trainee.forename + " " + trainee.surname
        trainee_email = trainee.email

        surveys = Survey.where(id: [1541, 1535, 1534])

        surveys.each do |survey|
          p "Sending feedback link to #{trainee_name} (#{trainee_email}) for survey ID #{survey.id}"

          Feedback::SurveyMailer.send_adult_link(hotel_name, survey, trainee_name, trainee_email).deliver_later

        end
    end
  end

  desc "Send feedback link to all academy bookings"
  # rake feedback:send_feedback_link_to_all_academy_attendees
  task :send_feedback_link_to_all_academy_attendees => :environment do
    attendees = BookingAttendee.joins(booking: {rfq_location: :rfq_request})
                .where("rfq_requests.send_adult_survey_link = true")

    attendees.each do |attendee|
      booking = attendee.booking
      survey = booking.rfq_location&.rfq_request&.survey

      if survey.present?
        Feedback::SurveyMailer.send_adult_link(booking.hotel&.name, survey, attendee.full_name, attendee.email).deliver_later
      end
    end
  end

  desc "Send feedback link to academy bookings that are complete and end date is today"
  # rake feedback:send_feedback_link_to_academy_attendees
  task :send_feedback_link_to_academy_attendees => :environment do
    attendees = BookingAttendee.joins(booking: {rfq_location: :rfq_request})
                .where("bookings.check_out = ? and rfq_requests.send_adult_survey_link = true", Date.today)

    attendees.each do |attendee|
      booking = attendee.booking
      survey = booking.rfq_location&.rfq_request&.survey

      if survey.present?
        Feedback::SurveyMailer.send_adult_link(booking.hotel&.name, survey, attendee.full_name, attendee.email).deliver_later
      end
    end
  end

end
