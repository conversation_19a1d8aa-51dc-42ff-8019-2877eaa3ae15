#encoding: utf-8

namespace :rfq do

  # desc "add required questions for apprentice stop rfq"
  task :add_app_stop_q => :environment do
    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Discounted soft drink provided?",
                    :pre_yn => true,
                    :prompt => "Please provide details" ,
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Discounted soft drink provided?").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Discounts at leisure attractions provided?",
                    :pre_yn => true,
                    :prompt => "Please provide details" ,
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Discounts at leisure attractions provided?").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Any other discounts?",
                    :pre_yn => true,
                    :prompt => "Please provide details" ,
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Any other discounts?").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Games chill out room provided?",
                    :pre_yn => true,
                    :prompt => "Please provide details",
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Games chill out room provided?").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Jugs of cordial provided with dinner",
                    :pre_yn => false,
                    :answer_type => "Yes/No").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Jugs of cordial provided with dinner").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Do you have Leisure Facilities?",
                    :pre_yn => true,
                    :prompt => "Facilities available",
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Do you have Leisure Facilities?").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Apprentice Feedback Prize £5.00 Voucher",
                    :pre_yn => true,
                    :prompt => "Type of Preferred Voucher" ,
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Apprentice Feedback Prize £5.00 Voucher").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "An Admin Fee is required of £2 per night to be included into the rates. This will be invoiced directly by ServAce to the hotel monthly. Can you confirm acceptance, and that it is included in the rates provided?",
                    :pre_yn => false,
                    :answer_type => "Yes/No").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "An Admin Fee is required of £2 per night to be included into the rates. This will be invoiced directly by ServAce to the hotel monthly. Can you confirm acceptance, and that it is included in the rates provided?").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Additional Notes field - Please use this to record any relevant information regarding the quotation.",
                    :pre_yn => false,
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Additional Notes field - Please use this to record any relevant information regarding the quotation.").first.present?

    RfqQuestion.new(:app_rfq => true,
                    :question_text => "Please confirm the total time to get to the training centre postcode in peak traffic",
                    :pre_yn => false,
                    :answer_type => "Text").save!(:validate => false) unless RfqQuestion.apprentice.where(:question_text => "Please confirm the total time to get to the training centre postcode in peak traffic").first.present?
    puts "Rake Task Completed Successfully"
    end

  # desc "update requests to have their rfq_room_options from their booleans"
  task :room_options => :environment do
    bad=[]
    mapping = {"single_room" => "Single",
               "twin_room" => "Twin for 2 people",
               "dbl_for_sole" => "Double for Sole",
               "dbl_for_double" => "Double for Double"}
    raise "Arruuugah wrong room types!" if (mapping.values + ["Triple"] != RfqResponseRoom::ROOM_TYPES)
    RfqRequest.all.each do |rfq|
      mapping.each do |meth, room_type|
        next if rfq.rfq_room_options.where(:room_type => room_type).first.present?
        if rfq.send(meth) || rfq.send(meth + "_trans")
          option = rfq.rfq_room_options.build(:room_type => room_type)
          RfqRoomOption::PACKAGE_FIELDS.each do |sym|
            option.send(sym.to_s + "=", true) if rfq.send("pkg_" + sym.to_s)
          end
          option.ex_trans = rfq.send(meth)
          option.inc_trans = rfq.send(meth + "_trans")
          unless option.save
            bad <<[rfq.id, option.inspect, option.errors.full_messages.to_sentence]
          end
        end
      end
      rfq.build_options
      rfq.save(:validate => false)
    end
    puts bad.inspect
  end

  desc "remove credit_app task from accepted responses if the client is paying"
  task :rem_capp_task => :environment do
    RfqTask.joins(:rfq_response => {:rfq_location => :rfq_request}).
      where("rfq_tasks.code = 'credit_app'").
      where("rfq_requests.payment_method = ?","Manager/ Learner to Pay Entire").destroy_all
  end

  desc "Send JI that are due"
  task :send_due_jis => :environment do
    bookings = AccBooking.joins(:acc_booking_person, :acc_booking_header => { :rfq_location => :rfq_request }).
      where("confirmed_at is null and cancelled_at is null and hotel_declined_at is null").
      where("acc_booking_people.disabled = 'f'").
      where("rfq_requests.no_ji is not true").distinct
    bookings.each do |booking|
      AccBookingMailer.delay.send_joining_instructions(booking.id) if booking.can_send_ji?(true)
    end
  end

  # rake "rfq:send_jis_to_all_bookings_under_rfq[1540 1541 1542]"
  desc "Send JI for all bookings under this RFQ"
  task :send_jis_to_all_bookings_under_rfq, [:ids] => :environment do |t, args|
    puts "Starting"
    args[:ids].split(' ').map{ |s| s.to_i }.each do |rfq_id|
      bookings = AccBooking.joins(acc_booking_header: {rfq_location: :rfq_request}). where(confirmed_at: nil, rfq_request: {id: rfq_id})
      if !bookings.any?
        puts "No bookings found for RFQ #{rfq_id}"
      else
        puts "For RFQ #{rfq_id}: Found #{bookings.count} bookings"
        puts "This will send JIs for the following bookings IDs, are you sure?"
        puts "#{bookings.map(&:id)}"
        confirm_token = 'yes'
        STDOUT.puts "To send JIs enter: '#{confirm_token}'"
        input = STDIN.gets.chomp
        if input != confirm_token
          raise "Aborting change. You entered #{input}"
        else
            bookings.each do |booking|
              AccBookingMailer.delay.send_joining_instructions(booking.id)
            end
        end
      end
    end
  end

  # desc "daily scheduled task: remind n days before first checkin if request has reminder_email set"
  task :remind_n_days => :environment do
    if Date.today.wday != 0 && Date.today.wday != 6
      Reminder.acc_booking_conf_reminder
      puts "Reminder Sent"
    else
      puts "Can not send reminder on a weekend"
    end
  end

  # desc "anonymise data"
  task :anonymise => :environment do
    raise "Cannot put dummy learners into live data!!" if Rails.env.production?
    RfqLearner.all.each do |l|
      l.forename = Faker::Name.first_name
      l.surname = Faker::Name.last_name
      l.email = Faker::Internet.email(l.forename)
      name = [Faker::Name.first_name, Faker::Name.last_name]
      l.manager = name.join(" ")
      l.manager_email = Faker::Internet.email(name.first)
      l.manager_telephone = Faker::Number.number(10)
      l.save(:validate => false)
    end
  end

  # desc "add individual cancellation policy to rfq hotel briefings"
  task :cancel_policies => :environment do
    RfqHotelBriefing.where("cancellation_policy is null").update_all(:cancellation_policy => "72 hours prior to check in")
  end

  # desc "update client contact for acc issue - acc issue now belongs to client contact
  #         This is mainly for staging so testing can continue."
  task :issues_client_contact => :environment do
    AccIssue.where(:client_contact_id => nil).each do |issue|
      prog = issue.rfq_location.rfq_request.rfq_programme
      if prog.rfq_roles.manager.any?
        issue.client_contact = prog.rfq_roles.manager.first.contact
        puts "set as prog manager"
      elsif prog.rfq_roles.admin.any?
        issue.client_contact = prog.rfq_roles.admin.first.contact
        puts "set as prog admin"
      else
        issue.client_contact = prog.client.contacts.first
        puts "set as client contact"
      end
      issue.save(:validate => false)
    end
  end

  # desc "copy approved ji logos to rfq request re defaults for rfq cloning"
  task :clone_logos => :environment do
    bad=[]
    good=[]
    puts 'Starting...'
    RfqRequest.joins(:rfq_locations => :rfq_responses).where("rfq_responses.accepted_at is not null").each do |rfq|
      rfq =  RfqRequest.find rfq.id #avoid read_only record
      puts "trying #{rfq.id}..."
      resp = rfq.rfq_locations.first.accepted_response
      task = resp.task_for_code('join_instr')
      ji = task.rfq_joining_instructions.approved.first
      next unless ji
      app = Dragonfly.app
      begin
        if ji.logo_left.present? && rfq.ji_logo_left.blank?
          img = app.fetch(ji.logo_left_uid)
          rfq.ji_logo_left = img if img.present?
          rfq.ji_logo_left.name = ji.logo_left_name
          rfq.ji_logo_left_name = ji.logo_left_name
          rfq.ji_logo_left_content_type = ji.logo_left_content_type
          puts "logo left set for rfq #{rfq.id}"
        end
        if ji.logo_right.present? && rfq.ji_logo_right.blank?
          img = app.fetch(ji.logo_right_uid)
          rfq.ji_logo_right = img if img.present?
          rfq.ji_logo_right.name = ji.logo_right_name
          rfq.ji_logo_right_name = ji.logo_right_name
          rfq.ji_logo_right_content_type = ji.logo_right_content_type
          puts "logo right set for rfq #{rfq.id}"
        end
        if rfq.save(:validate => false)
          good << rfq.id
        else
          bad << rfq.id
        end
      rescue Exception => e
        puts "no go: #{e.message}"
        bad << [rfq.id, e.message]
      end
    end
    g= good.size
    b=bad.size
    puts "Of #{g+b} rfqs #{g} had logos set ok and there were #{b} bad ones:"
    puts bad.inspect
  end

  # desc  "move rfq booking conf docs to rfq task"
  task :move_conf_docs => :environment do
    bad = []
    RfqBookingConfDoc.where("rfq_task_id is null").each do |conf_doc|
      task =conf_doc.rfq_location.accepted_response.task_for_code("conf_form")
      if task.blank?
        bad << conf_doc.id
        next
      end
     conf_doc.update_column("rfq_task_id", conf_doc.rfq_location.accepted_response.task_for_code("conf_form"))
    end
    puts "bad ones : #{bad.inspect}"
  end

  # desc "app contract level - change wording"
  task :new_app_contract_level => :environment do
    current = Organisation.where("apprentice_contract_level is not null").map{|o| o.apprentice_contract_level}.uniq
    puts "before #{current}"
    Organisation.where("apprentice_contract_level = 'BASIC'").update_all(:apprentice_contract_level => 'SELF')
    Organisation.where("apprentice_contract_level = 'STANDARD'").update_all(:apprentice_contract_level => 'PART')
    Organisation.where("apprentice_contract_level = 'ADVANCED'").update_all(:apprentice_contract_level => 'FULLY')
    current = Organisation.where("apprentice_contract_level is not null").map{|o| o.apprentice_contract_level}.uniq
    puts "after #{current}"
  end


  # desc "repair for duped group apprentice booking with several booking blocks"
  task :cancel_duped_header, [:header_id] => :environment do |t, args|
     hdr = AccBookingHeader.find  args[:header_id]
     return "no header!" if hdr.nil?
     hdr.acc_booking_blocks.not_cancelled.each do |block|
       puts "cancelling block ..."
       block.cancelled_at = Time.zone.now
       block.cancelled_by = "System"
       block.cancellation_reason = "This block of bookings were accidentally duplicated please login to the system to see which of the requested bookings is still wanted"
       block.save
       block.acc_bookings.not_cancelled.update_all(:cancellation_wish => true, :cancelled_at => block.cancelled_at, :cancellation_reason => block.cancellation_reason)
     end
  end

  # desc "check for duped bookings daily"
  task :dupe_book_check => :environment do
    ids = AccBooking.daily_dupe_check
    if ids.size > 0
      exp = AccBookingsSearchAndExport.new(AccBookingSearch.new({}), :hg, :system);nil
      f = exp.export_dupes(ids)
      SystemMailer.check_dupe_bookings_daily(f, ids.size).deliver
    else
      SystemMailer.check_dupe_bookings_daily(nil,0)
    end
  end

  desc "check for duped bookings for past 6 months"
  task :dupe_book_check_past => :environment do
    ids = AccBooking.dupe_checks
    if ids.size > 0
      exp = AccBookingsSearchAndExport.new(AccBookingSearch.new({}), :hg, :system);nil
      f = exp.export_dupes(ids)
      SystemMailer.check_dupe_bookings_daily(f, ids.size).deliver
    else
      SystemMailer.check_dupe_bookings_daily(nil,0).deliver
    end
  end

  desc "rfq wednesday email hotel reminder"
  task :weekly_email => :environment do
    if Date.today.wday == 3
      RfqWednesdayJob.perform_later
    end
    if Date.today.wday == 1
      RfqMondayJob.perform_later
    end
    if Date.today.wday == 0
      AccNonArrivalsJob.perform_later
    end
  end

  # #to run daily - not need for about a year though!
  # #add scheduled task in one year - add reminder
  # desc "clean up old deleted learners and bu's"
  task :bu_lrn_clean => :environment do
    ls =  RfqLearner.only_deleted.where(" deleted_at < ?", Time.zone.now - 1.year).delete_all!
    bus = RfqBusinessUnit.only_deleted.where(" deleted_at < ?", Time.zone.now - 1.year).delete_all!
    puts "CLEANUP: completely removed #{ls} learners previously soft deleted"
    puts "CLEANUP: completely removed #{bus} business units previously soft deleted"
  end

  # desc "RFQ starting soon task generator"
  task :rfq_starting => :environment do
    RfqHotelReadyJob.perform_now # doesn't take long - TODO reasses later
  end

  # desc "Block programmes running out of date"
  task :bp_reminder => :environment do
     RfqTask.where(:id => RfqDocument.where("rfq_documents.has_reminder <> 't'").bp_running_out.keys.map(&:to_i)).each do |task|
        doc = task.rfq_documents.completed.order("end_date DESC").first
        next if doc.has_reminder?
        rfq = doc.rfq_task.rfq_response.rfq_location.rfq_request
        task = Task.create!(
            :name =>            "BLOCK PROGRAMME REMINDER",
            :comment =>         "Contact #{rfq.contact.full_name} on #{rfq.contact.telephone}: block programme runs out on #{doc.end_date}, please ask the client for the next block progamme",
            :assigned_to_id => rfq.hg_contact_id ,
            :asset =>           rfq,
            :category =>        'Call',
            :due_at =>           Date.today
        )
        doc.update_columns(:has_reminder => true)
        puts "Task created for #{doc.id}"
    end
  end

  # desc "New editable fields for JI - update existing"
  task :ji_update1 => :environment do
    out = RfqJoiningInstruction.update_all(:no_booking_txt => RfqJoiningInstruction::NO_BOOKING, :prog_notes_li_one => RfqJoiningInstruction::PROG_NOTES_LI)
    puts out.inspect
  end


  # desc "store notional saving on complimentary bookings"
  task :comp_book_savings => :environment do
    comps = AccBooking.joins(:acc_booking_person, :acc_booking_header => {:rfq_location => {:rfq_responses => :rfq_response_rooms}}).
                       where("acc_booking_people.person_type = 'TRA' and rfq_responses.accepted_at is not null and
                          acc_bookings.complimentary is true and rfq_response_rooms.room_type = acc_bookings.room_type and
                           rfq_response_rooms.pkg_type = acc_bookings.pkg_type").
                           select("acc_bookings.*, rfq_response_rooms.price_inc_vat as would_have_cost_inc_vat, rfq_response_rooms.price_ex_vat as would_have_cost_ex_vat ")
    comps.map{|comp| comp.update_columns(:comp_saving_inc_vat => comp.would_have_cost_inc_vat, :comp_saving_ex_vat => comp.would_have_cost_ex_vat)}

  end

  # # for when virtual modules epic goes live
  # desc "mode of rfq for historic"
  task :set_mode => :environment do
    puts "-----------RFQ count: #{RfqRequest.count(:all)} ---"
    RfqRequest.update_all(:mode => RfqRequest::MODES[0])
    puts "-----------RFQ Requests with Residential mode: #{RfqRequest.where(:mode => "Residential").count(:all)} ---"
  end

  desc "change 648/674 to 'ServAce Client to Pay' from 'Manager/ Learner to Pay Entire'"
  task :move_648_674_to_HGCP => :environment do
    rfq_ids = [648, 674]


    rfq_ids.each do |r|
      r = RfqRequest.find r
      l = r.rfq_locations.first

      puts "RFQ #{r.id}"
      puts "-----------------------------------"
      puts "No of Headers  : #{l.acc_booking_headers.count}"
      puts "No of Bookings to change mtp  : #{l.acc_bookings.joins(:acc_booking_header).where("acc_booking_headers.payment_method = 'Manager/ Learner to Pay Entire' and no_nights_charge_manager > 0").count}"
      puts "No of Bookings already mtp 0  : #{l.acc_bookings.joins(:acc_booking_header).where("acc_booking_headers.payment_method = 'Manager/ Learner to Pay Entire' and no_nights_charge_manager = 0").count}"

      puts "No of Headers 'Manager/ Learner to Pay Entire' : #{l.acc_booking_headers.where(:payment_method => 'Manager/ Learner to Pay Entire' ).count}"

      r.update_column(:payment_method, 'ServAce Client to Pay')
      l.acc_booking_headers.where(:payment_method => 'Manager/ Learner to Pay Entire' ).update_all(:payment_method => 'HG Client to Pay')
      l.acc_bookings.where("acc_booking_headers.payment_method = 'Manager/ Learner to Pay Entire' and no_nights_charge_manager > 0").update_all(:no_nights_charge_manager => 0)

      puts "No of Headers 'ServAce Client to Pay' : #{l.acc_booking_headers.where(:payment_method => 'HG Client to Pay' ).count}"
      puts "No of Bookings mtp 0 : #{l.acc_bookings.joins(:acc_booking_header).where("acc_booking_headers.payment_method = 'HG Client to Pay'").where("no_nights_charge_manager = 0").count}"

      puts "-----------------------------------"

    end

  end

  # desc "subsistence provider added need default"
  task :loc_subs_prov => :environment do
    puts("---- RfqLocations to update = #{RfqLocation.count}")
    RfqLocation.update_all(:subsistence_provider => RfqLocation::SUBSISTENCE_PROVIDERS.first)
    puts("---- RfqLocations updated = #{RfqLocation.where(:subsistence_provider => RfqLocation::SUBSISTENCE_PROVIDERS.first).count}")
  end

  #   # rake rfq:add_ji_task['1 2 3']
  desc "Adds JI task to RFQs that don't have it, due to be created with 'Do not send joining instructions' option being ticked"
  task :add_ji_task, [:ids] => :environment do |t, args|
    puts "Task Started"
    args[:ids].split(' ').map{ |s| s.to_i }.each do |id|
      rfq = RfqRequest.find(id)
      add_ji_task_to_rfq(rfq)
    end
  end

  # rake rfq:add_task\['1687'\]
  desc "Adds task to RFQ"
  task :add_task, [:ids] => :environment do |t, args|
    puts "Task Started"
    args[:ids].split(' ').map{ |s| s.to_i }.each do |id|
      rfq = RfqRequest.find(id)
      puts "Found RFQ #{rfq.id}. #{rfq&.name}"
      RfqTask::APP_TASKS.each_with_index do |task, index|
        puts "For task: #{task[1][:name]} input: #{index}"
      end
      STDOUT.puts "Please select a task to add to the RFQ: #{rfq.id} by entering the task index number"
      input = STDIN.gets.chomp
      task_code = RfqTask::APP_TASKS.keys[input.to_i]
      add_task_to_rfq(task_code, rfq)
    end
  end

  # rake rfq:reload_bookings_within_rfqs\['1687'\]
  desc "Makes an update without changing anything, this is to refresh the rfq response room costs"
  task :reload_bookings_within_rfqs, [:ids] => :environment do |t, args|
    puts "Task Started"
    args[:ids].split(' ').map{ |s| s.to_i }.each do |id|
      rfq = RfqRequest.find(id)
      puts "Found RFQ #{rfq.id}. #{rfq&.name}"
      bookings = AccBooking.joins(acc_booking_header: :rfq_location).where("rfq_locations.rfq_request_id = ?", rfq.id)
      if bookings.any?
        puts "Found #{bookings.count} bookings for RFQ #{rfq.id}"
        bookings.each do |booking|

          if !booking.subsistence_only? && booking.rfq_response_room.present?
            puts "Updating booking ID: #{booking.id}"
            cost_before_save = booking.total_paid
            puts 'Room Rate is currently: ' + booking.pppn_inc_vat.to_s
            puts 'Updating to: ' + booking.rfq_response_room.price_inc_vat.to_s

            if (booking.pppn_inc_vat != booking.rfq_response_room.price_inc_vat)
              booking.update(updated_at: Time.now, total_cost_manager: booking.room_rate_plus_fees, pppn_inc_vat: booking.rfq_response_room.price_inc_vat)
              if booking.confirmed_flag && (cost_before_save != booking.total_cost_manager) && booking.booking_payments.charged.any?
                refund_amount = (booking.room_rate_plus_fees - cost_before_save ).to_pence
                if refund_amount.negative?
                  StripeCardPayment.new.refund_payment(booking.booking_payments.charged.first, booking, -refund_amount, 'Room rate has been amended')
                end
              end
            end
          else
            puts "Booking ID: #{booking.id} is a subsistence only booking"
          end
          
        end
      else
        puts "No bookings found for RFQ #{rfq.id}"
      end
    end
  end


  ###### UTILITY METHODS ##########


  def add_ji_task_to_rfq(rfq)
    puts "Found RFQ #{rfq.id}. Programme: #{rfq.rfq_programme.name}"
    tasks = rfq.rfq_locations.first.accepted_response.rfq_tasks
    if tasks.where(name: 'Joining Instructions').present?
      puts "This RFQ already has a JI Task"
    else
      puts "Setting 'Do not send joining instructions' option to false"
      rfq.update_column :no_ji, false

      puts "Removing Live status from RFQ"
      rfq.rfq_locations.first.update_column(:released_at, nil)
      rfq.rfq_locations.first.update_column(:released_by, nil)

      puts "Adding JI Task"
      ji_task = RfqTask::APP_TASKS["join_instr"]
      new_task = RfqTask.create(:rfq_response_id => rfq.rfq_locations.first.accepted_response.id,
                                :name => ji_task[:name],
                                :code => "join_instr",
                                :responsibility => ji_task[:resp],
                                :deadline => Date.today + ji_task[:deadline],
                                :position => ji_task[:position])
      if !tasks.where(name: 'Confirmation Form').present?
        puts "Adding Confirmation Form Task"
        conf_form_task = RfqTask::APP_TASKS["conf_form"]
        new_task = RfqTask.create(:rfq_response_id => rfq.rfq_locations.first.accepted_response.id,
          :name => conf_form_task[:name],
          :code => "conf_form",
          :responsibility => conf_form_task[:resp],
          :deadline => Date.today + conf_form_task[:deadline],
          :position => conf_form_task[:position])
      end
      puts "Adding Tasks Completed"
    end
  end

  def add_task_to_rfq(task_code, rfq)
    tasks = rfq.rfq_locations.first.accepted_response.rfq_tasks
    if tasks.where(code: task_code).present?
      puts "This RFQ already has that task!"
    else
      puts "Adding Task"
      task_template = RfqTask::APP_TASKS[task_code]
      new_task = RfqTask.create(:rfq_response_id => rfq.rfq_locations.first.accepted_response.id,
                                :name => task_template[:name],
                                :code => task_code,
                                :responsibility => task_template[:resp],
                                :deadline => Date.today + task_template[:deadline],
                                :position => task_template[:position])
      puts "Adding Tasks Completed"
    end
  end

end
