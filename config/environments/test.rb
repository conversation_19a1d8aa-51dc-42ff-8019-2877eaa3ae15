require 'active_support/core_ext/integer/time'

# The test environment is used exclusively to run your application's
# test suite. You never need to work with it otherwise. Remember that
# your test database is "scratch space" for the test suite and is wiped
# and recreated between test runs. Don't rely on the data there!

Rails.application.configure do
  # Configure 'rails notes' to inspect Cucumber files
  config.annotations.register_directories('features')
  config.annotations.register_extensions('feature') { |tag| /#\s*(#{tag}):?\s*(.*)$/ }

  # Settings specified here will take precedence over those in config/application.rb.

  # Store uploaded files on the local file system in a temporary directory.
  config.active_storage.service = :test

  config.cache_classes = true

  # Do not eager load code on boot. This avoids loading your whole application
  # just for the purpose of running a single test. If you are using a tool that
  # preloads Rails for running tests, you may have to set it to true.
  config.eager_load = false

  # Configure public file server for tests with Cache-Control for performance.
  config.public_file_server.enabled = true
  config.public_file_server.headers = {
    'Cache-Control' => "public, max-age=#{1.hour.to_i}"
  }

  # Show full error reports and disable caching.
  config.consider_all_requests_local       = true
  config.action_controller.perform_caching = false
  config.cache_store = :null_store

  # Raise exceptions instead of rendering exception templates.
  config.action_dispatch.show_exceptions = false

  # Disable request forgery protection in test environment.
  config.action_controller.allow_forgery_protection = true

  # Accept any CSRF token in test environment
  # config.action_controller.default_protect_from_forgery = false

  # Store uploaded files on the local file system in a temporary directory.
  config.active_storage.service = :test

  config.action_mailer.perform_caching = false

  # Tell Action Mailer not to deliver emails to the real world.
  # The :test delivery method accumulates sent emails in the
  # ActionMailer::Base.deliveries array.
  config.action_mailer.delivery_method = :test

  # Print deprecation notices to the stderr.
  config.active_support.deprecation = :stderr

  # Raise exceptions for disallowed deprecations.
  config.active_support.disallowed_deprecation = :raise

  # Tell Active Support which deprecation messages to disallow.
  config.active_support.disallowed_deprecation_warnings = []

  # Raises error for missing translations.
  # config.i18n.raise_on_missing_translations = true

  # Annotate rendered view with file names.
  # config.action_view.annotate_rendered_view_with_filenames = true

  # Set the logging level to :info
  config.log_level = ENV.fetch('LOGGER_LEVEL', 'debug').to_sym
  config.logger = ActiveSupport::Logger.new("log/test.log")
  config.action_controller.perform_caching = false
  config.middleware.insert_after Rails::Rack::Logger, ActionDispatch::DebugExceptions
  config.middleware.use Rails::Rack::Logger
end

port = ENV.fetch('RUBY_WEB_PORT', '3025')

CONFERENCESTOP_HOST = "localhost:#{port}"
ONESTOP_HOST = "localhost:#{port}"
P3_HOST = "localhost:#{port}"
CITB = [24]

EXCEPTION_PREFIX = '[HG-CRM-DEV]'

NEW_CONF_ACCOM_CUTOFF = Date.new(2015, 1, 27)
QUOTE_WIFI_PARKING_CUTOFF = Date.new(2015, 6, 1)

EVENTSTOP_HOME = 'http://localhost:3001'
EVENTSTOP_CREATE_EVENT = 'http://localhost:3001/events'
EVENTSTOP_DASH = 'http://localhost:3001/dashboard'
EVENTSTOP_ADMIN_VIEW = 'http://localhost:3001/hg_admin'
EVENTSTOP_REG_REDIRECT = 'http://localhost:3001?successful_reg=1'

legacy_eventstop_home =  'http://localhost:3001'
LEGACY_EVENTSTOP_HOME = legacy_eventstop_home
LEGACY_EVENTSTOP_CREATE_EVENT = "#{legacy_eventstop_home}/events"
LEGACY_EVENTSTOP_DASH = "#{legacy_eventstop_home}/dashboard"
LEGACY_EVENTSTOP_ADMIN_VIEW = "#{legacy_eventstop_home}/hg_admin"
LEGACY_EVENTSTOP_REG_REDIRECT = "#{legacy_eventstop_home}?successful_reg=1"

DIRECTORS = ['<EMAIL>', '<EMAIL>']
HG_DIRECTORS = DIRECTORS
TRAINLINE_ENDPOINT = 'https://et2-business-thetrainline.ttlnonprod.com/corporatessologin.aspx'
