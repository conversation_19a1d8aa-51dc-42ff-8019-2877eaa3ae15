Rails.application.routes.draw do
  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'
  # mount API => '/'

  # This code set relates to SSO using doorkeeper
  use_doorkeeper

  post '/webhooks', to: 'webhooks#receive'
  # post '/webhooks_connect', to: 'webhooks#receive_connected'

  # resources :credentials
  get '/user' => 'credentials#user'
  match '/auth/hg_one/authorize' => 'doorkeeper/authorizations#create', via: %i[get post]
  get 'virtual_redirector/:id', to: 'virtual_booking_confirmations#booking_redirector', as: :virtual_redirector
  get 'virtual_redirector_new/:id', to: 'virtual_bookings_multi_confirmations#booking_redirector_new',
                                    as: :virtual_redirector_new
  get 'active_job/status', to: 'active_job_status#status'

  get 'test_coverage', to: 'application#test_coverage' if !Rails.env.production? && !Rails.env.production?

  if Rails.env.production?
    constraints host: /hospitalityguaranteed/ do
      match '/(*anything)' => redirect { 'http://www.servace.co.uk/hospitality_guaranteed/home' }, via: [:get]
    end
    constraints host: /conferencestop/ do
      match '/(*anything)' => redirect { 'http://www.servace.co.uk/conference_stop/home' }, via: [:get]
    end
    constraints host: /accommodationstop/ do
      match '/(*anything)' => redirect { 'http://www.servace.co.uk/accommodation_stop/home' }, via: [:get]
    end
    constraints host: /apprenticestop/ do
      match '/(*anything)' => redirect { 'http://www.servace.co.uk/apprentice_stop/home' }, via: [:get]
    end
  end

  # Ensure polymorphic routes are available for conversations
  get 'conversations/:parent_id/:parent_type', to: 'conversations#index', as: :conversations
  get 'conversations/:parent_id/:parent_type/:id', to: 'conversations#show', as: :conversation
  post 'conversations/:parent_id/:parent_type', to: 'conversations#create', as: :conversations_create
  put 'conversations/:parent_id/:parent_type/:id', to: 'conversations#update', as: :conversation_update
  delete 'conversations/:parent_id/:parent_type/:id', to: 'conversations#destroy', as: :conversation_delete
  post 'conversations/:parent_id/:parent_type/:id/add_message', to: 'conversations#add_message', as: :conversation_add_message
  get 'conversations_filter/:parent_id/:parent_type', to: 'conversations#filter_values', as: :conversation_filter_values

  get 'conversations_dash/:parent_type', to: 'conversations_dash#index'

  get 'conversations_dash_filter/:parent_type', to: 'conversations_dash#filter_values'

  get 'current_user_information', to: 'users#current_user_information', as: :current_user_information

  namespace :apprentice do
    resources :bookings do
      collection do
        get :filter_data
        get :autocomplete_hotel_name
        get :create_booking_header
        get :create_booking_block
        get :create_acc_booking_person
        get :get_joining_instructions
        get :get_learners_to_notify
        post :notify_learners
        post :create_bookings
        get :export
        get :get_bookings
        put :bulk_confirm
        get :export_hotel_invoice_data
      end
      member do
        get :poll_for_export
        get :get_learners_for_provisional_assignment
      end
    end

    resources :business_units
  end

  namespace :conferences_new do
    resources :bookings do
      collection do
        get :filter_data
        get :autocomplete_hotel_name
        post :notify_learners
        post :create_bookings
        get :export
      end
      member do
        get :poll_for_export
      end
    end
  end

  namespace :academy do
    resources :bookings do
      collection do
        get :populate_filters
        get :filter_data
        get :autocomplete_hotel_name
        get :export
        get :get_bookings
        get :export_hotel_invoice_data
      end
      member do
        get :poll_for_export
        get :get_available_rooms
        patch :hotel_confirm_booking
        patch :hotel_decline_booking
        post :resend_proforma
        post :resend_stripe_receipt
        post :resend_confirmation
        patch :cancel_booking
        patch :update_dates
      end
      resources :booking_attendees, only: [:create, :update, :destroy] do
        member do
          patch :cancel_attendee
        end
      end
    end

    resources :business_units
  end

  resources :organisation_financials, only: [:index, :show] do
    collection do
      post :upload
      post :send_reminder_emails
    end
    member do
      post :approve_invoice
      post :reject_invoice
      post :send_approval_email
      post :test_7_day_reminder
    end
  end

  namespace :reports_dashboard do
    resources :apprentice_summary_report do
      collection do
        get :dashboard_booking_data, only: :json
        get :dashboard_issue_data, only: :json
        get :dashboard_feedback_data, only: :json
        get :dashboard_mobile_app_data, only: :json
        get :dashboard_notifications_data, only: :json
        get :dashboard_booking_adjusts_data, only: :json
        get :report_data, only: :json
        get :virtual_booking_report_data, only: :json
        get :report_data_by_room_type, only: :json
        get :complimentary_trainer_rooms, only: :json
        get :activities, only: :json
      end
    end
    resources :adult_summary_report do
      collection do
        get :report_data, only: :json
      end
    end
    resources :conferences_summary do
      collection do
        get :report_data, only: :json
        get :forecast_data, only: :json
        get :report_data_by_booker, only: :json
        get :report_data_for_preferred, only: :json
      end
    end
  end

  resources :stripe_reconciliation_reports do
    collection do
      get :credit_report_data, only: :json
      get :credit_report_download_csv, only: :json
      get :get_filter_data
      get :generate_data
      get :download_csv
      put :reconcile_payments
      put :invoice_payments
      get :get_hotel_monthly_data
      get :get_month_payment_data
      put :regenerate_reconciliation_report_current_year
    end
  end

  resources :booking_dates

  resources :rfq_tasks do
    resources :rfq_bu_accounts do
      collection do
        get :get_accounts
        get :get_business_units
        get :go_back
        patch :complete_task
        post :add_account
        put :bu_account_payment_method
      end
    end
    resources :joining_instructions do
      # anything that is a get without an ID goes in here
      collection do
        get :get_data
        get :jis_on_rfq
        get :ji_templates
        post :create_new_ji
      end
      member do
        get :get_data
        get :go_back
        post :save_attachments
        delete :remove_attachment
        post :save_logos
        delete :remove_logo
        post :remove_approval
        post :send_preview
        post :copy_ji
        delete :deleteJI
        get :get_date_in_advance
      end
    end
  end

  resources :rfq_locations, only: [] do
    resources :adult_bookings do
      collection do
        get :get_proposed_hotels
        get :get_all_hotels
        get :get_rfq_locations
        get :get_business_units
        get :get_payment_options
        get :get_academy_payment_options
        get :get_partner_logo
        post :create_partner_logo
        delete :delete_partner_logo
        get :get_welcome_image
        post :create_welcome_image
        delete :delete_welcome_image
        get :get_hotel_display_image
        post :create_hotel_display_image
        delete :delete_hotel_display_image
        post :create_bookings
      end
    end
  end

  constraints host: /./ do
    # allowed for any site

    devise_for :users,
               controllers: {
                 registrations: 'registrations',
                 sessions: 'sessions',
                 passwords: 'passwords',
                 unlocks: 'unlocks'
               }

    devise_scope :user do
      # Needed to signout from eventstop
      get '/ev/sign_out' => 'sessions#destroy'
      get '/:user_role/sign_up', to: 'registrations#new'
      get '/client/regions', to: 'registrations#regions'
    end

    get '/users/invited/:id', to: 'users#invited', as: :invited_user
    patch '/users/invited/:id', to: 'users#invited_update', as: :invited_user_update
    resources :communications, only: [] do
      collection do
        get :accept
        get :reject
      end
    end
    resources :airports do
      collection do
        get :autocomplete_airport_name
      end
    end
    resources :train_stations do
      collection do
        get :autocomplete_train_station_name
      end
    end

    resources :hotels, only: [] do
      collection do
        get :autocomplete_hotel_name
      end
    end

    resource :release_stamps, only: %i[show update] do
    end

    resources :releases, only: [:index] do
      member do
        get :download
      end
      collection do
        get :star_check
      end
    end

    get 'hotels/dupes', controller: :hotels, action: :dupes
    get 'hotels/reboot_time', controller: :hotels, action: :reboot_time
    get 'pref_rates_for', to: 'hotels#preferred_rates_for', as: :pref_rates_for
    get 'regions_for_country', to: 'hotels#regions_for_country', as: :regions_for_country
  end

  constraints host: /./ do
    post :redeem_points, to: 'cms/redemptions#redeem_points', as: :conference_stop_redeem_points
    # resources :users, :only => :show  # TODO - remove permanently?

    resource :profiles, only: %i[edit update], as: 'my_profile' do
      member do
        patch :update_password
        patch :update_opt_in
        get :expire_pw_now
      end
    end

    resources :towns do
      collection do
        get :autocomplete_town_name
      end
    end

    resources :acc_create_bookings, controller: :acc_create_bookings do
      collection do
        post :week_breakdown
        patch :add_prov_bookings
        patch :process_prov_bookings
        patch :add_staff_booking
        patch :process_staff_booking
        patch :add_learner_bookings
        get :search_learners
        get :new_virtual
        patch :add_learner_to_booking
        patch :new_block
        patch :add_block
        patch :add_group
        patch :search_group
        patch :process_group
      end
    end

    resources :bookings_virtual_multi_module do
      collection do
        get :new_virtual
        get :search_learners
        get :group_learners
        get :booking_summary
      end

      member do
        get :show_booking
        put :cancel
        put :cancel_single_booking
        put :undo_booking_cancellation
        put :update_special_requirements
        put :send_ji_now
        put :send_conf_now
      end
    end

    resources :acc_programmes, only: [] do
      member do
        get :upload_learners
        post :create_learners
        get :export_learners
        get :export_filtered_learners
        post :update_learners
      end
      resources :acc_learners do
        member do
          resource :acc_next_of_kin, except: [:destroy]
          get :bookings
          get :set_leaving_date
          put :leaving
          post :left
          patch :restore
          patch :hide
          patch :unhide
          post :set_password
          post :invite_learners_manager
        end
        collection do
          get :edit_group
          post :update_group
          get :prep_grp_leave
          get :notification_index
          get :invite_filtered_users
          patch :bulk_leave
          get :prep_grp_hide
          post :hide_group
          get :get_learners_for_transfer
          get :get_programmes_for_transfer
          post :transfer_learners
        end
        member do
          get :all_appointments, controller: :appointment
          post :appointment_save, controller: :appointment
          put 'appointment_cancel/:appointment_id', to: 'appointment#appointment_cancel'
          post 'appointment_update/:appointment_id', to: 'appointment#appointment_update'
        end
      end
      # resources :learner_managers, only: [:index]
      resources :acc_trainers, except: [:show]
      resources :acc_business_units do
        member do
          get :managers
        end
        collection do
          get :search_business_units
          post :delete_multiple
          post :merge
          post :bulk_edit
        end
      end

      resources :acc_trainers, except: [:show] do
        member do
          patch :leave
        end
      end
      resources :acc_business_units
    end

    resources :acc_issues do
      collection do
        get :learner_for_new
        get :rem_learner_for_new
        get :hotels_for_client
        get :rfqs_for_client
      end
      member do
        post :add_learner
        post :remove_learner
        # patch :learner_comment_visibility_update
      end
      resources :acc_attachments do
        collection do
          get :s3_ok_create
          get :s3_upload_form
          get :doc_list
        end
      end
      resources :acc_comments do
        resources :acc_attachments do
          collection do
            get :s3_ok_create
            get :s3_upload_form
            get :doc_list
          end
        end
      end
    end

    resources :acc_group_bookings do
      member do
        get :reset_group_form
        patch :process_reset_group
        put :update_joining_instruction
        get :edit_client_note
        patch :update_client_note
        get :amend_all_training_on_bookings
        patch :update_all_training_on_bookings
        post :confirm_all_bookings_for_tests
      end
      resources :acc_booking_people do
        collection do
          get :search_for_person
          patch :perform_search
          patch :selected_for_new
          patch :add_slot
          post :cancel_multiple
        end
        member do
          get :search_for_person
          patch :perform_search
          patch :select_learner
          get :pre_cancel
          patch :cancel
          patch :toggle_disabled
        end
      end

      resources :acc_booking_blocks do
        collection do
          patch :show_week_breakdown
        end
        member do
          get :pre_cancel
          patch :cancel
        end
      end
    end
    resources :acc_attachments do
      collection do
        get :s3_form_no_parent
        get :s3_create_no_parent
      end
    end
    resource :acc_chase_dashboard do
      member do
        get :summary
        get :bunits
      end
    end

    resource :availability_checks do
      member do
        get :get_rfqs
        get :get_rfqs_for_filter
        get :get_hotels
        get :get_hotel_rooms
        get :get_hotel_clients
        get :get_availability_checks
        post :save_availability_check
        put :edit_availability_check
        delete :delete_availability_check
        put :process_availability_check
        put :unprocess_availability_check
        post :remind_availability_checks
      end
    end

    resources :acc_bookings do
      resources :acc_booking_chases, only: %i[index show create] do
        collection do
          get :chase_index
        end
      end
      collection do
        get :live_programmes
        patch :bulk_confirm
        get :find_from_email
        get :get_rfqs
        post :bulk_create_bookings
        get :send_monthly_vat_summaries_to_managers
        post :refresh_bookings_results
      end
      member do
        patch :update_rfq_trainer
        get :cancel_booking_form
        patch :cancel_booking
        patch :undo_booking_confirmation
        patch :undo_booking_cancellation
        patch :undo_booking_confirmation
        # get :confirm_booking_form
        get :confirm_booking
        patch :send_ji_now
        patch :send_conf_now
        get :show_manager
        patch :resolve_booking
        get :spec_req
        patch :spec_req_save
        get :resend_stripe_receipt
        patch :confirm_booking_for_hotel
        put :mark_as_refunded
        get :get_payments_json
        get :send_payment_failed_email
        post :auto_pay_booking_for_tests
        get :audits
      end
    end

    resources :acc_non_arrivals do
      collection do
        get :get_programmes
        get :process_outstanding_non_arrivals
      end
    end

    resources :acc_trainer_bookings do
      collection do
        patch :room_picker
        get :new_trainer
        post :create_trainer
      end
    end

    resources :virtual_booking_confirmations

    #For multiple virtual bookings, may replace above
    resources :virtual_bookings_multi_confirmations
    # get "virtual_redirector/:id", :to => "virtual_booking_confirmations#booking_redirector", :as => :virtual_redirector

    resources :acc_booking_confirmations do
      collection do
        get :token_not_found
        get :no_stripe
      end
      member do
        get :already_processed
        get :payment_info
        post :card_payment
        get :confirmation
        get :check_booking
        put :update_payment_type
      end
    end
    resources :acc_booking_hotel_confirmations do
      member do
        get :pre_decline
        get :decline
        get :accept_group_booking
        get :decline_group_booking
      end
      collection do
        get :token_not_found
      end
    end
    resource :acc_issue_dashboard do
      collection do
        get :locations_for_org
        get :jump_to
      end
    end
    resources :virtual_modules do
      resources :virtual_time_slots, only: %i[new create destroy] do
      end
    end
    resources :virtual_join_instructions, only: %i[show edit update] do
      member do
        patch :approve
        patch :remove_logo
        get :s3_image_create
        get :s3_upload_form
      end
      collection do
        # get :new_from_approved
        # get :new_from_previous
      end
    end

    # ADMIN goes here
    draw :admin

    # CLIENT ###############################################
    namespace :client do
      resources :travel_forms do
        collection do
          get :get_data
        end

        member do
          get :attachments
          post :save_attachment
          delete :remove_attachment
        end
      end

      resource :dashboard do
        get :rfq
        get :app_feedback
        get :app_live
        get :adult_rfq
        get :possible_duplicates
      end

      resources :acc_staff do
        collection do
          get :upload
          post :bulk_create
        end
      end

      resources :shortlisted_hotels, only: %i[create destroy index]

      resources :delayed_jobs, only: [] do
        collection do
          get :csv_file_check
          get :csv_downloaded
        end
        member do
          get :download
        end
      end

      resources :attachments, except: %i[index show new edit update] do
        collection do
          get :s3_ok_create
          get :s3_image_create
          get :s3_upload_form
        end
      end

      resources :delayed_jobs, only: [] do
        collection do
          get :file_check
          get :downloaded
        end
        member do
          get :download
        end
      end
      resources :programmes do
        resources :rfq_booking_conf_docs do
          member do
            patch :accept
          end
        end
        resources :rfq_joining_instructions do
          collection do
            get :s3_image_create
            get :s3_upload_form
            get :new_from_approved
            get :new_from_previous
            get :epa_ji
          end
          member do
            patch :approve
            patch :remove_logo
          end
        end
      end
      resources :rfq_requests, only: %i[show edit update] do
        collection do
          get :view_conversations_dash
        end
        member do
          patch :confirm_virtual
          get :view_conversations
        end
        resources :rfq_tasks, only: [] do
          member do
            patch :accept
            get :attachments
            delete :destroy_attachment
            get :room_loadings
          end
          resources :rfq_documents do
            collection do
              get :s3_upload_form
              get :s3_ok_create
            end
          end
        end
      end
      resources :rfq_proposals
      resources :adult_rfq_proposals do
        member do
          post :complete_proposal
        end
      end
      resources :rfq_locations, only: [] do
      end
      resources :adult_rfq_tasks
      resources :rfq_responses, only: [:show] do
        resources :rfq_tasks, only: [] do
          member do
            patch :accept
            get :attachments
            delete :destroy_attachment
          end
        end
        resources :rfq_client_briefings, only: %i[edit show update]
        resources :rfq_hotel_briefings, only: [:show]
        member do
          patch :accept
        end
        resources :rfq_documents do
          member do
            patch :accept
          end
          collection do
            get :s3_upload_form
            get :s3_ok_create
            get :document_list
            get :edit_dates
            patch :update_dates
          end
        end
      end
      resources :adult_rfq_responses, only: [:show] do
        member do
          patch :accept
        end
      end
      # resource :dashboard, :only => [:show] # not wanted till P3
      resource :organisation, only: %i[show edit update]
      resource :terms_and_conditions, only: %i[edit update]
      resource :loyalty_scheme, only: :show do
        get :recommend_friend_info
      end
      resources :preferred_rate_agreements, only: %i[index show]
      resources :conferences do
        collection do
          get :autocomplete_contact_surname
          get :jump_to
          get :manage_codes
          get :le_selects
          get :view_conversations_dash
        end
        resources :timings, only: [] do
          collection do
            patch :update_positions
            get :toggle_sort
          end
        end
        member do
          get :view_conversations
          get :confirmation_doc
          get :documents
          get :bookings_for
          patch :final_details
          patch :toggle_code_retired
        end
        resources :quotations, only: %i[index update], path: 'proposal' do
          resources :attachments do
            member { get :s3_ok_create }
            collection { get :s3_ok_create }
          end
        end
        resources :attachments do
          collection { get :s3_ok_create }
        end
        resources :conference_dates, only: %i[show edit update] do
          member do
            patch :final_details
            patch :toggle_timings
          end
          resources :attachments, only: [] do
            collection { get :s3_ok_create }
          end
        end
      end
      resources :adult_bookings, only: [:show]
      resources :cs_bookings, only: %i[index show edit update] do
        member do
          patch :send_conf_now
          get :cancel_form
          patch :cancellation
        end
        collection do
          get :get_staff
          get :conferma_single_sign_on
        end
        resources :cs_booking_chases, only: %i[index show create] do
          collection do
            get :chase_index
          end
        end
      end

      resources :events, only: %i[index show] do
        resources :event_versions, only: %i[edit update]
        resources :event_add_texts do
          collection do
            get :clone
          end
          member do
            patch :publish
          end
          resources :event_images do
            member do
              patch :publish
              patch :publishing
            end
          end
        end
        resources :delegates, only: %i[index show] do
          resources :payments
          collection do
            get :cancellations
          end
          member do
            get :view_confirmation
          end
        end
        member do
          get :review_bookings
          get :remind_delegates
        end
      end
      resources :opportunities, only: [] do
        resources :attachments, only: [] do
          collection do
            get :s3_ok_create
          end
        end
      end
    end

    # top level
    resources :opportunities do
      collection do
        post :jump_to
      end
    end

    # SUPPLIER ###############################################
    namespace :supplier do
      resources :acc_non_arrivals, only: %i[index show]
      resources :blackout_dates
      resource :terms_and_conditions, only: %i[edit update]
      resource :dashboard, only: [:show] do
        get :rfq
        get :acc_stop
      end
      resources :opportunities, only: [] do
        resources :attachments, only: [] do
          collection do
            get :s3_ok_create
          end
        end
      end
      resources :news_articles, except: [:show]
      resources :offers, except: [:destroy]
      resources :rfq_bu_accounts do
        collection do
          get :edit_all
          post :update_all
        end
      end
      resources :rfq_requests do
        collection do
          get :adult_index
        end
        resources :rfq_tasks do
          member do
            patch :accept
            get :room_loadings
          end
        end
        resources :rfq_hotel_briefings, only: %i[edit update]
        resources :rfq_documents do
          member do
            patch :accept
          end
          collection do
            get :s3_upload_form
            get :s3_ok_create
            get :document_list
          end
        end
        member do
          get :declining
          patch :decline
        end
      end
      resource :chain do
        resources :contacts
      end
      resources :hotel_constructions, only: %i[index new create] do
        member do
          get :edit_stage_1
          patch :update_stage_1
          get :edit_stage_2
          patch :update_stage_2
          get :edit_stage_3
          patch :update_stage_3
          get :edit_stage_4
          patch :update_stage_4
          get :edit_stage_5
          patch :update_stage_5
          get :new_room
          get :edit_room
          post :create_room
          patch :update_room
          patch :build_hotel
        end
      end
      resources :hotels, except: %i[new create] do
        collection do
          get :upload, :csv_template
          post :upload_csv
        end
        resources :rooms
        resource :facilities
        resources :contacts
      end
      resources :conferences, only: [:show] do
        resources :conference_dates, only: [:show] do
          member do
            patch :final_details
          end
        end
        member do
          get :confirmation_doc
          get :documents
          patch :final_details
        end
      end
      resources :quotations, only: %i[edit update index show], path_names: { edit: 'provide' }
      resources :cs_bookings, only: %i[index show update]

      resources :preferred_rate_agreements, path: 'preferred_rates' do
        collection do
          get :historic
          get :clients_for
        end
      end

      resources :events, only: %i[index show] do
        resources :delegates, only: %i[index show] do
          collection do
            get :cancellations
          end
        end
      end
    end
  end

  scope module: 'cms' do
    constraints host: /./ do
      namespace :admin do
        resources :pages, except: [:show]
        resources :page_parts, only: %i[index edit update]
        resources :news, except: [:show] do
          get :autocomplete_hotel_name, on: :collection
        end
        resources :testimonials, except: [:show]
        resources :offers, except: [:show] do
          collection do
            get :chain_or_hotel
            get :chain_hotels
          end
        end
        resources :competitions do
          member { patch :set_winner }
        end
        resources :images, except: [:show]
        resources :offer_types, except: [:show]
        resources :feedback_questions, except: [:show]

        resources :blogs, except: [:show]
      end

      resources :blogs, only: %i[index show]

      get :register, to: 'hg_onestop#register', as: :os_register
      get :hotels, to: 'hg_onestop#hotels', as: :os_hotels_reg

      get :chains, to: 'hg_onestop#chains', as: :os_chains_reg

      get :home, to: 'pages#home', as: :conference_stop_home
      get :conference, to: 'pages#conference', as: :conference_stop_conference

      get :about, to: 'pages#about', as: :conference_stop_about
      get :key_principles, to: 'pages#key_principles', as: :conference_stop_key_principles
      get :'site-map', to: 'pages#sitemap', as: :conference_stop_sitemap
      get :hotel_how_to, to: 'pages#hotel_how_to', as: :conference_stop_hotel_how_to
      get :hotel_chain_how_to, to: 'pages#hotel_chain_how_to', as: :conference_stop_hotel_chain_how_to
      get 'hotel_details/:id', to: 'conference_stop#conference_facility', as: :conference_stop_hotel_details

      get :autocomplete_cities, to: 'pages#autocomplete_cities'
      # post :redeem_points,      :to => 'redemptions#redeem_points', :as => :conference_stop_redeem_points

      # HG Pages
      # get "hospitality_guaranteed/home", to: redirect("/"), :as => :hg_home
      get 'hospitality_guaranteed/home', to: 'hospitality_guaranteed#home', as: :hg_home
      get 'hospitality_guaranteed/why_us', to: 'hospitality_guaranteed#why_us', as: :hg_why
      get 'hospitality_guaranteed/loyalty_scheme', to: 'hospitality_guaranteed#loyalty_scheme', as: :hg_loyalty
      get 'hospitality_guaranteed/testimonials', to: 'hospitality_guaranteed#testimonials', as: :hg_test
      get 'hospitality_guaranteed/about_us', to: 'hospitality_guaranteed#about_us', as: :hg_about_us
      get 'hospitality_guaranteed/about_us/five_key', to: 'hospitality_guaranteed#five_key', as: :hg_five_key
      get 'hospitality_guaranteed/press_release/:id', to: 'hospitality_guaranteed#press_release',
                                                      as: :hg_press_release
      get 'hospitality_guaranteed/meet_the_team', to: 'hospitality_guaranteed#meet_the_team',
                                                  as: :hg_meet_the_team
      get 'hospitality_guaranteed/contact_us', to: 'hospitality_guaranteed#contact_us', as: :hg_contact_us
      # Servace
      get 'hg_onestop/home', to: 'hg_onestop#home', as: :os_home
      get 'hg_onestop/benefits', to: 'hg_onestop#benefits', as: :os_benefits
      get 'hg_onestop/my_hg_onestop', to: 'hg_onestop#my_hg_onestop', as: :os_myos
      get 'cookies', to: 'hg_onestop#cookies', as: :os_cookie

      # Conference Stop
      get 'conference_stop/home', to: 'conference_stop#home', as: :cs_home
      get 'conference_stop/conference_venues', to: 'conference_stop#conference_venues', as: :cs_venue_search
      get 'conference_stop/conference_facility/:id', to: 'conference_stop#conference_facility', as: :cs_hotel_view
      get 'conference_stop/loyalty', to: 'conference_stop#loyalty_scheme', as: :cs_loyalty
      get 'conference_stop/testimonials', to: 'conference_stop#testimonials', as: :cs_testimonials
      get 'conference_stop/benefits', to: 'conference_stop#benefits', as: :cs_benefit
      get 'conference_stop/packages', to: 'conference_stop#packages', as: :cs_package
      get 'conference_stop/recommend_friend', to: 'conference_stop#recommend_friend', as: :cs_recommend_friend

      # Accommodation Stop
      get 'accommodation_stop/home', to: 'accommodation_stop#home', as: :as_home
      get 'accommodation_stop/benefits', to: 'accommodation_stop#benefits', as: :as_benefit
      get 'accommodation_stop/packages', to: 'accommodation_stop#packages', as: :as_package

      # Apprentice Stop
      get 'apprentice_stop/home', to: 'apprentice_stop#home', as: :ap_home
      get 'apprentice_stop/benefits', to: 'apprentice_stop#benefits', as: :ap_benefit
      get 'apprentice_stop/packages', to: 'apprentice_stop#packages', as: :ap_package

      # Event Stop
      get 'event_stop/home', to: redirect('https://www.eventstop.co.uk'), as: :es_home
      get 'event_stop/benefits', to: redirect('https://www.eventstop.co.uk/benefits'), as: :es_benefit
      get 'event_stop/packages', to: redirect('https://www.eventstop.co.uk'), as: :es_package
      get 'event_stop/loyalty', to: redirect('https://www.eventstop.co.uk'), as: :es_loyalty

      # Offers
      # get ":site/offers", :to => "offers#index"
      # get ":site/offers/:id", :to => "offers#show"
      # news
      get ':site/news', to: 'news#index'
      get ':site/news/:id', to: 'news#show'
    end

    resources :pages
    resources :news, only: %i[index show]
    resources :testimonials, only: %i[index show]
    resources :competitions, only: %i[index show] do
      resources :entrants, only: [:create]
    end
    resources :offers, only: %i[index show]
    resources :messages, only: %i[new create]
    resources :newsletter_subscriptions, only: [:create]
    resources :enquiries

    resources :recommendations, only: [:create]
  end

  constraints host: /./ do
    root to: 'cms/hospitality_guaranteed#home'

    resource :profiles, only: %i[edit update], as: 'my_profile' do
      collection do
        patch :join_loyalty
      end

      member do
        patch :update_password
        get :expire_pw_now
        get :complete
      end
    end

    namespace :supplier do
      root to: 'hotels#index'
      resources :attachments, only: [] do
        collection do
          get :s3_upload_form
          get :s3_image_create
        end
      end

      resource :acc_stripe, controller: 'acc_stripe'

      resources :token_rfq_requests, only: [] do
        collection do
          get :edit
          patch :update
          get :declining
          patch :decline
          get :stripe_rates
        end
      end
      resource :acc_issue_dashboard

      resources :acc_attachments do
        collection do
          get :s3_form_no_parent
          get :s3_create_no_parent
        end
      end
      resources :acc_issues do
        member do
          post :add_learner
          post :remove_learner
        end
        resources :acc_attachments do
          collection do
            get :s3_ok_create
            get :s3_upload_form
            get :doc_list
          end
        end
        resources :acc_comments
      end
      resources :acc_bookings do
        collection do
          get :confirming
        end
        member do
          get :show_manager
          get :singling
          get :total_cost_manager
          patch :singled
          patch :twinning
          get :send_confirmation
          put :mark_as_refunded
          get :resend_stripe_receipt
        end
        resources :acc_stripe_payments do
          collection do
            get :get_stripe_payments
            patch :full_refund
          end
          member do
            put :refund
            patch :no_refund
          end
        end
        resources :acc_booking_adjusts
        member do
          patch :confirm
          patch :decline
        end
      end

      resources :acc_group_bookings, only: [:show] do
        member do
          patch :confirm
          patch :decline
        end
      end

      resources :adult_bookings, only: [:show]
      resources :delayed_jobs, only: [] do
        collection do
          get :file_check
          get :downloaded
        end
        member do
          get :download
        end
      end

      resource :chain do
        delete :remove_logo
        resources :contacts
        resources :clusters, except: [:edit] do
          member do
            patch :add_hotel
            patch :remove_hotel
            get :edit_email
            patch :update_email
          end
          collection do
            get :unclustered
          end
        end
      end
      resources :hotels do
        collection do
          get :upload, :csv_template
          post :upload_csv
        end
        resources :rooms, except: [:show]
        resource :facilities, except: [:show]
        resources :contacts
        resources :photos, except: [:show]
        member do
          get :add_contacts
          get :confirm_multi
          get :cancel_new
          patch :created_contacts
          post :create_contact
          patch :update_contact
          patch :update_contact_chain
          get :new_contact
        end
        collection do
        end
      end
      resources :conferences, only: [:show] do
        collection do
          post :jump_to
        end
      end
      resources :quick_quotes, only: [] do
        collection do
          get :edit
          patch :update
          get :declining
          patch :decline
          get :confirm
        end
      end
      resources :conference_dates, only: [] do
        resources :attachments, only: [] do
          collection { get :s3_ok_create }
        end
      end
      resources :quotations, only: %i[edit update index show], path_names: { edit: 'provide' } do
        resources :attachments, only: [] do
          collection { get :s3_ok_create }
        end
        member do
          get :decline
          patch :process_decline
          get :confirm
          get :accept_terms
          patch :toggle_contract_req
        end
      end
      resources :offers, except: [:destroy]
      resources :news_articles, except: [:show]
    end
  end

  resources :events, only: %i[index show] do
    resources :bookings, only: [:update] do
      collection do
        get :accommodation
        patch :update_accommodation
        get :packages
        patch :update_packages
        get :registration
        patch :update_registration
        get :payment
        patch :update_payment
        get :confirmation
      end
    end
  end

  namespace :payments do
    resources :credit_cards do
      collection do
        post :webhook
      end
    end
  end

  resources :bookings, only: [:show] do
    collection do
      get :search
      post :search_results
    end
  end

  resources :adult_accommodations do
    collection do
      post :stage_2
      post :stage_3
      get :not_found
      get :bu_address
    end
  end

  resource :make_stripe_payments

  # Pulls in the routes for the feedback 'app'
  draw :feedback

  # Stripe Connect endpoints
  #  - oauth flow
  get '/connect/oauth' => 'stripe#oauth', as: 'stripe_oauth'
  get '/connect/standalone' => 'stripe#standalone', as: 'stripe_standalone'
  get '/connect/confirm' => 'stripe#confirm', as: 'stripe_confirm'
  get '/connect/deauthorize' => 'stripe#deauthorize', as: 'stripe_deauthorize'
  #  - create accounts
  post '/connect/managed' => 'stripe#managed', as: 'stripe_managed'
  # post '/connect/standalone' => 'stripe#standalone', as: 'stripe_standalone'
  get '/stripe_rates' => 'stripe#stripe_rates', as: 'stripe_rates'
  get '/get_stripe_details' => 'stripe#get_stripe_details', as: 'get_stripe_details'

  scope module: 'cms' do
    get '*page', to: 'integrated_page#show'
  end
end
