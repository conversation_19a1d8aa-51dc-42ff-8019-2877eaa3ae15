const Modules = require('../modules');
const fakerData = require('../modules/helpers/generate_faker_data');

// Function to delete duplicate hotels
function deleteDupeHotel(hotelName) {
    cy.clickLinkByText('Hotels');
    cy.get('.buttonswrapper > .btn-primary').contains('Search').click();
    cy.get('tr').contains(hotelName).then(($rows) => {
        if ($rows.length > 1) {
            $rows.slice(1).each((index, row) => {
                cy.wrap(row).within(() => {
                    cy.get('td').eq(0).invoke('text').then((hotelId) => {
                        cy.get(`a[href*="admin/hotels/${hotelId.trim()}"]`).contains('Delete').click();
                    });
                });
            });
        } else {
            cy.log('Only one entry of the hotel exists, skipping deletion.');
        }
    });
}

describe('E2E Adult RFQ booking flow setup from scratch', () => {
    beforeEach(() => {
        console.log(Modules);
        cy.login();
        cy.url().should('include', '/admin/dashboard');
    });

    context('Create organisations and their dependencies', () => {
        beforeEach(function () {
            if (this.currentTest.title !== 'Creates a client organisation') {
                cy.clickLinkByText('Organisations');
                cy.clickLinkByText(fakerData.organisationName);
            }
        });

        it('Creates a client organisation', () => {
            Modules.createOrganisation(1, fakerData.organisationName);
        });

        it('creates a client user', () => {
            Modules.createUser('client', 1, fakerData.clientUser);
        });

        it('creates an adult programme', () => {
            Modules.createProgramme(fakerData.adultProgrammeName, 'adult', fakerData.clientUser);
        });

        it('creates BU & learners', () => {
            Modules.createBU(fakerData.buName, fakerData.adultProgrammeName);
            Modules.createLearners(5);
        });
    });

    context('Create a hotel organisation and its dependencies', () => {
        let hotelId;

        before(() => {
            cy.intercept('GET', '**/s3_upload_form', (req) => {
                req.reply((res) => {
                    res.body =
                        '<div id="s3-new-image-attachment"><input type="hidden" name="s3_attachment_id" id="s3_attachment_id" value="293174" autocomplete="off" /></div>';
                });
            });
        });

        it('Creates a hotel', () => {
            Modules.createHotel(fakerData.hotelName);
            deleteDupeHotel(fakerData.hotelName);
        });

        it('Creates a hotel user', () => {
            Modules.navToHotel(fakerData.hotelName, hotelId);
            Modules.createUser('hotel', 1, fakerData.hotelUser);
        });

        it('Creates a hotel quotation', () => {
            Modules.createHotelQuotation(fakerData.hotelName, hotelId, fakerData.hotelUser);
        });

        it('Completes & verifies hotel creation', () => {
            Modules.navToHotel(fakerData.hotelName, hotelId);
            Modules.completeHotelCreation(fakerData.hotelName, fakerData.hotelUser);
        });

        it('Should build the hotel', () => {
            Modules.buildHotel(fakerData.hotelName);
        });

        it('Should set the hotel user as stripe contact', () => {
            Modules.navToHotel(fakerData.hotelName, hotelId);
            Modules.setHotelUser(fakerData.hotelUser);
        });
    });

    context('Create an RFQ', () => {
        beforeEach(() => {
            cy.clickLinkByText('Organisations');
            cy.clickLinkByText(fakerData.organisationName);
            cy.get('a[href*="/organisations/"]').contains('Programmes').click();
        });

        it('Should create an Adult Residential RFQ', () => {
            cy.contains(fakerData.adultProgrammeName).click();
            cy.contains('Rfq Requests').click({ force: true });
            Modules.createRFQ('adult');
        });

        it('Should send a proposal to hotels', () => {
            cy.url().then((url) => {
                cy.clickLinkByText(fakerData.adultProgrammeName);
                cy.contains('Rfq Requests').click({ force: true });
                cy.get('tr').contains('RESIDENTIAL').parent().within(() => {
                    cy.get('a[class="btn btn-primary btn-small"]').contains('Show').click();
                });
                cy.clickLinkByText('Manage proposed hotels');
                Modules.sendProposalToHotel(fakerData.hotelName, fakerData.hotelUser);
                cy.visit(url);
            });
        });

        it('Should complete the RFQ proposal as hotel', () => {
            cy.url().then((url) => {
                Modules.completeRFQResponse(fakerData.hotelName, fakerData.hotelUser, 'adult');
                cy.visit(url);
            });
        });

        it('Should finish the proposal as admin', () => {
            Modules.finishProposalAsAdmin(fakerData.adultProgrammeName, 'adult');
        });

        it('Should accept the proposal as client', () => {
            Modules.acceptProposalAsClient(fakerData.clientUser, fakerData.adultProgrammeName, 'adult');
        });
    });

    context('adult RFQ Task flow', () => {
        beforeEach(() => {
            cy.clickLinkByText('Programmes');
            cy.clickLinkByText(fakerData.adultProgrammeName);
            cy.contains('Rfq Requests').click({ force: true });
            cy.clickButtonInRowWithText('RESIDENTIAL', 'Show');
            cy.url().should('include', '/admin/rfq_requests');
            cy.clickLinkByText('RFQ Task Management');
            cy.url().should('include', '/rfq_tasks');
        });

        it('Should complete the RFQ additional Client and Hotel information tasks', () => {
            ['Additional Client Information', 'Additional Hotel Information'].forEach((task) => {
                const type = task.includes('Client') ? 'rfq_client' : 'rfq_hotel';
                Modules.completeAdditionalInfoTask(task, type);
            });
        });

        it('should complete Hotel Menu', () => {
            Modules.completeHotelMenuTask.completeAdminSideHotelMenuTask();
            cy.navigateToManageTasks('hotel', fakerData.hotelUser, fakerData.adultProgrammeName, 'adult');
            Modules.completeHotelMenuTask.completeHotelSideHotelMenuTask();
        });

        it('Completes block programme', () => {
            cy.clickButtonInRowWithText('Block Programme', 'Uploads');
            cy.clickLinkByText('Upload New');
            cy.get('#override-modal-tall').should('be.visible');
            cy.get('.modal-footer > .btn').click();
            cy.navigateToManageTasks('hotel', fakerData.hotelUser, fakerData.adultProgrammeName, 'adult');
            cy.get('tr').contains('Block Programme').parent().within(() => {
                cy.get('.btn').contains('Accept').eq(-1).click();
            });
            cy.get('#flash_notice').should('contain', 'Successfully accepted Block Programme');
            cy.stopActingAsUser();
        });

        it('Should complete the RFQ room loading task', () => {
            Modules.completeRoomLoadingTask();
            cy.navigateToManageTasks('hotel', fakerData.hotelUser, fakerData.adultProgrammeName, 'adult');
            cy.clickButtonInRowWithText('Confirm Room Loading', 'View + Approve');
            cy.get('.btn-danger').contains('Accept').click();
            cy.get('#flash_notice').should('contain', 'successfully completed the task');
            cy.stopActingAsUser();
        });

        it('Should complete the RFQ Sign Off Welcome Letter task', () => {
            Modules.completeReleaseAndAcceptSignOffTask(
                'Sign Off Welcome Letter',
                fakerData.hotelUser,
                fakerData.clientUser,
                fakerData.adultProgrammeName,
                'adult'
            );
        });

        //this needs wrapping in if(!cypress.env(use_stripe_payment)) when the task is correctly hidden 
        it('Should complete the RFQ hotel credit application task', () => {
            Modules.completeCreditApplicationTask();
        });

          it('Should add a credit account to a BU', () => {
            Modules.addCreditAccount(fakerData.buName, fakerData.adultProgrammeName, fakerData.hotelUser);
        });

        it('Should complete the RFQ SLA tasks and release the RFQ', () => {
            Modules.completeSlasAndReleaseRfq();
        });
    });
});
