const modules = require('../modules');

describe('Adult Bookings Tests', () => {

    beforeEach(() => {
        cy.login();
        cy.url().should('include', '/admin/dashboard');
        cy.contains('a', 'Programmes').click();
        cy.get('tbody').within(() => {
            cy.get('tr').filter((index, el) => {
                const text = Cypress.$(el).text().toLowerCase();
                return text.includes('adult') && text.includes('rfq') && text.indexOf('adult') < text.indexOf('rfq');
            }).first().within(() => {
                cy.contains('a', 'Adult').click();
            });
        });
        cy.url().should('include', '/admin/rfq_requests');
        cy.contains('a', 'Create Booking').then($a => {
            const href = $a.attr('href');
            cy.visit(href);
        });
        cy.url().should('include', '/adult_booking');
    });

    context('Happy Path', () => {

        const cardTests = [
            { name: 'Visa', cardNumber: '****************' },
            { name: 'Visa Debit', cardNumber: '****************' },
            { name: 'Mastercard', cardNumber: '****************' },
            { name: 'Mastercard Debit', cardNumber: '****************' },
        ];

        const cardDetails = {
            expiry: "12/35",
            security_code: "777",
            postcode: "WS11 1DB"
        };

        it.only('Should make a basic card payment booking successfully', () => {
            modules.fillInFormDetails();
            // Example: use the first card for the test
            const fullCardDetails = { ...cardTests[0], ...cardDetails };
            modules.payForBooking(fullCardDetails);
        });

        cardTests.forEach(({ name, cardNumber }) => {
            it(`Should create & confirm an adult booking with a ${name} card`, () => {
                modules.fillInFormDetails();
                expect(cardNumber).to.be.a('string'); // Placeholder
            });
        });

    });

    context('Unhappy Path', () => {

        const declinedCardTests = [
            { name: 'Generic Decline', cardNumber: '****************', declineCode: 'generic_decline', errorType: 'card_declined' },
            { name: 'Insufficient Funds Decline', cardNumber: '****************', declineCode: 'insufficient_funds', errorType: 'card_declined' },
            { name: 'Lost Card Decline', cardNumber: '****************', declineCode: 'lost_card', errorType: 'card_declined' },
            { name: 'Stolen Card Decline', cardNumber: '****************', declineCode: 'stolen_card', errorType: 'card_declined' },
            { name: 'Expired Card Decline', cardNumber: '****************', declineCode: null, errorType: 'expired_card' },
            { name: 'Incorrect CVC Decline', cardNumber: '****************', declineCode: null, errorType: 'incorrect_cvc' },
            { name: 'Processing Error Decline', cardNumber: '****************', declineCode: null, errorType: 'processing_error' },
            { name: 'Incorrect Number Decline', cardNumber: '****************', declineCode: null, errorType: 'incorrect_number' },
        ];

        declinedCardTests.forEach(({ name, cardNumber, errorType }) => {
            it(`Should handle booking creation with a ${name}`, () => {
                modules.fillInFormDetails();
                expect(cardNumber).to.be.a('string'); // Placeholder for actual test logic
            });
        });
    });
});
