import dayjs from 'dayjs';

describe('Manager Dashboard', () => {

  const roles = [
    { role: 'admin', shouldSeeReports: false, alertText: 'Problem! You have not got access to any programmes to view in the reports.' },
    { role: 'manager', shouldSeeReports: true, user: '<PERSON><PERSON>' },
    { role: 'exec manager', shouldSeeReports: true, user: '<PERSON>' },
  ];

  const tabs = ['Spend By Residential Programme', 'Spend By Virtual Programme', 'Spend By Room Type', 'Complimentary Trainer Rooms', 'Activities', 'Manager Dashboard'];
  const cardTitles = ['Bookings', 'Issues', 'Feedback', 'Mobile App', 'Notifications', 'Booking Adjustments'];
  const vertCardTitles = ['Late Bookings', 'Urgent Confirmation Required', 'Open P1 Issues', 'Feedback Completed', 'Mobile App Registration Rate'];
  const bookingTypes = ['confirmed', 'pending', 'cancelled'];
  const subCardBookingTypes = ['Late Bookings', 'Urgent Confirmation Required'];
  const adjustmentTypes = ['Early Arrivals', 'Non Arrivals'];
  const issueTypes = ['Closed Issues', 'Open Issues'];
  const feedbackTypes = ['Not Complete', 'Complete'];

  function getUserDetails() {
    cy.visit('/admin/organisations')
  }

  function clickOnType(type, alias, typeLabel) {
    cy.wait(alias).then((interception) => {
      const { data, sub_card_data: subCardData } = interception.response.body;
      const section = typeLabel === 'UrgentLateBooking'
        ? subCardData.find(section => section.title.toLowerCase() === type.toLowerCase())
        : data.find(section => section.name.toLowerCase() === type.toLowerCase());

      if (!section) {
        throw new Error(`${typeLabel} type "${type}" not found in the response`);
      }

      const { redirectURL, value } = section;
      cy.visit(redirectURL);
      if (typeLabel === 'Booking') {
        cy.get('[data-cy="bookingsTotal"]').should('contain.html', `Found: ${value} bookings`);
      } else if (typeLabel.includes('Feedback')) {
        cy.get('body').should('be.visible');
      } else if (value === 0) {
        cy.get('table tbody tr').should('have.length', 0);
      } else {
        cy.get('table tbody tr').should('have.length', value);
      }
    });
  }

  function navToManagerReports() {
    cy.contains('a', 'Bookings').click();
    cy.url().should('include', '/apprentice/bookings');
    cy.contains('a', 'Reports Dashboard').click();
    cy.url().should('include', '/reports_dashboard/apprentice_summary_report');
    cy.get('[data-cy="loadingSpinner"]').should('not.exist');
  }

  beforeEach(() => {
    cy.visit(Cypress.env('login_url'));
    cy.login();
    cy.visit(Cypress.env('admin_dashboard_url'));
    cy.url().should('include', '/admin/dashboard');
  });

  context('Role permissions in /users for the reports dash', () => {
    roles.forEach(({ role, shouldSeeReports, alertText, user }) => {
      it(`should ${shouldSeeReports ? '' : 'NOT '}show the reports dash for a ${role}`, () => {
        if (shouldSeeReports) {
          cy.actAs(user, 'Client');
          navToManagerReports();
          cy.get('[data-cy="loadingSpinner"]').should('not.exist');
          cy.get('[data-cy="reportSections"]').should('be.visible');
          tabs.forEach((tab) => {
            cy.contains('div', tab).click({ force: true });
            cy.get('body').should('be.visible');
            cy.document().should('have.property', 'readyState', 'complete');
          });
        } else {
          navToManagerReports();
          cy.get('[class="alert alert-danger"]').should('contain', alertText);
        }
      });
    });
  });

  context('Manager Reports dash as manager/exec', () => {
    roles.forEach(({ role, shouldSeeReports, user }) => {
      if (shouldSeeReports) {
        it(`should show the correct data for the ${role} user`, () => {
          cy.actAs(user, 'Client');
          navToManagerReports();
          cy.get('[data-cy="reportSections"]').should('be.visible');
          cy.get('[data-cy="chartCard"]').each(($el) => {
            cy.wrap($el).within(() => {
              cy.get('.card_header').should('have.css', 'background-color', 'rgb(0, 79, 89)');
              cy.get('[data-cy="chartTitle"]').each(($title, index) => {
                if ($title.text().includes(cardTitles[index])) {
                  cy.wrap($title).should('contain', cardTitles[index]);
                }
              });
              cy.get('[data-cy="chartSubtitle"]').should('be.visible');
              cy.get('[data-cy="chart"]').should('be.visible');
            });
          });

          cy.get('[class="col-2"]').each(($el) => {
            cy.wrap($el).within(() => {
              cy.get('.card_header').should('have.css', 'background-color', 'rgb(0, 79, 89)');
              cy.get('[data-cy="chartTitle"]').each(($title, index) => {
                if ($title.text().includes(vertCardTitles[index])) {
                  cy.wrap($title).should('contain', vertCardTitles[index]);
                }
              });
            });
          });
        });
      }
    });

    function validateData(type, alias, endpoint, typeLabel) {
      const isIssueOrFeedback = endpoint.includes('issue') || endpoint.includes('feedback');
      let checkinStart = isIssueOrFeedback ? dayjs().subtract(4, 'weeks').format('YYYY/MM/DD') : dayjs().format('YYYY-MM-DD');
      let checkinEnd = isIssueOrFeedback ? dayjs().format('YYYY/MM/DD') : dayjs().add(4, 'weeks').format('YYYY-MM-DD');

      if (endpoint.includes('adjusts')) {
        checkinStart = dayjs().subtract(4, 'weeks').format('YYYY/MM/DD').replace(/\//g, '%2F');
        checkinEnd = dayjs().format('YYYY/MM/DD').replace(/\//g, '%2F');
      } else if (isIssueOrFeedback) {
        checkinStart = checkinStart.replace(/\//g, '%2F');
        checkinEnd = checkinEnd.replace(/\//g, '%2F');
      }

      const url = endpoint.includes('issue')
        ? `/reports_dashboard/apprentice_summary_report/${endpoint}?fromDate=${checkinStart}`
        : endpoint.includes('feedback')
        ? `/reports_dashboard/apprentice_summary_report/${endpoint}?startDate=${checkinStart}&endDate=${checkinEnd}`
        : `/reports_dashboard/apprentice_summary_report/${endpoint}?checkin_start=${checkinStart}&checkin_end=${checkinEnd}`;

      cy.intercept('GET', url).as(alias);
      cy.actAs('Jonny Connelly', 'Client');
      navToManagerReports();
      clickOnType(type, `@${alias}`, typeLabel);
    }

    bookingTypes.forEach((type) => {
      it(`should validate that the booking chart data matches the booking index data for ${type} bookings`, () => {
        validateData(type, 'getBookingData', 'dashboard_booking_data', 'Booking');
      });
    });

    subCardBookingTypes.forEach((type) => {
      it(`should validate that the urgent/late bookings data matches the booking index data for ${type}`, () => {
        validateData(type, 'getSubCardBookingData', 'dashboard_booking_data', 'UrgentLateBooking');
      });
    });

    adjustmentTypes.forEach((type) => {
      it(`should validate that the adjustment chart data matches the booking index data for ${type}`, () => {
        validateData(type, 'getAdjustmentData', 'dashboard_booking_adjusts_data', 'Adjustment');
      });
    });

    issueTypes.forEach((type) => {
      it(`should validate that the issue chart data matches the issue index data for ${type}`, () => {
        validateData(type, 'getIssueData', 'dashboard_issue_data', 'Issue');
      });
    });

    feedbackTypes.forEach((type) => {
      it(`should validate that the feedback chart data matches the feedback index data for ${type}`, () => {
        validateData(type, 'getFeedbackData', 'dashboard_feedback_data', 'Feedback');
      });
    });
  });
});
