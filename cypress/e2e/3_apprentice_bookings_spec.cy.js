const Modules = require('../modules');

describe('Booking Creation Tests', () => {
    context('Creating Bookings - Vue style', () => {
        beforeEach(() => {
            cy.login();
            cy.visit(Cypress.env('new_bookings_url'));
            cy.get('[data-cy="createBookingBtn"]').contains('Add Booking').should('be.visible').click();
            cy.url().should('include', '/apprentice/bookings/new');
            cy.get('[data-cy="selectRFQ"]').should('be.visible');
        });

        it('Should create & confirm a booking', () => {
            // params: bookingType, rfqType, checkInDay, checkOutDay
            // defining the bookingID this way allows you to create a booking and use the ID in the same test
            // this is useful for testing the booking confirmation process
            const bookingID = Modules.createBooking('Residential', 'Apprentice', '12-06-2025', '15-06-2025');
            Modules.confirmBooking('Residential');

            cy.log(`Booking ID: ${bookingID}`);
            // Example usage: Verify the booking ID is not null or undefined
            expect(bookingID).to.not.be.null;
            expect(bookingID).to.not.be.undefined;
        });
    });

    context.only('Creating Bookings - Legacy style', () => {
        beforeEach(() => {
            cy.visit(Cypress.env('login_url'));
            cy.login();
            cy.visit(Cypress.env('admin_dashboard_url'));
            cy.contains('a', 'Bookings').click();
            cy.url().should('include', '/bookings');
            cy.contains('a', 'Create New').click();
        });

        it('Should create a booking via the legacy booking flow', () => {
            Modules.createBookingViaLegacyFlow('12-07-2025', '15-07-2025', false, false);
            Modules.payForBooking();
        });
    });
});
