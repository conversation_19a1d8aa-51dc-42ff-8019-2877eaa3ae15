const Modules = require('../modules');
const fakerData = require('../modules/helpers/generate_faker_data');

// Function to delete duplicate hotels
function deleteDupeHotel(hotelName) {
  cy.clickLinkByText('Hotels');
  cy.get('.buttonswrapper > .btn-primary').contains('Search').click();
  cy.get('tr').contains(hotelName).then(($rows) => {
    if ($rows.length > 1) {
      $rows.slice(1).each((index, row) => {
        cy.wrap(row).within(() => {
          cy.get('td').eq(0).invoke('text').then((hotelId) => {
            cy.get(`a[href*="admin/hotels/${hotelId.trim()}"]`).contains('Delete').click();
          });
        });
      });
    } else {
      cy.log('Only one entry of the hotel exists, skipping deletion.');
    }
  });
}

describe('E2E ApprenticeStop booking flow setup from scratch', () => {
  beforeEach(() => {
    console.log(Modules);
    cy.login();
    cy.url().should('include', '/admin/dashboard');
  });

  context('Create organisations and their dependencies', () => {
    beforeEach(function () {
      if (this.currentTest.title !== 'creates a client organisation') {
        cy.clickLinkByText('Organisations');
        cy.clickLinkByText(fakerData.organisationName);
      }
    });

    it('creates a client organisation', () => { 
      Modules.createOrganisation(1, fakerData.organisationName); 
    });
    
    it('creates a client user', () => { 
      Modules.createUser('client', 1, fakerData.clientUser); 
    });

    it('creates an apprentice programme', () => { 
      Modules.createProgramme(fakerData.apprenticeProgrammeName, 'apprentice', fakerData.clientUser); 
    });

    it('creates BU & learners', () => {
      Modules.createBU(fakerData.buName, fakerData.apprenticeProgrammeName);
      Modules.createLearners(5);
    });
  });

  context('Create a hotel organisation and its dependencies', () => {
    let hotelId;

    before(() => {
      cy.intercept('GET', '**/s3_upload_form', (req) => {
        req.reply((res) => {
          res.body =
            '<div id="s3-new-image-attachment"><input type="hidden" name="s3_attachment_id" id="s3_attachment_id" value="293174" autocomplete="off" /></div>';
        });
      });
    });

    it('Creates a hotel', () => {
      Modules.createHotel(fakerData.hotelName);
      deleteDupeHotel(fakerData.hotelName, 2);
    });

    it('Creates a hotel user', () => {
      Modules.navToHotel(fakerData.hotelName, hotelId);
      Modules.createUser('hotel', 1, fakerData.hotelUser);
    });

    it('Creates a hotel quotation', () => {
      Modules.createHotelQuotation(fakerData.hotelName, hotelId, fakerData.hotelUser);
    });

    it('Completes & verifies hotel creation', () => {
      Modules.navToHotel(fakerData.hotelName, hotelId);
      Modules.completeHotelCreation(fakerData.hotelName, fakerData.hotelUser);
    });

    it('Should build the hotel', () => { 
      Modules.buildHotel(fakerData.hotelName); 
    });

    it('Should set the hotel user as stripe contact', () => {
      Modules.navToHotel(fakerData.hotelName, hotelId);
      Modules.setHotelUser(fakerData.hotelUser);
    });
  });

  context('Create an RFQ', () => {

    //probably not needed now 
    let programmeType = 'apprentice';

    beforeEach(() => {
      cy.clickLinkByText('Organisations');
      cy.clickLinkByText(fakerData.organisationName);
      cy.get('a[href*="/organisations/"]').contains('Programmes').click();
    });

    it('Should create an Apprentice Residential RFQ', () => {
      cy.contains(fakerData.apprenticeProgrammeName).click();
      cy.contains('Rfq Requests').click({ force: true });
      Modules.createRFQ('apprentice');
    });

    // deliberately putting this test here to give the delayed jobs time to run for proposals
    it('Should create an Apprentice Virtual RFQ', () => {
      cy.contains(fakerData.apprenticeProgrammeName).click();
      cy.contains('Rfq Requests').click({ force: true });
      Modules.createRFQ('virtual');
    });

    it('Should send a proposal to hotels', () => {
      cy.url().then((url) => {
        cy.clickLinkByText(fakerData.apprenticeProgrammeName);
        cy.contains('Rfq Requests').click({ force: true });
        cy.get('tr').contains('RESIDENTIAL').parent().within(() => {
          cy.get('a[class="btn btn-primary btn-small"]').contains('Show').click();
        });
        cy.clickLinkByText('Manage proposed hotels');
        Modules.sendProposalToHotel(fakerData.hotelName, fakerData.hotelUser);
        cy.visit(url);
      });
    });

    it('Should complete the RFQ proposal as hotel', () => {
      cy.url().then((url) => {
        Modules.completeRFQResponse(fakerData.hotelName, fakerData.hotelUser, programmeType);
        cy.visit(url);
      });
    });

    it('Should finish the proposal as admin', () => {
      Modules.finishProposalAsAdmin(fakerData.apprenticeProgrammeName, programmeType);
    });

    it('Should accept the proposal as client', () => {
      Modules.acceptProposalAsClient(fakerData.clientUser, fakerData.apprenticeProgrammeName, programmeType);
    });
  });

  //expand this context as required
  context('configure the RFQ', () => {
    beforeEach(() => {
      cy.clickLinkByText('Programmes');
      cy.clickLinkByText(fakerData.apprenticeProgrammeName);
    });

    it('Should add fees to RFQ', () => {
      Modules.addAdminFees(1); // Call with desired fee value
    });
  });

  context('apprentice RFQ Task flow', () => {
    let programmeName = fakerData.apprenticeProgrammeName;
    let programmeType = 'apprentice';

    beforeEach(() => {
      cy.clickLinkByText('Programmes');
      cy.clickLinkByText(programmeName);
      cy.contains('Rfq Requests').click({ force: true });
      cy.clickButtonInRowWithText('RESIDENTIAL', 'Show');
      cy.url().should('include', '/admin/rfq_requests');
      cy.clickLinkByText('RFQ Task Management');
      cy.url().should('include', '/rfq_tasks');
    });

    it('Should complete the RFQ additional Client and Hotel information tasks', () => {
      ['Additional Client Information', 'Additional Hotel Information'].forEach((task) => {
        const type = task.includes('Client') ? 'rfq_client' : 'rfq_hotel';
        Modules.completeAdditionalInfoTask(task, type);
      });
    });

    it('should complete CRB / Disclosure of barring letter', () => {
      cy.clickButtonInRowWithText('CRB / Disclosure of barring letter', 'Uploads');
      cy.clickLinkByText('Upload New');
      cy.get('.modal-footer > .btn').click();
      const tryAccept = (retries = 3) => {
        cy.reload();
        cy.get('a').contains('Accept', { timeout: 5000 }).then($acceptBtn => {
          if ($acceptBtn.length) {
            cy.wrap($acceptBtn).should('be.visible').click({ force: true });
          } else if (retries > 0) {
            tryAccept(retries - 1);
          } else {
            throw new Error("'Accept' button not found after retries");
          }
        });
      };
      tryAccept();
      cy.clickThroughModal();
      cy.get('tr').contains('CRB / Disclosure of barring letter').parent().within(() => {
        cy.get('td').should('contain.text', 'Complete');
      });
    });

    it('should complete Hotel Menu', () => {
      Modules.completeHotelMenuTask.completeAdminSideHotelMenuTask();
      cy.navigateToManageTasks('hotel', fakerData.hotelUser, programmeName, programmeType);
      Modules.completeHotelMenuTask.completeHotelSideHotelMenuTask();
    });

    it('Completes block programme', () => {
      cy.clickButtonInRowWithText('Block Programme', 'Uploads');
      cy.clickLinkByText('Upload New');
      cy.get('#override-modal-tall').should('be.visible');
      cy.get('.modal-footer > .btn').click();
      cy.navigateToManageTasks('hotel', fakerData.hotelUser, programmeName, programmeType);
      cy.get('tr').contains('Block Programme').parent().within(() => {
        cy.get('.btn').contains('Accept').eq(-1).click();
      });
      cy.get('#flash_notice').should('contain', 'Successfully accepted Block Programme');
      cy.stopActingAsUser();
    });

    it('Should complete the RFQ room loading task', () => {
      Modules.completeRoomLoadingTask();
      cy.navigateToManageTasks('hotel', fakerData.hotelUser, programmeName, programmeType);
      cy.clickButtonInRowWithText('Confirm Room Loading', 'View + Approve');
      cy.get('.btn-danger').contains('Accept').click();
      cy.get('#flash_notice').should('contain', 'successfully completed the task');
      cy.stopActingAsUser();
    });

    it('Should complete the RFQ JI task', () => {
      cy.navigateToManageTasks('client', fakerData.clientUser, programmeName, programmeType);
      Modules.completeJoiningInstructionsTask();
    });

    it('Should complete the RFQ Confirmation form task', () => {
      cy.navigateToManageTasks('client', fakerData.clientUser, programmeName, programmeType);
      Modules.completeConfirmationFormTask();
    });

    if (!Cypress.env('use_stripe_payment')) {
      it('Should complete RFQ BU Credit account tasks', () => {
        cy.navigateToManageTasks('hotel', fakerData.hotelUser, programmeName, programmeType);
        Modules.completeBUCreditAccountTask();
      });
    }

    it('Should complete the RFQ training manual tasks', () => {
      ['hotel', 'client'].forEach((userType) => {
        const user = userType === 'client' ? fakerData.clientUser : fakerData.hotelUser;
        cy.navigateToManageTasks(userType, user, programmeName, programmeType);
        Modules.completeTrainingManualTask(
          userType,
          `${userType.charAt(0).toUpperCase() + userType.slice(1)} Training Manual`
        );
      });
    });

    it('Should complete the RFQ hotel safeguarding training task', () => {
      cy.get('a').contains('Prevent/Safeguarding Training as Complete').click();
      cy.clickThroughModal();
      cy.get('tr').contains('Prevent/Safeguarding Training').parent().within(() => {
        cy.get('td').should('contain.text', 'Complete');
      });
    });

    it('Should complete the RFQ manage learners task', () => {
      cy.navigateToManageTasks('client', fakerData.clientUser, programmeName, programmeType);
      Modules.completeManageLearnersTask(fakerData.clientUser, programmeName);
    });

    it('Should complete the RFQ Sign Off Welcome Letter task', () => {
      Modules.completeReleaseAndAcceptSignOffTask('Sign Off Welcome Letter', fakerData.hotelUser, fakerData.clientUser, programmeName, programmeType);
    });

    if(Cypress.env('use_stripe_payment')) {
      it('Should complete the BU Credit account and hotel stripe connect tasks', () => {
        [
          'Business Unit Credit as Complete',
          'Connect to Stripe as Complete'
        ].forEach(taskText => {
          cy.get('a').then($links => {
        const $filtered = $links.filter((_, el) => {
          return el.textContent.match(new RegExp(`Mark Task: \\d+ - ${taskText}`));
        });
        cy.wrap($filtered).click();
          });
          cy.clickThroughModal();
        });
      });
    }

    if(!Cypress.env('use_stripe_payment')) {
      it('Should complete the RFQ hotel credit application task', () => {
        Modules.completeCreditApplicationTask();
      });
    }

    it('Should complete the RFQ SLA tasks and release the RFQ', () => {
      Modules.completeSlasAndReleaseRfq();
    });
  });
});
