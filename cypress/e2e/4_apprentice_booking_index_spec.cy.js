import dayjs from 'dayjs';

// This spec file tests the new bookings index page
describe('new bookings index spec', () => {

  function loadFilterData(){
    expandFilters();
    cy.get('[data-cy="clientDropdown"]').should('be.visible');
    // Wait for the API call only if it hasn't been called before
    cy.wait('@getFilterData');
    cy.get('[data-cy="checkinStart"]').should('be.visible').clear();
    cy.get('[data-cy="checkinEnd"]').should('be.visible').clear();
  }

  function expandFilters() {
    //visibility of filters section
    cy.get('[data-cy="showFiltersBtn"]').should('contain', 'Show Filters').click();
    cy.get(':nth-child(2) > .text-h6').should('have.text', 'Filters');
    //getting the filters list section for visibility validation
    cy.get('.q-page > :nth-child(1) > :nth-child(1)').should('be.visible');
  }
  
  function hideFilters() {
    //hidden checks
    cy.get('[data-cy="showFiltersBtn"]').should('contain', 'Hide Filters').click();
    cy.get(':nth-child(2) > .text-h6').should('not.exist');
    //actionable assertion, filters are hidden - should not be focused
    cy.get('.q-page > :nth-child(1) > :nth-child(1)').should('not.be.focused');
    cy.get('[data-cy="showFiltersBtn"]').should('contain', 'Show Filters');
  }

  function clearDropdownFilters(dropdownCyData) {
    cy.get('[data-cy="' + dropdownCyData + '"]').parentsUntil('[class="q-field__inner relative-position col self-stretch"]').last().within(() => {
      cy.get('i').first().click();
    });
  }

  function filterBookings() {
    let initialBookingsCount = '';
    let updatedBookingsCount = '';

    //filter bookings - we can implicitly wait for the index to update by checking the bookings count before & after clicking the button
    cy.get('[data-cy="bookingsTotal"]').then(($num) => {
      const text = $num.text();
      //note - i tried calling getTotalBookings() here but cypress wasn't having it, an object returned as opposed to text.
      initialBookingsCount = extractNumberFromText(text);
      cy.get('[data-cy="bookingsTotal"]').should('contain', initialBookingsCount);

      cy.get('[data-cy="filterBookingsBtn"]').click();

      //unsure if a nested check is best here, can move it out if better practice
      cy.get('[data-cy="bookingsTotal"]').then(($num) => {
        const text = $num.text();
        updatedBookingsCount = extractNumberFromText(text);
        cy.get('[data-cy="bookingsTotal"]').should('contain', updatedBookingsCount);
      });
    });
  }

  //might be better as a cypress command in future if all dropdowns are handled the same way
  //approach isn't ideal - but cy.get().select() doesn't work, with the dropdown being detatched from the clickable element, that invokes it
  function selectFromDropdown(dropdownCyData, option) {
    cy.get('[data-cy="' + dropdownCyData + '"]').should('be.visible').click();
  
    // Select the option from the dropdown
    cy.get('[class="q-menu q-position-engine scroll"]').should('be.visible').and('not.be.empty').within(() => {
      cy.get('[role="option"]').contains(option).click();
    });
  }
  
  // Utility function to extract number from text
  function extractNumberFromText(text) {
    const match = text.match(/\d+/);
    return match ? parseInt(match[0], 10) : null;
  }

  function navigateToRandomPaginatedTab() {
    // Get the current active tab number
    cy.get(`[data-cy="pageTabsTop"]`).within(() => {
      cy.get('button[aria-current="true"]').invoke('text').then((currentTabNum) => {
        const currentTab = parseInt(currentTabNum, 10);
  
        //Generate a random number between 1-10, excluding the current active tab number
        let newTabNum;
        do {
          newTabNum = Math.floor(Math.random() * 10);
        } while (newTabNum === currentTab);
  
        //Click on the new random tab number
        cy.get('button').contains(newTabNum).click();
  
        //Check if the new tab is active
        cy.get('button[aria-current="true"]').should('contain', newTabNum);
      });
    });
  }

  //Helper function to clear a random dropdown from a list of dropdowns
  function clearRandomDropdown(dropdownCyDataList) {
    const randomIndex = Math.floor(Math.random() * dropdownCyDataList.length);
    const randomDropdown = dropdownCyDataList[randomIndex];
    cy.get('[data-cy="' + randomDropdown + '"]').parentsUntil('[class="q-field__inner relative-position col self-stretch"]').last().within(() => {
      cy.get('i').first().click();
    });
  }

  //Helper function to select a random option from a dropdown
  function selectRandomOptionFromDropdown(dropdownCyData) {
    cy.get('[data-cy="' + dropdownCyData + '"]').should('be.visible').click();

    // Select the option from the dropdown
    cy.get('[class="q-menu q-position-engine scroll"]').should('be.visible').and('not.be.empty').within(() => {
      cy.get('[role="option"]').then($options => {
        const randomIndex = Math.floor(Math.random() * $options.length);
        cy.wrap($options[randomIndex]).click();
      });
    });
  }

  function goToBookingDetails(bookingID) {
    cy.get('[data-cy="bookingsTable"]').within(() => {
      bookingID = cy.get('tr').get('[data-cy="bookingIDBtn"]').first().then(($btn) => {
        bookingID = $btn.text();
        cy.get('tr').get('[data-cy="bookingIDBtn"]').first().should('be.visible').click();
        //check if the url is correct
        cy.url().should('include', '/acc_bookings/' + bookingID);
      }); 
    });
  }

context('Bookings Index', () => {

context('When the user is an admin - Bookings Index', () => {

    beforeEach(() => {
    cy.login();

    //clients api
    cy.intercept('GET', '/apprentice/bookings/filter_data').as('getFilterData');
    cy.intercept('GET', '/apprentice/bookings').as('getBookings');

    cy.visit(Cypress.env('new_bookings_url'));
    
    //preloading filter data for the dropdowns & resetting state for tests
    loadFilterData();
  });

    context('sad path', () => {

      it('Should return an empty table with no bookings',() =>{
        cy.get('[data-cy="managerField"]').type('Mike Rowave');
        filterBookings();
        cy.get('[data-cy="bookingsTotal"]').should('contain', '0');
      });
    });

    context('happy path', () => {

    it('Expands and hides the bookings filters', () => {
      
      //doing in reverse as I want to ensure the filters are hidden before expanding them
      //same as expandFilters but in reverse
      hideFilters();

      //made this a function as i'm gonna be using it in each test (most likely)
      expandFilters();
    });

    it('Filters to a client and goes to their booking details page', () => {
      let client = 'Remit'
      let bookingID = '';

      //api call is made when the client dropdown is clicked
      selectFromDropdown('clientDropdown', client);
      cy.get('[data-cy="clientDropdown"]').should('have.text', client);
      filterBookings();

      goToBookingDetails(bookingID)  
    });

    it('Should return a table with bookings',() =>{
      cy.get('[data-cy="withoutSubsistence"]').click();
      cy.get('[data-cy="withoutSubsistence"]').should('have.attr', 'aria-checked').and('equal', 'true');

      selectFromDropdown('clientDropdown', 'Remit');
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').should('not.be.empty');
    })

    it('Should select a quick filter and return bookings', () => {
      cy.get('[data-cy="apprenticeHotelNotConfirmedQF"]').click();

      filterBookings();
    });

    it('Should navigate to booking details via the group booking id ref btn', () => {
      let bookingID = '';
      let groupBookingID = '';

      selectFromDropdown('clientDropdown', 'Remit');
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').within(() => {

        bookingID = cy.get('tr').get('[data-cy="bookingIDBtn"]').first().then(($btn) => {
          bookingID = $btn.text();
        }); 

        groupBookingID = cy.get('tr').get('[data-cy="bookingRefBtn"]').first().then(($btn) => {
          groupBookingID = $btn.text();
          cy.get('tr').get('[data-cy="bookingRefBtn"]').first().should('be.visible').click();
          //check if the url is correct
          cy.url().should('include', '/acc_group_bookings/' + groupBookingID);
          
        });  
      });  
      cy.get('[data-cy="view_booking_summary"]').first().should('be.visible').click();
      cy.get('[data-cy="view_booking_details"').should('be.visible').click();
      cy.url().should('include', '/acc_bookings/' + bookingID);
    });

    it('Navigates to a random page tab', () => {
      filterBookings();
      navigateToRandomPaginatedTab();
    });

    it('Opens the BU modal on a given booking', () => {

      selectFromDropdown('clientDropdown', 'Remit');
      filterBookings();

      cy.contains('span', 'Sorry, no bookings found').should('not.exist');

      cy.get('[data-cy="bookingsTable"]').within(() => {
        cy.get('tr').get('[data-cy="buModalBtn"]').first().should('be.visible').click();
      }); 

      cy.get('[data-cy="buPopupModal"]').should('be.visible').within(() => {
        cy.get('[class="text-h6"]').should('have.text', 'Business Unit Details');
        cy.get('button').contains('Close').click();
      });
      cy.get('[data-cy="buPopupModal"]').should('not.exist');
    });

    it('Filters by booking type: EPA', () => {
      bookingID = '';
      selectFromDropdown('epaBookingDropdown', 'EPA');
      filterBookings();
      goToBookingDetails(bookingID);
      //check if the url is correct
      cy.url().should('include', '/acc_bookings/' + bookingID);
      cy.get('h1').should('contain', 'EPA');
    });


    it('Filters by booking type: EPA subsistence only', () => {
      bookingID = '';
      selectFromDropdown('epaBookingDropdown', 'EPA');
      cy.get('[data-cy="onlySubsistence"]').click();
      filterBookings();
      goToBookingDetails(bookingID);
      //check if the url is correct
      cy.url().should('include', '/acc_bookings/' + bookingID);
      cy.get('h1').should('contain', 'EPA');
      //room type row should contain 'Subsistence only'
      cy.get(':nth-child(9) > td').should('contain', 'Subsistence Only');
    });

    it('Filters by booking type: Subsistence only', () => {
      bookingID = '';
      cy.get('[data-cy="onlySubsistence"]').click();
      filterBookings();
      goToBookingDetails(bookingID);
      //check if the url is correct
      cy.url().should('include', '/acc_bookings/' + bookingID);
      //room type row should contain 'Subsistence only'
      cy.get(':nth-child(6) > td').first().should('contain', 'Subsistence Only');
    });

    it('Filters by non-stripe payment', () => {
      bookingID = '';

      selectFromDropdown('paymentMethodDropdown', 'Non Stripe');
      selectFromDropdown('rfqTypeDropdown','Residential')
      filterBookings();
      goToBookingDetails(bookingID);
    });

    it('Filters by only hotel confirmed bookings', () => {
      let hotelConfirmedText = '';
      bookingID = '';
      //this regex will strip out the date/time from the bookings index hotel confirmation text, for comparison on booking details
      let hotelConfirmedRegex = /([A-Za-z0-9]+(-[A-Za-z0-9]+)+)/; 
    
      // Select the dropdown option and filter bookings
      selectFromDropdown('hotelConfirmedDropdown', 'Confirmed');
      filterBookings();
    
      // Store the hotel confirmed text and booking ID
      cy.get('[data-cy="bookingsTable"]').within(() => {
        cy.get('tr>td').eq(14).within(() => {
          cy.get('span').invoke('text').then((text) => {
            hotelConfirmedText = text;
          });
        });
    
        cy.get('tr').get('[data-cy="bookingIDBtn"]').first().invoke('text').then((text) => {
          bookingID = text;
          cy.get('tr').get('[data-cy="bookingIDBtn"]').first().should('be.visible').click();
    
          // Use the stored booking ID to verify the URL
          cy.url().should('include', '/acc_bookings/' + bookingID);
        });
      });
    
      //due to variable scope issues I've opted for a regex check
      cy.then(() => {
        cy.get('#confirmation-details > .well > .table > tbody > :nth-child(2) > td').invoke('text').should((text) => {
          expect(text, hotelConfirmedText).to.match(hotelConfirmedRegex);
        });
      });
    });


    it('Filters for bookings by surname', () => {
      let surname = 'Smith';
      bookingID = '';

      cy.get('[data-cy="surnameField"]').should('be.visible').type(surname);
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').within(() => {
        
        cy.get('tr').get('[data-cy="bookingIDBtn"]').first().then(($btn) => {
          bookingID = $btn.text();
          cy.get('tr').get('[data-cy="bookingIDBtn"]').first().should('be.visible').click();
          //check if the url is correct
          cy.url().should('include', '/acc_bookings/' + bookingID);
        });  
      });  
    });
    
    it('Clears (clearable) filters', () => {

      // List of dropdowns to clear
      let dropdowns = ['clientDropdown', 'roomTypeDropdown', 'paymentMethodDropdown', 'epaBookingDropdown'];
      
      dropdowns.forEach(dropdown => {
        selectRandomOptionFromDropdown(dropdown);
      });
    
      // Clear a random dropdown
      clearRandomDropdown(dropdowns);
    });

    it('Filters by individual booking IDs', () => {
      bookingID = '1';

      cy.get('[data-cy="bookingID"]').should('be.visible').type(bookingID);
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').within(() => {
        cy.get('tr').get('[data-cy="bookingIDBtn"]').first().then(($btn) => {
          bookingID = $btn.text();
          cy.get('tr').get('[data-cy="bookingIDBtn"]').first().should('be.visible').click();
          //check if the url is correct
          cy.url().should('include', '/acc_bookings/' + bookingID);
        });  
      });
    });
    
    // it('Verifies the export button & files work as expected', () => {
    //   let backdropID = '';
    
    //   cy.intercept('GET', '/apprentice/bookings/export*').as('exportBookings');

    //   selectFromDropdown('clientDropdown', 'Remit');
    //   selectFromDropdown('roomTypeDropdown', 'Single');
    //   selectFromDropdown('paymentMethodDropdown', 'Stripe BACS');
    //   filterBookings();

    //   cy.get('[data-cy="exportToExcelBtn"]').should('be.visible').click();
    
    //   cy.wait('@exportBookings').then((interception) => {
    //     //Extract the backdropID from the response body
    //     backdropID = interception.response.body.backdropID;
    //     cy.log('Backdrop ID:', backdropID);

    //     //Start the polling process
    //     waitForProgressToComplete(backdropID);
    //   });
    // });

    it('Should filter by hotel', () => {
      let hotel = 'Delta by Marriott MK Hotel';
      bookingID = '';

      selectFromDropdown('hotelDropdown', hotel);
      filterBookings();

      goToBookingDetails(bookingID);
      
    });

    it('Filters by manager', () => {
      let manager = 'Ben Vaughan';

      cy.get('[data-cy="managerField"]').should('be.visible').type(manager);
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').within(() => {
        cy.get('tr').get(':nth-child(1) > :nth-child(5)').should('be.visible').and('contain', manager);
      });
    });

    it('Filters by programme JIs', () => {

      selectFromDropdown('clientDropdown', 'Remit');
      selectFromDropdown('programmeDropdown', 'Remit 2024/2025');
      cy.get('[data-cy="jiDropdown"]').should('not.be.empty');
      selectFromDropdown('jiDropdown', 'Tuesday to Friday Block Training')
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').should('not.be.empty');
      });

    it('Filters by tags', () => {
      let tag = 'LOPEZ';

      selectFromDropdown('clientDropdown', 'Remit');
      selectFromDropdown('tagsDropdown', tag);
      filterBookings();

      cy.get('[data-cy="bookingsTable"]').should('not.be.empty');

    });

    //Some strangeness in cypress with this test's status - it's passing but the test is marked as ongoing
    // it.only('Filters by virtual bookings', () => {
    //   bookingID = '';
    //   selectFromDropdown('rfqTypeDropdown', 'Virtual');
    //   filterBookings();

    //   cy.get('[data-cy="bookingsTable"]').within(() => {
    //     bookingID = cy.get('tr').get('[data-cy="bookingIDBtn"]').first().then(($btn) => {
    //     bookingID = $btn.text();
    //     cy.get('tr').get('[data-cy="bookingIDBtn"]').first().should('be.visible').click();
    //     //check if the url is correct - virtual
    //     cy.url().should('include', '/bookings_virtual_multi_module/#/bookingdetails/' + bookingID);
    //   });
    // });

    context('Counts/Database tests', () => {
    //do pagination test above and below max tabs
    let byGroupID = [];
    const bookingHeaderID = 73483;

    it("Database test", () => {
      cy.task("DBSERVICE", {
        dbConfig: Cypress.env("DB"),
        sql: "SELECT * FROM acc_bookings WHERE acc_booking_header_id = " + bookingHeaderID
      }).then((result) => {
        expect(result.rows.length).to.equal(77);
        byGroupID = result.rows;
      });
    });

    it("Should return the correct number of bookings", () => {
      cy.get('[data-cy="groupBookingID"]').should('be.visible').type('73483');
      filterBookings();
      cy.get('[data-cy="bookingsTotal"]').should('contain', '77');
    });
  });

    //parking this for a while - need to figure out how to get the bookings data as the below times out
    /* it.only('Sets the client filter', () => {

      expandFilters();
      filterBookings();

      //listen for returned bookings for filter function
      cy.intercept({resourceType: 'XHR', path: '/apprentice/bookings?page=1'}).as('getBookings')
      
      cy.wait('@getBookings').then((interception) => {
        cy.log(JSON.stringify(interception.response.body));
        expect(interception.response.statusCode).to.eq(200);
        //pagination is set to 20 per page so I threw in a check to ensure we never get more than 20 bookings at a time
        //this is handily stored as 'items' in the response body
        expect(interception.response.body).to.be.an('object');
        expect(interception.response.body).to.have.property('pagy');
        expect(interception.response.body.pagy).to.have.property('items').that.equals(20);
      });
    }); */
  });

  });

  context('When the user is a client', () => {

    beforeEach(() => {
      cy.login();
      cy.url().should('include', '/admin/dashboard');
      cy.actAs('Jonny Connelly', 'Client');
      
      //clients api
      cy.intercept('GET', '/apprentice/bookings/filter_data').as('getFilterData');
      cy.intercept('GET', '/apprentice/bookings').as('getBookings');

      cy.visit(Cypress.env('new_bookings_url'));
      expandFilters();
    });

    it('Should not show the client filter', () => {
      cy.get('[data-cy="clientDropdown"]').should('not.be.visible');
    });

    it('Goes to a clients booking filtered by hotel', () => {
      let hotel = 'Delta by Marriott MK Hotel';
      bookingID = '';

      selectFromDropdown('hotelDropdown', hotel);
      filterBookings();

      goToBookingDetails(bookingID);
      cy.url().should('include', '/acc_bookings/' + bookingID);
    });
  });
});
});