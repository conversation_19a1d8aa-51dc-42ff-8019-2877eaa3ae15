const generateContactDetails = require('../helpers/generate_contact_details');

// Function to fill in address fields
/**
 * Fills in address fields on a form based on the provided organisation type.
 *
 * - Selects "United Kingdom" as the country.
 * - Sets address fields with default values.
 * - Uses a different postcode if the organisation type is "hotel".
 * - Dynamically fills in phone and email fields based on the orgType.
 *
 * @param {string} orgType - The type of organisation (e.g., "hotel", "restaurant").
 *   Used to determine which fields to fill and which postcode to use.
 *
 * @example
 * fillInAddressFields('hotel');
 */
function fillInAddressFields(orgType) {
    const contactDetails = generateContactDetails();
    const isHotel = orgType.toLowerCase() === 'hotel';
    const postcode = isHotel ? 'ML14XL' : 'ML14XY';
    const addressFields = {
        '#primary_location_postcode': postcode,
        '#primary_location_address_1': 'Little Cottage',
        '#primary_location_address_2': '17 High Street',
        '#primary_location_city': 'Crafty Valley',
        '#primary_location_county': 'BIG CITY',
        [`#${orgType.toLowerCase()}_primary_location_attributes_phone`]: contactDetails.contactPhone,
        [`#${orgType.toLowerCase()}_primary_location_attributes_email`]: contactDetails.contactEmail
    };

    cy.get('#primary_location_country_id').select('United Kingdom', { force: true });
    // Fill in the address fields
    for (const [selector, value] of Object.entries(addressFields)) {
        cy.get(selector).type(value);
    }
}




// Export the functions as a module
module.exports = {
    fillInAddressFields,
};