import { faker } from '@faker-js/faker';

/**
 * Returns either a hardcoded value or a generated value based on the Cypress environment variable 'USE_HARDCODED_DATA'.
 * This is used to control whether to use static test data or dynamically generated data in Cypress tests, for the overall run.
 * This is generated ONCE and used throughout the test suite, per run.
 *
 * If 'USE_HARDCODED_DATA' is set to a truthy value in Cypress.env, the function returns the provided hardcoded value.
 * Otherwise, it returns the generated value. This is useful for toggling between static and dynamic test data.
 *
 * @param {*} hardcodedValue - The value to use when hardcoded data is preferred (e.g., for consistent test runs).
 * @param {*} generatedValue - The value to use when dynamic, generated data is preferred (e.g., for randomized tests).
 * @returns {*} The selected value based on the environment configuration.
 */
function getValue(hardcodedValue, generatedValue) {
  const useHardcodedData = Cypress.env('USE_HARDCODED_DATA'); // Access the env variable
  return useHardcodedData ? hardcodedValue : generatedValue;
}

// Other generated data
const organisationName = getValue('Debug Organisation', faker.company.companyName());
const apprenticeProgrammeName = getValue('Debug Apprentice Programme',`${faker.lorem.words(2)} - Apprentice`);
const adultProgrammeName = getValue('Debug Adult Programme', `${faker.lorem.words(2)} - Adult`);
const virtualProgrammeName = getValue('Debug Virtual Programme', `${faker.lorem.words(2)} - Virtual`);
const clientUser = getValue('Debug Client', `${faker.name.firstName()} ${faker.name.lastName()}`);
const buName = getValue('Debug BU', faker.company.companyName());
const hotelUser = getValue('Hotel User', `${faker.name.firstName()} ${faker.name.lastName()}`);
const hotelName = getValue('Debug Hotel', faker.company.companyName());

module.exports = {
  organisationName,
  apprenticeProgrammeName,
  adultProgrammeName,
  clientUser,
  buName,
  hotelUser,
  hotelName
};