// Correct import for faker
import { faker } from '@faker-js/faker';

// Generate contact details
/**
 * Generates a random set of contact details using the faker library.
 *
 * @returns {Object} An object containing randomly generated contact information:
 *   - contactTitle {string}: A title such as 'Mr.', 'Ms.', etc.
 *   - contactFirstName {string}: A random first name.
 *   - contactLastName {string}: A random last name.
 *   - contactPhone {string}: A 10-digit phone number (digits only).
 *   - contactEmail {string}: A randomly generated email address.
 *   - contactPostcode {string}: A postcode in the format 'AA1 1AA'.
 *   - hotelName {string}: A randomly generated company name, used here as a hotel name.
 *
 * @example
 * const details = generateContactDetails();
 * // details = {
 * //   contactTitle: 'Dr.',
 * //   contactFirstName: 'Jane',
 * //   contactLastName: 'Doe',
 * //   contactPhone: '1234567890',
 * //   contactEmail: '<EMAIL>',
 * //   contactPostcode: 'AB1 2CD',
 * //   hotelName: 'Acme Hotels Ltd'
 * // }
 */
function generateContactDetails() {
    return {
        contactTitle: faker.name.prefix(),
        contactFirstName: faker.name.firstName(),
        contactLastName: faker.name.lastName(),
        contactPhone: faker.phone.phoneNumber('##########'),
        contactEmail: faker.internet.email(),
        contactPostcode: faker.address.zipCode('??# #??'),
        hotelName: faker.company.companyName()
    };
}

module.exports = generateContactDetails;