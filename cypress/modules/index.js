module.exports = {
    //critical data structure setup
    createHotel: require('./data_structure_setup/create_hotel'),
    createLearners: require('./data_structure_setup/create_learners'),
    createOrganisation: require('./data_structure_setup/create_organisation'),
    createProgramme: require('./data_structure_setup/create_programme'),
    createUser: require('./data_structure_setup/create_user'),
    createBU: require('./data_structure_setup/create_bu'),

    //hotel specific bits
    createHotelQuotation: require('./hotel/create_hotel_quotation'),
    navToHotel: require('./hotel/nav_to_hotel.js'),
    completeHotelCreation: require('./hotel/complete_hotel_creation.js'),
    buildHotel: require('./hotel/build_hotel.js'),
    setHotelUser: require('./hotel/set_hotel_user.js'),

    //helpers
    generateContactDetails: require('./helpers/generate_contact_details'),
    generateFakerData: require('./helpers/generate_faker_data'),
    hardcodedContactDetails: require('./helpers/hardcoded_contact_details'),
    fillInAddressFields: require('./helpers/fill_in_address_fields'),

    //rfq setup
    rfqLocationDetails: require('./rfq_setup/rfq_location_details'),
    createRFQ: require('./rfq_setup/create_rfq'),
    sendProposalToHotel: require('./rfq_setup/send_rfq_proposal'),
    completeRFQResponse: require('./rfq_setup/complete_rfq_response'),
    finishProposalAsAdmin: require('./rfq_setup/finish_rfq_proposal'),
    acceptProposalAsClient: require('./rfq_setup/client_accept_rfq_proposal'),
    addCreditAccount: require('./rfq_setup/add_credit_account'),
    addAdminFees: require('./rfq_setup/add_admin_fees'),

    //rfq flow
    completeAdditionalInfoTask: require('./rfq_flow/complete_additional_info_tasks'),
    // completeBlockProgrammeTask: require('./rfq_flow/complete_block_programme_task'),   //unused ATM
    completeHotelMenuTask: require('./rfq_flow/complete_hotel_menu_task'),
    completeJoiningInstructionsTask: require('./rfq_flow/complete_JI_task'),
    completeConfirmationFormTask: require('./rfq_flow/complete_confirmation_form_task'),
    completeBUCreditAccountTask: require('./rfq_flow/complete_bu_credit_acct_task'),
    completeReleaseAndAcceptSignOffTask: require('./rfq_flow/complete_release_signoff_task'),
    completeTrainingManualTask: require('./rfq_flow/complete_training_task'),
    completeManageLearnersTask: require('./rfq_flow/complete_manage_learners_task'),
    completeRoomLoadingTask : require('./rfq_flow/complete_room_loading_task'),
    completeCreditApplicationTask: require('./rfq_flow/complete_credit_application_task'),
    completeSlasAndReleaseRfq: require('./rfq_flow/complete_slas_and_release_task'),
    completeStripePaymentTask: require('./rfq_flow/complete_stripe_payment_task'),

    //bookings
    createBooking: require('./bookings/create_booking'),
    confirmBooking: require('./bookings/confirm_booking'),
    createBookingViaLegacyFlow: require('./bookings/legacy_create_booking'),

    //non/early arrivals
    nonEarlyArrivals: require('./non_and_early_arrivals/non_early_arrivals'),
    //stripe payments
    payForBooking: require('./stripe_payments/pay_for_booking'),

    //adult bookings
    fillInFormDetails: require('./adult_bookings/fill_in_form_details'),

}
