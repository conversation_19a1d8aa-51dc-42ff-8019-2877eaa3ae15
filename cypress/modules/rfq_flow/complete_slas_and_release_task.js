function completeSlasAndReleaseRfq() {
  const tasks = ['Hotel and Client SLA', 'HG and Hotel SLA'];

  tasks.forEach(task => {
    cy.clickLinkByText(`${task} as Complete`);
    cy.clickThroughModal();

    const trimmedTask = task.replace(/^\d+\s*-\s*/, '');
    cy.get('tr').contains(trimmedTask).parent().within(() => {
      cy.get('td').should('contain.text', 'Complete');
    });
  });

  cy.get('.btn').contains('Release').click();
  cy.clickThroughModal();

  cy.get('body').then($body => {
    if ($body.find('#flash_error').length > 0) {
      cy.get('#flash_error').then($el => {
        const errorText = $el.text();
        cy.log('Flash Error:', errorText);
        if (errorText && errorText.trim().length > 0) {
          throw new Error(`Flash Error: ${errorText}`);
        }
      });
    }
  });
}

module.exports = completeSlasAndReleaseRfq;