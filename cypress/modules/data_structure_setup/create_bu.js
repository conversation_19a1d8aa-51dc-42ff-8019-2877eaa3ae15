import { faker } from '@faker-js/faker';

/**
 * Creates a new Business Unit (BU) within a specified programme using Cypress commands.
 *
 * Navigates through the UI to the "Business Units" section of the given programme,
 * opens the modal to add a new BU, fills in the required fields with generated data,
 * and submits the form. Verifies that the new BU appears in the data table.
 *
 * @param {string} buName - The name to assign to the new Business Unit.
 * @param {string} programmeName - The name of the programme under which to create the BU.
 *
 * @remarks
 * - Generates a random 4-character alphanumeric BU code for uniqueness.
 * - Uses a UK-style postcode pattern for the BU's address.
 * - Assumes custom Cypress commands `cy.clickLinkByText` are available.
 * - Relies on specific data-cy attributes for element selection.
 */
function createBU(buName, programmeName) {
    const buId = faker.random.alphaNumeric(4);
  
    cy.get('a[href*="/organisations/"]').contains('Programmes').click();
    cy.clickLinkByText(programmeName);
    cy.clickLinkByText('Business Units');
  
    cy.get('[data-cy="openModalButton"]').click();
    cy.get('[data-cy="addBu"]').should('be.visible');
    cy.get('[data-cy="businessUnitNameInput"]').type(buName);
    cy.get('[data-cy="businessUnitCodeInput"]').type(buId);
    cy.get('[data-cy="postcodeInput"]').type(faker.address.zipCode('??# #??'));
    cy.get('[data-cy="submitButton"]').click();
  
    cy.get('.n-data-table [data-col-key="name"]').should('contain', buName);
  }

module.exports = createBU;