// Function to create a programme
/**
 * Creates a new programme via the UI and adds a contact person to it.
 *
 * Navigates to the "Programmes" section, opens the "Add Programmes" modal,
 * fills in the required fields, submits the form, and verifies the programme
 * was created. Finally, associates a contact person with the new programme.
 *
 * @param {string} programmeName - The name of the programme to create.
 * @param {'adult'|'apprentice'} programmeType - The type of programme; determines the selection in the dropdown.
 * @param {Object} contact - The contact person to add to the programme.
 */
function createProgramme(programmeName, programmeType, contact) {
    cy.get('a[href*="/organisations/"]').contains('Programmes').click();
    cy.clickLinkByText('Add Programmes');
    cy.get('.modal-body').should('be.visible');
  
    cy.get('#rfq_programme_name').type(programmeName, { force: true });
    cy.get('#rfq_programme_rfq_type').select(programmeType === 'adult' ? 'Adult' : 'Apprentice', { force: true });
    cy.get('.checkbox').click();
    cy.get('#rfq_programme_zero_chg_sub').check();
    cy.get('#new_rfq_programme > .btn').click({ force: true });
  
    cy.get('.modal-body').should('not.be.visible');
    cy.get('.table_hotel a').contains(programmeName).should('be.visible').click();
    cy.get('h2').should('contain', programmeName);
    cy.url().should('include', `/rfq_programmes/`);
  
    // Add person to the programme
    addPersonToProgramme(contact);
}

// Helper function to add a person to the programme
function addPersonToProgramme(contact) {
    cy.clickLinkByText('Add Person');
    cy.get('[role="dialog"]').should('be.visible');
    cy.get('#rfq_role_contact_id').select(contact);
    cy.get('#rfq_role_acts_as').select('manager');
    cy.get('#rfq_role_feedback').check();
    cy.get('#rfq_role_reminders').check();
    cy.get('#rfq_role_dashboard_access').check();
    cy.get('#new_rfq_role > .btn').click();
    cy.get(':nth-child(8) > .btn-danger').click();
}

module.exports = createProgramme;