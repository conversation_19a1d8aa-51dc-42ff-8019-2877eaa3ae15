// Correct import for faker
import { faker } from '@faker-js/faker';
const { fillInAddressFields } = require('../helpers/fill_in_address_fields');

// Function to create a hotel
/**
 * Creates a new hotel with the specified name, fills in all required fields,
 * sets up facilities, and adds a conference suite using Cypress commands.
 * 
 * This function:
 * - Navigates to the Hotels section and initiates hotel creation.
 * - Fills in hotel details with generated data (including long description, web address, and random numbers).
 * - Selects specific hotel attributes (currency, style, star rating, etc.).
 * - Fills in room type counts and company registration details.
 * - Saves the hotel and verifies creation.
 * - Navigates to Facilities, checks several facility options, and updates the hotel.
 * - Navigates to Conference Suites, adds a new suite with randomized capacities, and verifies creation.
 * 
 * @param {string} hotelName - The name to assign to the new hotel.
 * 
 * @remarks
 * - Uses the global `faker` object for generating random data.
 * - Assumes existence of custom Cypress commands like `cy.clickLinkByText` and `fillInAddressFields`.
 * - Facilities are checked using a generated selector list for 31 possible facilities.
 * - Suite details are filled using a mapping of selectors to values, with random capacities.
 */
function createHotel(hotelName) {
    const longString = faker.random.alphaNumeric(205);
    const webAddress = faker.internet.url();
  
    cy.clickLinkByText('Hotels');
    cy.contains('.btn', 'Add New Hotel').click();
    
    cy.get('#hotel_for_conferences').check();
    cy.get('#hotel_name').type(hotelName);
    cy.get('#hotel_primary_location_attributes_website').type(webAddress);
    fillInAddressFields('hotel');
    cy.get('#hotel_currency_id').select('1');
    cy.get('#hotel_hotel_style').select('Golf and Spa Hotel');
    cy.get('#hotel_star_rating').select('5');
    cy.get('#hotel_rated_by').select('AA');
    cy.get('#hotel_description').type(longString);
    cy.get('#hotel_nearest_train_station').type(faker.address.cityName());
    cy.get('#hotel_distance_from_train_station').clear().type(faker.datatype.number({ min: 1, max: 10 }).toString());
    cy.get('#hotel_nearest_airport').type(`${faker.address.cityName()} Airport`);
    cy.get('#hotel_distance_from_airport').type(faker.datatype.number({ min: 10, max: 50 }).toString());
  
    const roomTypes = [
      'hotel_no_of_twins',
      'hotel_no_of_doubles',
      'hotel_family_rooms',
      'hotel_disabled_rooms'
    ];
  
    roomTypes.forEach(room => {
      cy.get(`#${room}`).type('100');
    });
  
    cy.get('#hotel_company_reg_number').type(faker.random.alphaNumeric(8));
    cy.get('#hotel_company_vat_number').type(faker.random.alphaNumeric(6));
    cy.get('#hotel_company_reg_address').type(faker.address.streetAddress());
  
    cy.get('#save-hotel').click();
  
    cy.get('#flash_notice').contains(`Hotel '${hotelName}' created`);
    cy.url().should('include', '/hotels');
  
  
    cy.clickLinkByText('Facilities');
    cy.url().should('include', '/facilities');
    cy.get('#hotel_parking_complimentary').check();
    cy.get('#hotel_all_wifi_complimentary').check();
    cy.get('#hotel_parking_on_site').check();
  
    const facilities = Array.from({ length: 31 }, (_, i) => `:nth-child(3) > .radio > #hotel_facility_${i + 1}`);
  
    cy.get('#hotel_number_of_single_rooms').clear();
    cy.get('#hotel_number_of_single_rooms').type('100');
  
    facilities.forEach(facility => {
      cy.get(facility).check();
    });
  
    cy.contains('Update Hotel').click();
    cy.get('#flash_notice').contains('Hotel updated');
  
    cy.clickLinkByText('Conference Suites');
    cy.url().should('include', '/rooms');
    cy.contains('Add new conference suite').click();
  
    const suiteDetails = {
      '#room_name': 'Test Suite',
      '#room_wifi': true,
      '#room_theatre_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_classroom_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_boardroom_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_ushape_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_cabaret_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_lunch_dinner_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_dinner_dance_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_reception_capacity': faker.datatype.number({ min: 100, max: 200 }).toString(),
      '#room_length': '15',
      '#room_width': '10',
      '#room_area': '150',
      '#room_height_max': '6',
      '#room_door_height': '10',
      '#room_door_width': '10'
    };
  
    Object.entries(suiteDetails).forEach(([selector, value]) => {
      if (typeof value === 'boolean') {
        cy.get(selector).check();
      } else {
        cy.get(selector).type(value);
      }
    });
  
    cy.get(':nth-child(2) > input.btn').click();
    cy.get('#flash_notice').contains('Room created');
}

// Export the function
module.exports = createHotel;