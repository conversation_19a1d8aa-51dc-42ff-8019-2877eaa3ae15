const { fillInAddressFields } = require('../helpers/fill_in_address_fields');
const generateContactDetails = require('../helpers/generate_contact_details');

// Function to create an organisation
/**
 * Creates one or more organisations in the application
 *
 * Navigates to the "Organisations" section, fills out the organisation creation form
 * with generated contact details, submits the form, and verifies successful creation.
 * After creation, navigates to the organisation's row and clicks "Edit".
 *
 * @param {number} [organisationsToCreate=1] - The number of organisations to create. Defaults to 1 if not specified.
 * @param {string} organisationName - The name to use for the new organisation(s).
 *
 * @example
 * createOrganisation(2, 'Test Organisation');
 *
 * @remarks
 * - Uses helper functions `generateContactDetails`, `fillInAddressFields`, and Cypress custom commands like `cy.clickLinkByText`.
 * - Assumes the presence of specific form fields and selectors in the UI.
 * - After creation, the function navigates to the edit page of the newly created organisation.
 */
function createOrganisation(organisationsToCreate, organisationName) {
    // Default to 1 if not provided
    organisationsToCreate = organisationsToCreate || 1; 

    const contactDetails = generateContactDetails();
    const fillOrganisationDetails = () => {
        cy.get('#organisation_name').type(organisationName);
        fillInAddressFields('organisation', contactDetails);
        cy.get('#organisation_primary_contact_attributes_first_name').type(contactDetails.contactFirstName);
        cy.get('#organisation_primary_contact_attributes_surname').type(contactDetails.contactLastName);
        cy.get('#organisation_primary_contact_attributes_telephone').type(contactDetails.contactPhone);
        cy.get('#organisation_primary_contact_attributes_email').type(contactDetails.contactEmail);
        cy.get('.form-actions > .btn').click();
        cy.get('#organisation_apprentice_contract_level').select('FULLY');
        cy.get('.form-actions > .btn').click();
        cy.get('#flash_notice').should('contain.text', 'Organisation was successfully created.');
    };

    for (let i = 0; i < organisationsToCreate; i++) {
        cy.clickLinkByText('Organisations');
        cy.clickLinkByText('Add New Organisation');
        fillOrganisationDetails();
        cy.contains('a', 'Organisations').click();
        cy.contains('tr', organisationName).within(() => {
            cy.get('a[href]').contains('Edit').click();
        });
    }
}

// Export the functions as a module
// accidentally exported as an object before, oops
module.exports = createOrganisation;