// Correct import for faker
import { faker } from '@faker-js/faker';

// Import generateContactDetails
const generateContactDetails = require('../helpers/generate_contact_details');

/**
 * Creates a specified number of learners in the application via Cypress UI automation.
 * 
 * Each learner is generated with randomised contact details, gender, and date of birth.
 * The function ensures that at least one learner is marked as an adult (age >= 18).
 * All learners are assigned to the same randomly generated group code.
 * 
 * Steps performed for each learner:
 *  - Navigates to the Learners section.
 *  - Opens the "Upload Individual Learner" form.
 *  - Fills in learner and manager details with generated data.
 *  - Assigns a group code to each learner.
 *  - Checks the "adult" box for the first learner who is 18 or older.
 *  - Submits the form.
 * 
 * @param {number} numberOfLearners - The number of learners to create.
 * 
 * @requires faker - For generating random data.
 * @requires generateContactDetails - Function to generate contact details for each learner.
 * @requires Cypress (cy) - Cypress commands for UI automation.
 */
function createLearners(numberOfLearners) {
    const groupCode = faker.random.alphaNumeric(6);

    // Generate learners with contact details
    const learners = Array.from({ length: numberOfLearners }, () => {
        const contactDetails = generateContactDetails(); // Generate contact details for each learner
        const gender = Math.random() < 0.5 ? 'M' : 'F';
        const pastDate = faker.date.past(20, new Date(2009, 0, 1));
        const formattedDate = `${pastDate.getDate()}-${pastDate.toLocaleString('default', { month: 'short' })}-${pastDate.getFullYear()}`;
        const age = new Date().getFullYear() - pastDate.getFullYear();

        return {
            contactDetails,
            gender,
            formattedDate,
            age,
        };
    });

    let adultCreated = false;

    learners.forEach(({ contactDetails, gender, formattedDate, age }) => {
        cy.clickLinkByText('Learners');
        cy.url().should('include', `/acc_learners`);
        cy.contains('.btn', 'Upload Individual Learner').click();

        cy.get('#rfq_learner_forename').type(contactDetails.contactFirstName);
        cy.get('#rfq_learner_surname').type(contactDetails.contactLastName);
        cy.get('#rfq_learner_email').type(faker.internet.email());
        cy.get('#rfq_learner_telephone').type(contactDetails.contactPhone);
        cy.get('#rfq_learner_gender').select(gender);
        cy.get('#rfq_learner_date_of_birth').type(formattedDate).type('{enter}', { force: true });
        cy.get('#rfq_learner_manager').type(faker.name.findName());
        cy.get('#rfq_learner_manager_email').type(faker.internet.email());
        cy.get('#rfq_learner_manager_telephone').type(faker.phone.phoneNumber('##########'));
        cy.get('#rfq_learner_group_number').type(groupCode);

        if (age >= 18 && !adultCreated) {
            cy.get('#rfq_learner_adult').check();
            adultCreated = true;
        }

        cy.get('.btn').contains('Save').click();
    });
}

module.exports = createLearners;