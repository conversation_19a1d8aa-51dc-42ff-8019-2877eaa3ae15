import { faker } from '@faker-js/faker';

/**
 * Creates one or more users by filling out and submitting the contact creation form in the UI.
 * 
 * This function navigates through the application's UI to add new contacts and, if the user type is 'client',
 * promotes the contact to a user with specific roles and permissions. It uses random data for some fields
 * and validates the creation process, throwing errors if creation fails.
 * 
 * @param {string} userType - The type of user to create (e.g., 'client'). Determines additional steps in the process.
 * @param {number} [usersToCreate=1] - The number of users to create. Defaults to 1 if not specified.
 * @param {string} user - The full name of the user (e.g., "<PERSON>"). Must be a non-empty string.
 * @throws Will throw an error if the user parameter is not defined or empty.
 * @throws Will throw an error if contact creation fails, including the failure message from the UI.
 * 
 * @example
 * // Create a single client user named "<PERSON>"
 * createUser('client', 1, '<PERSON>');
 * 
 * @example
 * // Create three staff users named "<PERSON>"
 * createUser('staff', 3, '<PERSON>');
 */
function createUser(userType, usersToCreate, user) {
    if (!user) {
        throw new Error(`${userType} user is not defined or empty`);
    }

    // fallback to default user amt of 1, if not provided
    usersToCreate = usersToCreate || 1;

    const [firstName, lastName] = user.split(' ');
    const randomDay = Math.floor(Math.random() * 28) + 1;
    const randomMonth = Math.floor(Math.random() * 12) + 1;

    for (let i = 0; i < usersToCreate; i++) {
        cy.clickLinkByText('Contacts');
        cy.clickLinkByText('Add New Contact');
        cy.url().should('include', '/contacts/new');

        const contactData = {
            title: faker.name.prefix(),
            email: faker.internet.email(),
            phone: faker.phone.phoneNumber(),
            firstName: firstName,
            lastName: lastName
        };

        cy.get('#contact_title').type(contactData.title);
        cy.get('#contact_first_name').type(firstName);
        cy.get('#contact_surname').type(lastName);
        cy.get('#contact_email').type(contactData.email);
        cy.get('#contact_telephone').type(contactData.phone);

        if (userType === 'client') {
            cy.get('#contact_birthday_3i').select(randomDay.toString());
            cy.get('#contact_birthday_2i').select(randomMonth.toString());
            cy.get('#contact_event_book_freq').select('Monthly');
            cy.get('#contact_location_id').select('Little Cottage, Crafty Valley');
        }

        cy.get('input[value="Create Contact"]').click();


        cy.get('body').then(($body) => {
            const alert = $body.find('#new_contact > .alert');
            if (alert.length > 0) {
                cy.wrap(alert).invoke('text').then((text) => {
                    throw new Error(`Contact creation failed: ${text.trim()}`);
                });
            }
        });

        if (userType === 'client') {
            cy.contains('a', 'Promote to User').click();
            cy.get('div[role="dialog"]').should('be.visible');

            cy.get('#user_role').select('MANAGER');
            cy.get('#user_app_enabled').check();
            cy.get('#user_con_enabled').check();
            cy.get('input[value="Promote"]').click();
        }
    }
}

module.exports = createUser;