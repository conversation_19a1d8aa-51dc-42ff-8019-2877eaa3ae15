import { faker } from '@faker-js/faker';
import dayjs from 'dayjs';


function generateBookingDetails() {
    return {
        firstName: faker.name.firstName(),
        lastName: faker.name.lastName(),
        email: faker.internet.email(),
        phone: faker.phone.phoneNumber('7#########'),
    };
}


/**
 * Fills in the booking form details for an adult booking flow using Cypress.
 *
 * This function:
 * - Generates booking details and selects a start and end date (1 day from now, for 5 nights).
 * - Handles date selection, including navigating to the next month if the end date crosses a month boundary.
 * - Fills in guest and company details using generated/faker data.
 * - Copies guest details to company fields via UI interaction.
 * - Selects a payment method from a dropdown (works around tricky dropdown UI).
 * - Checks the terms and conditions checkbox and submits the form.
 *
 * Assumes:
 * - `generateBookingDetails` and `faker` are available in scope.
 * - Uses `dayjs` for date manipulation.
 * - Relies on specific data-cy and aria-label selectors in the DOM.
 *
 * @function
 */
function fillInFormDetails() {
    const bookingDetails = generateBookingDetails();
    const startDate = dayjs().add(1, 'day');
    const endDate = startDate.add(5, 'day');

    const formattedStartDate = startDate.format('DD/MM/YYYY');
    const formattedEndDate = endDate.format('DD/MM/YYYY');

    //datepicker/guest numbers
    cy.get('[data-cy="number-of-guests"]').clear().type('1');
    cy.get('[data-cy="date"]').click();
    cy.get('[class="q-date__main col column"]').should('be.visible');
    
    // Select start date
    cy.get('.q-date__calendar-days').contains('[class="block"]', startDate.date().toString()).click();

    // If end date is in a different month, navigate to next month
    if (endDate.month() !== startDate.month()) {
        cy.get('[aria-label="Next month"]').click();
    }

    // Select end date
    cy.get('.q-date__calendar-days').contains('[class="block"]', endDate.date().toString()).click();

    cy.get('[data-cy="hotel-book-btn"]').first().should('contain.text', 'Book').click();

    //next page should have the dates displayed
    cy.get('p').should('contain', formattedStartDate).and('contain', formattedEndDate);


    //fields to fill in
    cy.get('[aria-label="Guest forename *"]').type(bookingDetails.firstName);
    cy.get('[aria-label="Guest surname *"]').type(bookingDetails.lastName);
    cy.get('[aria-label="Guest email *"]').type(bookingDetails.email);
    cy.get('[aria-label="Guest telephone *"]').type(bookingDetails.phone);

    //Fills in company details
    cy.get('[aria-label="Company name *"]').type(
        faker.company.name ? faker.company.name() : faker.company.companyName()
    );
    cy.get('[aria-label="Company address 1"]').type(faker.address.streetAddress());
    cy.get('[aria-label="Company address 2"]').type(faker.address.secondaryAddress());
    cy.get('[aria-label="Company postcode"]').type(faker.address.zipCode('??# #??'));
    cy.get('[aria-label="Company town"]').type(faker.address.city());
    cy.get('[aria-label="Company county"]').type(faker.address.state());

    //autofill guest details
    cy.contains('Copy from guest 1').should('not.be.disabled').click();

    //Switch statment to handle dropdown selection based on payment type
    switch(paymentType) {
        case 'BACS':
            cy.get('[class="q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"]').last().click()
            cy.get('.q-item__section').contains('BACS / Credit Card (Paid to Hotel)').should('be.visible').click();
            break;
        case 'Credit Account':
            cy.get('[aria-hidden="true"]').contains('arrow_drop_down').first().click();
            cy.wait(100); // wait for dropdown to open
            cy.get('.q-item__section').should('be.visible').click();
            cy.get('[class="block"]').contains('Assign To Company').click();
            cy.get('[class="q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"]').last().click()
            cy.get('.q-item__section').contains('Business Account').should('be.visible').click();
            cy.get('[aria-label="Credit Account Number *"]').should('be.visible').type(creditNumber);
            break;
        case 'Stripe':
            cy.get('[class="q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"]').last().click()
            cy.get('.q-item__section').contains('Card').should('be.visible').click();
            break;
        default:
            throw new Error(`Unknown payment type: ${paymentType}`);
    }

    cy.get('[role="checkbox"]').click().then(() => {
        cy.get('.q-checkbox').should('have.attr', 'aria-checked', 'true');
    });


    //click button rather than span itself
    cy.contains('Submit').should('not.be.disabled').click();
}


module.exports = fillInFormDetails;