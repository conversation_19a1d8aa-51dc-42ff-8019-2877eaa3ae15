const dayjs = require("dayjs");
const customParseFormat = require("dayjs/plugin/customParseFormat");
dayjs.extend(customParseFormat);

/**
 * Creates a booking in the application with the specified parameters.
 * Handles different booking types, including toggling EPA and subsistence-only options,
 * and selects required organization, RFQ type, and dates.
 * After creation, navigates to the booking summary and details pages, returning the booking ID.
 *
 * @param {string} bookingType - The type of booking (e.g., 'Residential', 'EPA', 'Subsistence Only').
 * @param {string} rfqType - The RFQ (Request for Quotation) type to select.
 * @param {string} checkInDay - The check-in date in a format accepted by the date picker.
 * @param {string} checkOutDay - The check-out date in a format accepted by the date picker.
 * @returns {Cypress.Chainable<string>} - A Cypress chainable that yields the created booking ID.
 *
 * @example
 * createBooking('Residential', 'Standard', '2024-06-01', '2024-06-05').then((bookingID) => {
 *   // Use bookingID for further actions
 * });
 *
 * @remarks
 * - Automatically determines if a room is required or if the booking is subsistence-only based on the booking type.
 * - Selects the first available Judicial Institution (JI) from the dropdown.
 * - Assumes that prerequisite functions like selectOrganization, selectRfqType, selectDates, handleSubsistenceOnly, and addLearner are defined elsewhere.
 */
function createBooking(bookingType, rfqType, checkInDay, checkOutDay) {
    cy.log(`Creating booking with type: ${bookingType}, RFQ type: ${rfqType}, Check-in: ${checkInDay}, Check-out: ${checkOutDay}`);

    const bookingTypeExtras = {
        type: bookingType,
        requiresRoom: bookingType.toLowerCase().includes('residential') || bookingType.toLowerCase().includes('epa'),
        subsistenceOnly: bookingType.toLowerCase().includes('subsistence only')
    };

    selectOrganization();
    selectRfqType(rfqType);
    selectDates(checkInDay, checkOutDay);

    if (bookingType.includes('EPA')) {
        cy.get('[data-cy="isEpaToggle"]').click();
    }

    if (bookingTypeExtras.subsistenceOnly) {
        handleSubsistenceOnly();
    }

    addLearner(bookingTypeExtras.requiresRoom);

    cy.get('[data-cy="selectJIs"]').parentsUntil('[tabindex="-1"]').click({ force: true });
    cy.get('[role="listbox"]').should('be.visible').within(() => {
        cy.get('[class="q-item__label"]').first().click();
    });

    cy.get('[data-cy="createBookingBtn"]').should('be.enabled').click();
    cy.get('[data-cy="view_booking_summary"]').should('be.visible').click();
    cy.url().should('include', '/acc_group_bookings/');
    cy.get('[data-cy="view_booking_details"]').should('be.visible').click();
    cy.url().should('include', '/acc_bookings/');
    return cy.url().then((url) => {
        const bookingID = url.split('/').pop();
        return cy.log(`Booking ID: ${bookingID}`).then(() => bookingID);
    });
}

function selectOrganization() {
    cy.get('body').then(($body) => {
        if ($body.find('[data-cy="selectOrg"]').is(':visible')) {
            cy.get('[data-cy="selectOrg"]').click();
            cy.get('[role="listbox"]').first().should('be.visible').within(() => {
                cy.get('[class="q-item__label"]').first().click();
            });
        }
    });
}

function selectRfqType(rfqType) {
    cy.get('[data-cy="selectRFQ"]').click();
    cy.get('[role="listbox"]').last().should('be.visible').within(() => {
        cy.get('[class="q-item__label"]').contains(rfqType).click();
    });
    cy.get('[data-cy="selectRFQ"]').children().should('contain.text', rfqType);
}

function selectDates(checkInDate, checkOutDate) {
    const currentMonth = dayjs().locale('en-gb').format('MM');
    const currentMonthText = dayjs().locale('en-gb').format('MMM');
    const formattedCheckInDate = dayjs(checkInDate, 'DD-MM-YYYY').locale('en-gb');
    const formattedCheckOutDate = dayjs(checkOutDate, 'DD-MM-YYYY').locale('en-gb');

    cy.get('[data-cy="dateBlocksPicker"]').should('be.visible').within(() => {
        if (formattedCheckInDate.format('MM') !== currentMonth || formattedCheckOutDate.format('MM') !== currentMonth) {
            if (!dayjs().isSame(formattedCheckInDate, 'month')) {
                cy.get('span').contains(currentMonthText).click();
                cy.get('.q-date__months').within(() => {
                    cy.get('span').contains(formattedCheckInDate.format('MMM')).click();
                });
            }
        }
    });

    cy.get('span').should('contain', formattedCheckInDate.format('MMM'));
    cy.get('span').contains(new RegExp(`^${formattedCheckInDate.format('D')}$`)).click();

    if (formattedCheckOutDate.isAfter(formattedCheckInDate, 'month')) {
        cy.get('i').contains('chevron_right').click();
    }

    cy.get('span').contains(new RegExp(`^${formattedCheckOutDate.format('D')}$`)).click();
}

function handleSubsistenceOnly() {
    cy.get('tbody').first().find('tr').each(($tr) => {
        const hasAccommodation = $tr.find('td:contains("Accommodation")').length > 0;
        if (hasAccommodation) {
            cy.wrap($tr).find('[role="checkbox"]').each(($checkbox) => {
                const isChecked = $checkbox.find('.q-checkbox__inner').hasClass('q-checkbox__inner--truthy');
                if (isChecked) {
                    cy.wrap($checkbox).click();
                    cy.wrap($checkbox).find('.q-checkbox__inner').should('have.class', 'q-checkbox__inner--falsy');
                }
            });
        }
    });
}

function addLearner(requiresRoom) {
    cy.get('[data-cy="addLearnerBtn"]').click();
    cy.get('[data-cy="addLearnerPopup"]').should('be.visible');
    if (requiresRoom) {
        cy.get('[data-cy="addLearnerRoom"]').parent().click();
        cy.get('[role="listbox"]').should('be.visible').within(() => {
            cy.get('[class="q-item__label"]').first().invoke('text').as('roomType');
        });
        cy.get('@roomType').then((roomType) => {
            cy.get('span').contains(roomType).click();
        });
    }
    cy.get('[data-cy="addLearnerPopup"]').should('be.visible').within(() => {
        cy.get('tbody tr').should('not.be.empty').then(($rows) => {
            const randomRowIndex = Math.floor(Math.random() * $rows.length);
            const $randomRow = $rows.eq(randomRowIndex);
            cy.wrap($randomRow).find('td').first().invoke('text').then((learnerName) => {
                cy.wrap(learnerName).as('learnerName');
                cy.wrap($randomRow).find('button').click();
            });
        });
        cy.get('button').contains('Close').click();
    });
    cy.get('@learnerName').then((learnerName) => {
        cy.get('[class="q-table"]').should('not.be.empty').and('contain', learnerName);
    });
}

module.exports = createBooking;
