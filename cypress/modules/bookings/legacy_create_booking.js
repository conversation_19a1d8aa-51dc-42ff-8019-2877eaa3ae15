function createBookingViaLegacyFlow(checkInDate, checkOutDate, isSubsistenceOnly = false, isEpa = false) {
    cy.get('#acc_booking_organisation_id').then($select => {
        // Select the first non-blank option in the dropdown
        const firstOption = $select.find('option').not('[value=""]').first();
        cy.wrap($select).select(firstOption.val());
    });

    // Select the first option with the text 'Apprentice' from the dropdown
    cy.get('#acc_booking_header_rfq_location_id').then($select => {
        const apprenticeOption = $select
            .find('option')
            .filter((i, opt) => opt.text.includes('Apprentice'))
            .first();
        if (apprenticeOption.length) {
            cy.wrap($select).select(apprenticeOption.val());
        }
    });

    cy.get('[value="Start Booking Apprentices"]').click();
    cy.url().should('include', '/acc_create_bookings');

    // Parse UK formatted dates (DD-MM-YYYY)
    const [startDate, startMonth, startYear] = checkInDate.split('-');
    const [endDate, endMonth, endYear] = checkOutDate.split('-');

    // Helper to get month name from month number
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const startMonthName = monthNames[parseInt(startMonth, 10) - 1].slice(0, 3);
    const endMonthName = monthNames[parseInt(endMonth, 10) - 1].slice(0, 3);

    // Start date picker
    cy.get('#acc_booking_header_acc_booking_blocks_attributes_0_start_date').click();
    cy.get('.datepicker-switch').first().then($switch => {
        if (
            !$switch.text().includes(startYear) ||
            !$switch.text().includes(startMonthName)
        ) {
            cy.wrap($switch).click(); // Open month/year picker
            cy.get('.datepicker-months').within(() => {
                cy.contains('span', startMonthName).click();
            });
            cy.get('#acc_booking_header_acc_booking_blocks_attributes_0_start_date').should('have.value', checkInDate);
        }
    });
    cy.get('.datepicker-days').within(() => {
        cy.contains('[class="day"]', new RegExp(`^${parseInt(startDate, 10)}$`)).click();
    });

    // End date picker
    cy.get('#acc_booking_header_acc_booking_blocks_attributes_0_end_date').click();
    cy.get('.datepicker-switch').first().then($switch => {
        if (
            !$switch.text().includes(endYear) ||
            !$switch.text().includes(endMonthName)
        ) {
            cy.wrap($switch).click(); // Open month/year picker
            cy.get('.datepicker-months').within(() => {
                cy.contains('span', endMonthName).click();
            });
            cy.get('#acc_booking_header_acc_booking_blocks_attributes_0_end_date').should('have.value', checkOutDate);
        }
    });
    cy.get('.datepicker-days').within(() => {
        cy.contains('[class="day"]', new RegExp(`^${parseInt(endDate, 10)}$`)).click();
    });

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to midnight for accurate comparison
    const checkIn = new Date(`${startYear}-${startMonth}-${startDate}`);
    if (checkIn < today) {
        cy.get('#acc_booking_header_acc_booking_blocks_attributes_0_override_past_date').check({ force: true });
    }

    // Submit the booking form
    cy.get('#new_acc_booking_header').submit();
    cy.url().should('contains', '/week_breakdown');

    // Check page has correct dates in table
    const formatToYMD = dateStr => {
        const [d, m, y] = dateStr.split('-');
        return `${y}-${m}-${d}`;
    };
    cy.get('.block > strong').should(
        'contain.text',
        `Block: ${formatToYMD(checkInDate)} - ${formatToYMD(checkOutDate)}`
    );

    // Add a learner
    cy.contains('a', 'Add Learner').click();

    if (!isSubsistenceOnly) {
        // Get the first non blank value of the dropdown
        cy.get('#rfq_learner_rfq_response_room_id').select(1);
        cy.get('#rfq_learner_rfq_business_unit_id')
            .find('option')
            .not('[value=" "]')
            .first()
            .then(function (el) {
                cy.get('#rfq_learner_rfq_business_unit_id').select(el.val());
            });
    }

    // Click search button
    cy.get('.controls > .btn').click();

    // Select the first learner regardless of id and click add
    cy.get('[id^="acc-learner-"]').then($learners => {
        const randomIndex = Math.floor(Math.random() * $learners.length);
        cy.wrap($learners[randomIndex]).find('.btn').click();
    });

    // Close the modal pop up
    cy.get('.modal-footer > .btn').click();

    if (isEpa) {
        cy.get('#acc_booking_header_save_as_epa_bookings').should('exist').check();
        cy.get('#acc_booking_header_epa_subsistence_only').should('exist');
        if (isSubsistenceOnly) {
            cy.get('#acc_booking_header_epa_subsistence_only')
                .should('be.visible')
                .should('be.enabled')
                .check({ force: true });
        }
    }

    cy.get('#select-m-f').click();
    cy.get('#create-bookings-submit').click();
    cy.url().should('contains', '/week_breakdown');
    cy.get('[data-cy="view_booking_summary"]').click();
    cy.get('[data-cy="view_booking_details"]').click();
    cy.get('[data-cy="confirmation-details"] > .well > .table > tbody > :nth-child(2) > td').then($td => {
        cy.get('h4').then($h4 => {
            if ($h4.text().includes('Hotel must confirm!')) {
                cy.get('[data-cy="hotel-confirm-booking-button"]').click();
                cy.get('.modal-body').should('be.visible');
                cy.contains('button', 'Continue').click();
            }
        });
    });
 
    if (Cypress.env('use_stripe_payment')) {
        cy.contains('a', 'Go to confirm').invoke('removeAttr', 'target').click();

        cy.url().should('include', '/acc_booking_confirmations');
        cy.get('#room-type-drop-down').find('option').not('[value=""]').first().then($option => {
            cy.get('#room-type-drop-down').select($option.val(), { force: true });
        });
        cy.get('#acc_booking_tcs_acceptance').check();
        cy.clickLinkByText('Proceed to Payment');

        //check form for error messages
        cy.get('body').then($body => {
            if ($body.find('div.alert.alert-error').length) {
                cy.get('div.alert.alert-error').find('li').each($li => {
                    // Use Cypress built-in fail
                    assert.fail(`Alert error: ${$li.text()}`);
                });
            }
        });

        cy.url().should('include', '/payment_info');
        cy.get('#acc_booking_confirmed_by').type('Test User');
    }
}

module.exports = createBookingViaLegacyFlow;