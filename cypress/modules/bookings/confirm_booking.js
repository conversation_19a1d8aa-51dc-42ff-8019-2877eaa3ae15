/**
 * Confirms a hotel booking by verifying the confirmation message, 
 * checking booking details based on the booking type, and simulating 
 * the actions a hotel user would take to process the booking.
 *
 * - Asserts that the confirmation message is displayed.
 * - Checks if the booking is "Subsistence only" based on the bookingType parameter.
 * - Switches to hotel reservations view and navigates to "My Bookings".
 * - Refreshes and reloads the bookings results to ensure the latest data is shown.
 *
 * @param {Object} bookingType - An object describing the type of booking.
 * @param {boolean} bookingType.subsistenceOnly - Indicates if the booking is for subsistence only.
 */
function confirmBooking(bookingType) {
    cy.get('h4').should('contain.text', 'Hotel must confirm!');
    cy.get('[data-cy="booking-info"] > .table').should('be.visible').within(() => {
        cy.get('tr').should(bookingType.subsistenceOnly ? 'contain.text' : 'not.contain.text', 'Subsistence only');
    });

    cy.get('[data-cy="act-as-hotel-reservations-button"]').should('be.visible').click();
    cy.get('.ap > a').should('be.visible').click();
    cy.url().should('include', '/supplier/');
    cy.clickLinkByText('My Bookings');
    cy.refreshBookingsResults();
    cy.reload();
}

module.exports = confirmBooking;