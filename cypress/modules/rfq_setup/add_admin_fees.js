//plenty of room to expand this function if business logic ever requires it
//for now this function is used to add admin fees to the RFQ, as theyre mandatory
function addAdminFees(fee) {

    const fieldsToType = [
        // Accommodation confirmation fees
        '#admin_fee_acc_conf_book_hg_uk',
        '#admin_fee_acc_conf_book_self_uk',
        '#admin_fee_acc_conf_book_self_eu',
        '#admin_fee_acc_conf_book_hg_eu',
        '#admin_fee_acc_conf_non_comm',
        '#admin_fee_acc_conf_out_of_hours',
        '#admin_fee_acc_conf_cancellation',
        '#admin_fee_acc_conf_amendment',
        // Accommodation adult fees
        '#admin_fee_acc_adult_self_book',
        '#admin_fee_acc_adult_hg_book',
        '#admin_fee_acc_adult_non_comm',
        '#admin_fee_acc_adult_out_of_hours',
        // Conference fees
        '#admin_fee_conf_self_book',
        '#admin_fee_conf_part_book',
        '#admin_fee_conf_full_book',
        // Travel fees
        '#admin_fee_trav_air_uk',
        '#admin_fee_trav_air_int',
        '#admin_fee_trav_train',
        '#admin_fee_trav_out_of_hours',
        // Apartment fees
        '#admin_fee_app_self_book',
        '#admin_fee_app_part_book',
        '#admin_fee_app_full_book',
        '#admin_fee_app_self_book_per_night',
        '#admin_fee_app_part_book_per_night',
        '#admin_fee_app_full_book_per_night',
        // Charge to fields (grouped for clarity)
        '#admin_fee_self_book_chrg_to',
        '#admin_fee_part_book_chrg_to',
        '#admin_fee_full_book_chrg_to',
        // Client fees
        '#admin_fee_client_fee_per_booking',
        '#admin_fee_client_fee_per_night',
        // Notes
        '#admin_fee_app_chrg_note'
    ];

    cy.clickLinkByText('Fees');
    cy.url().should('include', '/admin_fees');
    cy.clickLinkByText('New Admin Fee');
    cy.url().should('include', '/admin_fees/new');


    // Only the first field is active, uncomment others as needed
    fieldsToType.forEach(selector => {
        if (selector.includes('chrg_to')) {
            cy.get(selector).select('Client', { force: true });
        } else {
            cy.get(selector).type(fee.toString());
        }
    });

    cy.get('#admin_fee_expires_on').type('31-Dec-2025');
    cy.clickLinkByText('Save Fees');
    cy.get('#flash_notice').should('contain', 'Successfully created admin fees');
}

module.exports = addAdminFees;