import { faker } from '@faker-js/faker';
const { getLocationDetails } = require('./rfq_location_details');

const locationDetails = getLocationDetails();

function createRFQ(type) {
    const dateNow = new Date();
    const todayMinusOneMonth = new Date(dateNow.getFullYear(), dateNow.getMonth() - 1, dateNow.getDate()).toLocaleDateString('en-GB').replace(/\//g, '-');
    const todayPlusOneYear = new Date(dateNow.setFullYear(dateNow.getFullYear() + 1)).toLocaleDateString('en-GB').replace(/\//g, '-');

    if (['apprentice', 'adult', 'virtual'].includes(type)) {
        const isVirtual = type === 'virtual';
        cy.clickLinkByText(isVirtual ? 'Add Virtual Request' : 'Add Residential Request');
        if (isVirtual) {
            cy.get('#rfq_request_app_level_override').check();
        }
        fillCommonFields(type, todayMinusOneMonth, todayPlusOneYear);
        isVirtual ? fillVirtualFields() : fillResidentialFields(type, todayPlusOneYear);
        
    }

    if (type === 'adult') {
        cy.get('#rfq_request_new_adult_booking_flow_toggle').check();
        cy.get('#rfq_request_enable_on_account').check();
    }

    cy.get('.well > .btn').click();
    cy.get('#flash_notice').contains('Request Created');
    cy.get('#flash_notice > .close').click();

    cy.get('body').then(($body) => {
        if ($body.find('.alert-error:contains("errors prevented saving this Rfq Request")').length > 0) {
            cy.get('.alert-error ul > li').each(($li) => {
                cy.log(`RFQ creation failed: ${$li.text()}`);
            });
        }
    });

    if (type !== 'virtual') {
        fillRfqQuestions();
    }
}

function fillCommonFields(type, todayMinusOneMonth, todayPlusOneYear) {
    cy.get('#rfq_request_name').type(`${type.charAt(0).toUpperCase() + type.slice(1)} ${faker.lorem.words(1)} RFQ`);
    cy.selectFirstOption('#rfq_request_contact_id');
    cy.selectFirstOption('#rfq_request_hg_contact_id');
    cy.get('#rfq_request_description').type(faker.lorem.sentence());
    cy.get('#rfq_request_start_date').type(todayMinusOneMonth);
    cy.get('#rfq_request_end_date').click({ force: true }).type(todayPlusOneYear);

    if(type === 'adult') {
        //while not a common field i'm tying the card pay checkbox to payment method logic
        cy.get('#rfq_request_new_adult_booking_flow_toggle').should('be.visible').check();
    }

    const isStripePayment = Cypress.env('use_stripe_payment');
    const paymentMethod = isStripePayment ? (type === 'adult' ? 'Manager/ Guest to Pay Entire' : 'Manager/ Learner to Pay Entire') : 'HG Client to Pay';

    //both adult/apprentice use the same selector now
    const paymentMethodSelector = '#rfq-pay-method';



    cy.log(`Using payment method: ${paymentMethod}`);
    cy.get(paymentMethodSelector).should('be.visible').should('not.be.disabled').select(`${paymentMethod}`, { force: true,  matchCase: false });
    
    
    //wrapping card pay in a conditional to avoid errors AND for the edge case of MLPE but no card pay
    // if(Cypress.env('use_stripe_payment') && type !== 'virtual') {
    //     cy.get('#rfq_request_card_pay').should('be.visible').check();
    // }
    
}

function fillResidentialFields(type, todayPlusOneYear) {
    if (type === 'adult') {
        cy.get('#rfq_request_adult_bill_to').select('Guest to Pay');
        cy.get('#rfq_request_multi_booking').check();
        cy.get('#rfq_request_no_ji_unless_hotel_confirmed').check();
    }

    cy.get('#rfq_request_deadline_time').click({ force: true }).type('09:00');
    cy.get('body').click(0, 0);
    cy.get('.rfq_request_deadline_date > .controls > #rfq_request_deadline_date').type(todayPlusOneYear);
    cy.get('#rfq_request_days_of_stay').type('5');
    cy.get('#rfq_request_enable_non_arrivals').check();
    cy.get('#rfq_request_no_emergency_contact').check();
    cy.get('#rfq_request_reminder_email').check();
    cy.get('#rfq_request_reminder_days').type('5');

    if (type === 'apprentice') {
        fillApprenticeSpecificFields();
    }

    cy.get('#rfq_request_max_single_rooms_qty').type('20');
    cy.get('#rfq_request_room_budget').type('£5');
    cy.get('#rfq_request_late_bookings_enabled').check();
    cy.get('#rfq_request_notes').type('test text notes');

    for (let i = 0; i < 5; i++) {
        ['inc_trans', 'ex_trans', 'ro', 'fb'].forEach(option => {
            cy.get(`#rfq_request_rfq_room_options_attributes_${i}_${option}`).check({ force: true });
        });
    }

    cy.get('#rfq_request_rfq_locations_attributes_0_city_or_pcode').type(locationDetails.cityOrPcode);
    cy.get('#rfq_request_rfq_locations_attributes_0_country_id').should('be.visible').select(locationDetails.country, { force: true });
    cy.get('#rfq_request_rfq_locations_attributes_0_room_nights').type(locationDetails.roomNights);
}

function fillApprenticeSpecificFields() {
    cy.get('#rfq_request_enable_epa_bookings').check();
    cy.get('#send-ji-to-learner').check();
    cy.get('#rfq_request_zero_chg_sub').check();
    cy.get('#rfq_request_room_type').select('Twin Occupancy');
    cy.get('#rfq_request_max_twin_rooms_qty').type('10');
    cy.get('#rfq_request_subsistence_req').check();
    cy.get('#rfq_request_no_manager_email').check();
    cy.get('#rfq_request_sent_summary').check();
    cy.get('#rfq_request_single_at_twin').check();
    cy.get('#rfq_request_comp_trainer_room').check();
    cy.get('#rfq_request_rfq_locations_attributes_0_emergency_no_override').type(locationDetails.emergencyNoOverride);
    cy.get('#rfq_request_rfq_locations_attributes_0_ji_confirm_override').type(locationDetails.jiConfirmOverride);

    ['hide_payment_section', 'hide_pay_method', 'hide_emergency_contact', 'train_details_req', 'wl_hide_bank_hol', 'override_welcome'].forEach(field => {
        if (!locationDetails[field]) cy.get(`#rfq_request_rfq_locations_attributes_0_${field}`).uncheck();
    });

    cy.get('#rfq_request_rfq_locations_attributes_0_welcome_override').type(locationDetails.welcomeOverride);
    cy.get('#rfq_request_rfq_locations_attributes_0_wl_dinner_override').type(locationDetails.wlDinnerOverride);
    cy.get('#rfq_request_rfq_locations_attributes_0_wl_hotel_contact_override').type(locationDetails.wlHotelContactOverride);
    cy.get('#rfq_request_rfq_locations_attributes_0_additional_welcome_notes').type(locationDetails.additionalWelcomeNotes);
    cy.get('#rfq_request_rfq_locations_attributes_0_additional_hotel_client_sla_terms').type(locationDetails.additionalHotelClientSlaTerms);
    cy.get('#rfq_request_rfq_locations_attributes_0_additional_hotel_hg_sla_terms').type(locationDetails.additionalHotelHgSlaTerms);

    cy.get('#rfq_request_rfq_locations_attributes_0_centre_name').type(locationDetails.centreName);
    ['address1', 'address2', 'address3'].forEach((field, index) => {
        cy.get(`#rfq_request_rfq_locations_attributes_0_address_${index + 1}`).type(locationDetails[field]);
    });
    if (!locationDetails.transportReq) cy.get('#rfq_request_rfq_locations_attributes_0_transport_req').uncheck();

    cy.get('#rfq_request_rfq_locations_attributes_0_centre_town').type(locationDetails.centreTown, { force: true });
    cy.get('#rfq_request_rfq_locations_attributes_0_centre_county').type(locationDetails.centreCounty, { force: true });
    cy.get('#rfq_request_rfq_locations_attributes_0_centre_postcode').type(locationDetails.centrePostcode, { force: true });
    //I'm suspecting this is making rfq.location.id fall over sometimes as it's not always selcted
    cy.get('#rfq_request_rfq_locations_attributes_0_centre_country_id').select(locationDetails.centreCountry, { force: true });
    cy.get('#rfq_request_rfq_locations_attributes_0_centre_country_id').should('contain', locationDetails.centreCountry);
}

function fillVirtualFields() {
    cy.get('#rfq_request_no_emergency_contact').uncheck();
    cy.get('#rfq_request_reminder_email').uncheck();
    cy.get('#rfq_request_reminder_days').type('5');
    cy.get('#rfq_request_link_clicked').check();
    cy.get('#rfq_request_learner_confirm').uncheck();
    cy.get('#rfq_request_fee_per_booking').type('5', { force: true });
    cy.get('#rfq_request_admin_fee_note').type('5', { force: true });
}

function fillRfqQuestions() {
    cy.url().should('include', '/rfq_requests');
    cy.clickLinkByText('Add New Question');
    cy.url().should('include', '/rfq_questions/new');

    const questionDetails = {
        questionText: 'What is your favourite colour?',
        answerType: 'String'
    };

    cy.get('#rfq_question_question_text').type(questionDetails.questionText);
    cy.get('#rfq_question_answer_type').select(questionDetails.answerType);
    cy.get('.controls > .btn').click();
    cy.url().should('include', '/rfq_requests');
    cy.get('#flash_notice').should('contain', 'Created Question');
}

module.exports = createRFQ;