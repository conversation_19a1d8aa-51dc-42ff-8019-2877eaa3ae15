function addCreditAccount(buName, adultProgrammeName, hotelUser) {

    cy.actAs(hotelUser, 'hotel')
    cy.get('.as > a').click();
    cy.contains('a', 'My Accommodation Hub').click();
    cy.get('td').contains(adultProgrammeName).parent('tr').within(() => {
        cy.contains('a', 'Manage accounts').click();
    });
    cy.get('td').contains(buName).parent('tr').within(() => {
        cy.get('[type="text"]').type(123);
    });
    cy.get('[value="Update All"]').contains('Update All').should('be.visible').click();
    cy.get('#flash_notice').contains('Successfully updated your business account details').should('be.visible');
}

module.exports = addCreditAccount;