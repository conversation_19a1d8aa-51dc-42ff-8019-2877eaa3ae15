function setHotelUser(hotelUser, email, password) {
    password = password || 'Password123!'; // Default password if not provided

    // this is bulky as it makes the hotel user, grants permissions, sets the login and sets the stripe contact
    cy.url().should('include', '/hotels');
    cy.clickLinkByText('Contacts');
    cy.get('tbody').within(() => {
        cy.contains('td', hotelUser).should('exist').parents('tr').find('a:contains("User")').click();
    });
    cy.url().should('include', '/users/');
    cy.clickLinkByText('Edit');
    cy.url().should('include', '/edit');

    cy.get('#user_email').invoke('val').then(val => {
        email = val.trim();
    }).then(() => {
        ['#user_app_enabled', '#user_con_enabled', '#user_conf_dashboard_enabled', '#user_advanced_events'].forEach(selector => { cy.get(`${selector}`).check(); });
        cy.get('.btn').contains('Update User').click();
        cy.get('.alert').should('contain', 'User was successfully updated');
        cy.contains('a', 'Change Password').click();
        cy.url().should('include', '/passwords/edit');
        ['#user_password', '#user_password_confirmation'].forEach(selector => { cy.get(selector).type(password); });
        cy.get('input[value="Update User"]').click();
        cy.url().should('include', '/users');
        // this is a bit roundabout but essentially on /user/edit there is a link to the organisation page, in this case, the hotel page
        cy.get('span.label').contains('Organisation').parent('div').find('a[href*="hotels"]').click();
        setStripeContact(hotelUser, email, password);
    });
}

function setStripeContact(hotelUser, email, password) {
    cy.clickLinkByText('Show');
    cy.get('#hotel_stripe_contact_id').select(hotelUser);
    cy.get('input[value="Set Stripe Contact"]').click();
    cy.get('p').should('contain', 'Only this contact will be able to connect the hotel to Stripe');
    cy.contains('a', 'Logout').click();
    cy.get('a[href="/users/sign_in"]').click();
    cy.get('#user_email').type(email);
    cy.get('#user_password').type(password);
    cy.get('input[value="Login"]').click();
    cy.url().should('include', '/');
    cy.get('.ap > a').click();
    cy.url().should('include', '/supplier/dashboard');
    cy.clickLinkByText('Stripe Management');
}

module.exports = setHotelUser;
