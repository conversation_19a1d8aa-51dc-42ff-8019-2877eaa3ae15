const navToHotel = require('./nav_to_hotel');

//Builds hotel if all the app health checks are complete
function buildHotel(hotelName) {
    navToHotel(hotelName);
    cy.contains('Health Check').click();
    cy.get('[class="alert alert-success"]').then(($alert) => {
      if (!$alert.text().includes('Hotel is OK')) {
        cy.contains('Build').click();
        cy.get('#flash_notice').contains('Hotel is now built and can be verified - please double check the details and verify if happy');
        cy.clickLinkByText('Save');
        cy.get('#flash_notice').contains(`Hotel '${hotelName}' updated`);
        cy.get('tr').each(($row) => {
          if ($row.text().includes(hotelName) && ($row.text().includes('NOT COMPLETE') || $row.text().includes('DISABLED'))) {
            cy.log('Hotel is not complete or is disabled');
            cy.fail();
          }
        });
      }
    });
  }

module.exports = buildHotel;