/**
 * pay_for_stripe_booking.js
 * Cypress module to automate <PERSON><PERSON> payment with test card details.
 */

//test card = uk visa debit 
function payForStripeBooking() {

    const cardNumber = '****************';
    const expDate = '12/34';
    const cvc = '123';
    const postalCode = 'TS225HG';

    // Assumes Stripe iframe is present and visible
    cy.get('iframe')
        .first()
        .then($iframe => {
            const $body = $iframe.contents().find('body');
            cy.wrap($body).find('#Field-numberInput').type(cardNumber, { delay: 10 });
            cy.wrap($body).find('#Field-expiryInput').type(expDate, { delay: 10 });
            cy.wrap($body).find('#Field-cvcInput').type(cvc, { delay: 10 });
            cy.wrap($body).find('#Field-postalCodeInput').type(postalCode, { delay: 10 });
        });

    // Click the pay/submit button (update selector as needed)
    cy.contains('button', /pay|submit|complete/i).click();
}

module.exports = payForStripeBooking;