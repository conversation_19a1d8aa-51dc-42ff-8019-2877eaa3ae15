// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

import 'cypress-file-upload';

Cypress.Commands.add('login', (email, password) => {
    email = email || '<EMAIL>';
    password = password || 'Pa55w0rd!';
    Cypress.Cookies.debug(true);
    cy.visit(Cypress.env('login_url'));
    cy.get('#user_email').type(email);
    cy.get('#user_password').type(`${password}{enter}`);
});

Cypress.Commands.add('loginByBasicAuth', (username, password) => {
    username = username || Cypress.env('basic_auth_username');
    password = password || Cypress.env('basic_auth_password');

    const authHeader = 'Basic ' + btoa(username + ':' + password);
  
    cy.intercept('GET', '/**', (req) => {
      req.headers['Authorization'] = authHeader;
    }).as('authRequest');
  });

  Cypress.Commands.add('handleTermsAndConditions', () => {
    cy.get('#user_terms_and_conditions').check();
    cy.get('.btn').click();
    cy.get('.ap > a').click();
  });

  Cypress.Commands.add('actAs', (act_as_name, act_as_type) => {
    cy.get('#masquerade_as_name').type(act_as_name);
    cy.get('#ui-id-1').should('be.visible').click();
    cy.get('input[name="commit"]').first().click({ force: true });
    cy.get('.ap > a').click();

    if (act_as_type === 'hotel') {
      cy.url().should('include', '/supplier/dashboard');
    } else {
      cy.url().should('include', 'reports_dashboard/apprentice_summary_report');
    }
  });

Cypress.Commands.add('clickLinkByText', (text) => {
  //if working with more than one link with the same text, wrap this command in a .within() block
  cy.contains(text).click();
});

// This needs refining to be more generic as it's not always 18 q's and 2 options etc. 
// TODO: Random selection of COUNT options 
// TODO: Random Data on any type feilds
Cypress.Commands.add('bulkFillFeedback', (url, start, end) => {
    for (let i = start; i <= end; i++) {
      cy.visit(url);
  
      cy.get('#survey_response_name').type('Test Learner ' + i);
      cy.get('#survey_response_email').type('test.learner' + i + '@test.com');
  
      for (let j = 0; j < 10; j++) {
        const randomOption = Math.floor(Math.random() * 2) + 1;
        cy.get(`#survey_response_answers_attributes_${j}_chosen_option_${randomOption}`).click();
      }
  
      cy.get('#survey_response_answers_attributes_10_comments').type('No');
  
      for (let j = 11; j < 18; j++) {
        const randomOption = Math.floor(Math.random() * 2) + 1;
        cy.get(`#survey_response_answers_attributes_${j}_chosen_option_${randomOption}`).click();
      }
  
      cy.get('#survey_response_answers_attributes_18_comments').type('No');
  
      cy.get('#submit-responses-btn').click();
    }
  });

Cypress.Commands.add('selectFirstOption', (selector) => {
  cy.get(selector).then($select => {
    const val = $select.find('option').not(':empty').first().val();
    cy.get(selector).select(val);
  });
});

Cypress.Commands.add('openWindow', (url, features) => {
  const w = Cypress.config('viewportWidth')
  const h = Cypress.config('viewportHeight')
  if (!features) {
    features = `width=${w}, height=${h}`
  }
  console.log('openWindow %s "%s"', url, features)

  return new Promise(resolve => {
    if (window.top.aut) {
      console.log('window exists already')
      window.top.aut.close()
    }
    // https://developer.mozilla.org/en-US/docs/Web/API/Window/open
    window.top.aut = window.top.open(url, 'aut', features)

    // letting page enough time to load and set "document.domain = localhost"
    // so we can access it
    setTimeout(() => {
      cy.state('document', window.top.aut.document)
      cy.state('window', window.top.aut)
      resolve()
    }, 500)
  })
})

Cypress.Commands.add('clickThroughModal', () => {
  cy.get('[class="modal-dialog"]').should('be.visible').within(() => {
    cy.get('.modal-footer > .btn-success').click();
    });
})

Cypress.Commands.add('interceptAndReturn', (url, response) => {
  cy.intercept(url, (req) => {
    req.reply(response);
  });
});

Cypress.Commands.add('refreshBookingsResults', () => {
  //debug check to see if we're in test when refreshing mat view
  cy.exec('echo $RAILS_ENV').then((env) => {
    cy.log(env.stdout);
  })
  cy.task("DBSERVICE", {
    dbConfig: Cypress.env("DB"),
    sql: 'REFRESH MATERIALIZED VIEW CONCURRENTLY "bookings_results";'
  }).then((result) => {
    cy.log(result);
  });
});

Cypress.Commands.add('checkTableForText', (selector, text, retries) => {

  retries = retries || 5; // default to 5 retries

  cy.get(selector).then(($table) => {
    if ($table.text().includes(text)) {
      cy.wrap($table).should('be.visible').and('contain', text);
    } else if (retries > 0) {
      cy.wait(2000); // wait for 2 seconds before retrying
      cy.reload().then(() => {
        cy.checkTableForText(selector, text, retries - 1);
      });
    }
  });
});

Cypress.Commands.add('findProgramme', (programmeName) => {
  // Function to find a programme in the list of programmes - covers paginated tables
  cy.get('body').then(($body) => {
    const programmeLink = $body.find(`a:contains(${programmeName})`);
    if (programmeLink.length > 0) {
      cy.wrap(programmeLink).click();
    }

    //If there is pagination, click through to continue searching for the programme
     else if ($body.find('.next_page').length > 0 && !$body.find('.next_page').hasClass('disabled')) { 
      cy.get('.next_page').click().then(() => {
        cy.findProgramme(programmeName);
      });
    } else {
      cy.fail(`Programme ${programmeName} not found`);
    }
  });
});

Cypress.Commands.add('checkElementVisibility', (selector, retries) => {
  cy.get('body').then(($body) => {
    if ((!$body.find(selector).length || !$body.find(selector).is(':visible')) && retries > 0) {
      cy.wait(5000); // wait for 5 seconds before retrying
      cy.reload();
      return cy.checkElementVisibility(selector, retries - 1);
    } else {
      cy.get(selector).should('be.visible');
    }
  });
});

Cypress.Commands.add('stopActingAsUser',() => {
  // Stop masquerading as user
  cy.get('a[data-method="delete"][href="/admin/masquerades"]').click();
  cy.url().should('include', '/admin/dashboard');
});

Cypress.Commands.add('clickButtonInRowWithText', (rowText, selector) => {
  //pass in some text of interest and the selector to search for it in
  //this function is for tables where each bit of data is within a <tr>
  cy.get('tr').contains(rowText).parent().within(() => {
    cy.contains(selector).should('be.visible').then(($button) => {
      cy.wrap($button).click();
    });
  });
});

Cypress.Commands.add('navigateToManageTasks', (userType, user, programmeName, programmeType) => {
  cy.actAs(user, userType);

  //this has become a little messy but I have to account for the differences in the three views.
  const isHotel = userType.includes('hotel');
  const isClient = userType.includes('client');
  const isAdult = programmeType.toLowerCase().includes('adult');

  if (isHotel && isAdult) {
    cy.clickLinkByText('Accommodation Hub');
    cy.url().should('include', 'supplier/dashboard/acc_stop');
    cy.clickLinkByText('My Accommodation Hub');
  }

  if (isClient) {
    cy.clickLinkByText('Programme Management');
    if (isAdult) {
      cy.clickLinkByText('Accommodation Hub');
      cy.get('tbody').within(() => {
        cy.get('tr').first().find('td').first().find('a[href]').first().click();
        cy.url().should('include', '/client/adult_rfq_tasks');
      });
      return;
    }
  }

  const buttonText = userType === 'hotel' ? 'Manage Tasks' : 'ID:';
  cy.clickButtonInRowWithText(programmeName, buttonText);
});