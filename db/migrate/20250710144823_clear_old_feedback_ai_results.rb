class ClearOldFeedbackAiResults < ActiveRecord::Migration[8.0]
  def up
    # Clear all existing FeedbackAiResult records to prevent JSON structure issues
    # These will be regenerated with the updated, consistent field names
    say "Clearing #{FeedbackAiResult.count} old feedback AI analysis results..."
    FeedbackAiResult.delete_all
    say "Old feedback AI results cleared. Fresh analysis will be generated on next request."
  end

  def down
    # Cannot restore deleted records, but this is acceptable since these are generated data
    say "Cannot restore deleted feedback AI results (they are generated data that will be recreated)"
  end
end
