class UpdateNullCancellationReasons < ActiveRecord::Migration[8.0]
  def up
    # Check if the table exists before attempting migration
    return unless table_exists?(:acc_bookings)
    
    # Check if the column exists before attempting to update
    return unless column_exists?(:acc_bookings, :cancellation_reason)
    
    # Update records where cancellation_reason is null or empty string
    # BUT only for bookings that are actually cancelled (have cancelled_at or cancelled_by)
    # Using sanitized SQL to prevent injection and handle edge cases
    execute <<-SQL.squish
      UPDATE acc_bookings 
      SET cancellation_reason = 'Not required'
      WHERE (cancellation_reason IS NULL 
             OR TRIM(cancellation_reason) = ''
             OR cancellation_reason = ' ')
        AND (cancelled_at IS NOT NULL OR cancelled_by IS NOT NULL)
    SQL
    
    # Log the number of affected rows for debugging
    affected_rows = connection.select_value(<<-SQL.squish)
      SELECT COUNT(*) 
      FROM acc_bookings 
      WHERE cancellation_reason = 'Not required'
        AND (cancelled_at IS NOT NULL OR cancelled_by IS NOT NULL)
    SQL
    
    Rails.logger.info "Updated #{affected_rows} acc_bookings records with 'Not required' cancellation reason"
  end

  def down
    # Check if the table and column exist before attempting rollback
    return unless table_exists?(:acc_bookings)
    return unless column_exists?(:acc_bookings, :cancellation_reason)
    
    # Revert only the records we specifically updated in the up method
    # Only revert cancelled bookings that we set to 'Not required'
    # Set them back to NULL instead of empty string to maintain data integrity
    execute <<-SQL.squish
      UPDATE acc_bookings 
      SET cancellation_reason = NULL
      WHERE cancellation_reason = 'Not required'
        AND (cancelled_at IS NOT NULL OR cancelled_by IS NOT NULL)
    SQL
    
    # Log the rollback action
    affected_rows = connection.select_value(<<-SQL.squish)
      SELECT COUNT(*) 
      FROM acc_bookings 
      WHERE cancellation_reason IS NULL
        AND (cancelled_at IS NOT NULL OR cancelled_by IS NOT NULL)
    SQL
    
    Rails.logger.info "Rolled back migration: #{affected_rows} acc_bookings records now have NULL cancellation reason"
  end
end
