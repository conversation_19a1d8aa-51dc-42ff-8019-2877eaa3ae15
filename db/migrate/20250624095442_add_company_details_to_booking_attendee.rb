class AddCompanyDetailsToBookingAttendee < ActiveRecord::Migration[8.0]
  def change
    add_column :booking_attendees, :company_name, :string, null: true
    add_column :booking_attendees, :company_address_1, :text, null: true
    add_column :booking_attendees, :company_address_2, :text, null: true
    add_column :booking_attendees, :company_postcode, :text, null: true
    add_column :booking_attendees, :company_town, :string, null: true
    add_column :booking_attendees, :company_county, :string, null: true
    add_reference :booking_attendees, :rfq_business_unit, foreign_key: { to_table: :rfq_business_units }, null: true

    add_column :bookings, :booker_company_name, :string, null: true
    add_column :bookings, :booker_company_address_1, :text, null: true
    add_column :bookings, :booker_company_address_2, :text, null: true
    add_column :bookings, :booker_company_postcode, :text, null: true
    add_column :bookings, :booker_company_town, :string, null: true
    add_column :bookings, :booker_company_county, :string, null: true
    add_reference :bookings, :booker_rfq_business_unit, foreign_key: { to_table: :rfq_business_units }, null: true


    booking_attendees = BookingAttendee.joins(:acc_booking_person).where.not(acc_booking_person: nil)
    booking_attendees.each do |booking_attendee|
      acc_booking_person = booking_attendee.acc_booking_person
      booking_attendee.update(company_name: acc_booking_person.booker_company,
                              company_address_1: acc_booking_person.address_1,
                              company_address_2: acc_booking_person.address_2,
                              company_postcode: acc_booking_person.postcode,
                              company_town: acc_booking_person.town,
                              company_county: acc_booking_person.county,
                              rfq_business_unit_id: acc_booking_person.rfq_business_unit_id)
    end
  end

  
end
