class UpdateExistingBookingsSurveyCompletedAt < ActiveRecord::Migration[6.1]
  def up
    # Update existing acc_bookings with survey_completed_at from their associated survey_responses
    execute <<-SQL
      UPDATE acc_bookings 
      SET survey_completed_at = survey_responses.completed_at
      FROM survey_responses 
      WHERE survey_responses.booking_id = acc_bookings.id 
        AND survey_responses.completed_at IS NOT NULL
        AND acc_bookings.survey_completed_at IS NULL
    SQL
  end

  def down
    # Clear all survey_completed_at timestamps
    execute <<-SQL
      UPDATE acc_bookings 
      SET survey_completed_at = NULL
    SQL
  end
end
