SELECT 
              uuid_generate_v4() as uniq_id,
              acc_bookings.id
              AS
              id,
              acc_booking_stays.id AS uid,
              hotels.NAME
              AS hotel_name,
              locations.city
              AS hotel_city,
              locations.county
              AS hotel_county,
              contacts.first_name
              AS booker_first_name,
              contacts.surname
              AS booker_surname,
              contacts.email
              AS booker_email,
              acc_bookings.hotel_id
              AS hotel_id,
              acc_bookings.hotel_name
              AS hotel_name_conferma,
              acc_bookings.room_type
              AS room_type,
              acc_bookings.single_reason
              AS single_reason,
              acc_bookings.pkg_type
              AS pkg_type,
              acc_bookings.transport_included
              AS transport_included,
              acc_bookings.special_requirements
              AS special_requirements,
              acc_bookings.rooming_preference
              AS rooming_preference,
              acc_bookings.pppn_inc_vat
              AS pppn_inc_vat,
              acc_booking_stays.check_in
              AS first_checkin,
              acc_booking_stays.check_out
              AS last_check_out,
              (acc_booking_stays.check_out - acc_booking_stays.check_in)
              AS total_nights,
              acc_bookings.subsistence_only
              AS subsistence_only,
              acc_bookings.cancelled_at
              AS cancelled_at,
              acc_bookings.cancelled_by
              AS cancelled_by,
              acc_bookings.cancellation_reason
              AS cancellation_reason,
              acc_bookings.confirmed_at
              AS confirmed_at,
              acc_bookings.confirmed_flag,
              acc_bookings.confirmed_by
              AS confirmed_by,
              acc_bookings.card_pay
              AS card_pay,
              acc_bookings.amended_at
              AS amended_at,
              acc_bookings.amended_by
              AS amended_by,
              acc_bookings.last_early_arrival_at
              AS last_early_arrival_at,
              acc_bookings.last_non_arrival_at
              AS last_non_arrival_at,
              acc_bookings.hg_chase_email
              AS hg_chase_email,
              acc_bookings.hg_chase_phone
              AS hg_chase_phone,
              acc_bookings.subsistence_pppn
              AS subsistence_pppn,
              acc_bookings.cost_code
              AS booking_cost_code,
              acc_bookings.hotel_confirmed_at
              AS hotel_confirmed_at,
              acc_bookings.hg_note
              AS hg_note,
              acc_bookings.client_note,
              acc_bookings.epa_booking,
              acc_booking_stays.check_in
              AS stay_check_in,
              acc_booking_stays.check_out
              AS stay_check_out,
              acc_bookings.subsistence_days 
              AS stay_sub_days,
              acc_booking_headers.id
              AS booking_ref,
              acc_booking_headers.confirm_token
              AS abh_confirm_token,
              acc_booking_headers.payment_method
              AS abh_payment_method,
              acc_booking_headers.multi_modules_selected
              AS abh_multi_modules_selected,
              acc_booking_headers.adult_subsistence_allowed
              AS abh_subsistence_allowed,
              acc_booking_headers.adult_sub_food
              AS abh_sub_food,
              acc_booking_headers.adult_sub_drink
              AS abh_sub_drink,
              acc_booking_headers.adult_bill_to
              AS abh_bill_to,
              acc_booking_headers.client_note
              AS block_client_note,
              acc_booking_headers.booking_type
              AS abh_book_type,
              acc_booking_people.person_type
              AS person_type,
              acc_bookings.created_at,
              acc_bookings.resolved_at,
              acc_bookings.resolved_by,
              acc_bookings.train_details_req,
              acc_bookings.from_station,
              acc_bookings.single_as_twin,
              acc_bookings.acc_booking_person_id,
              acc_bookings.virtual_flag,
              acc_bookings.nice_vslot,
              acc_bookings.joining_instruction_id,
              acc_booking_people.forename
              AS s_forename,
              acc_booking_people.surname
              AS s_surname,
              acc_booking_people.email
              AS s_email,
              acc_booking_people.telephone
              AS s_telephone,
              rfq_business_units.NAME
              AS bunit_name,
                rfq_business_units.id
              AS bunit_id,
              rfq_programmes.id
              AS prog_id,
              rfq_programmes.NAME
              AS prog_name,
              rfq_programmes.rfq_type
              AS rfq_type,
              rfq_learners.manager
              AS manager,
              rfq_learners.manager_email
              AS manager_email,
              rfq_learners.manager_telephone
              AS manager_telephone,
              rfq_learners.date_of_birth
              AS dob,
              rfq_learners.forename
              AS l_forename,
              rfq_learners.middlename
              AS l_middlename,
              rfq_learners.surname
              AS l_surname,
              rfq_learners.gender
              AS l_gender,
              rfq_learners.adult
              AS l_adult,
              rfq_learners.email
              AS l_email,
              rfq_learners.telephone
              AS l_telephone,
              rfq_learners.programme_name
              AS l_programme_name,
              rfq_learners.start_date
              AS l_start_date,
              rfq_learners.proposed_end_date
              AS l_proposed_end_date,
              rfq_learners.course_code
              AS l_course_code,
              rfq_learners.additional_information
              AS l_additional_information,
              rfq_learners.employer_name
              AS l_employer_name,
              rfq_learners.employer_contact_email
              AS l_employer_contact_email,
              rfq_learners.employer_contact_number
              AS l_employer_contact_number,
              rfq_learners.levy
              AS l_levy,
              rfq_learners.reference
              AS l_reference,
              rfq_learners.id
              AS lid,
              rfq_learners.left_at
              AS l_left_at,
              rfq_learners.client_hidden
              AS l_client_hidden,
              acc_booking_people.learner_group_code
              AS group_code,
              rfq_trainers.forename
              AS t_forename,
              rfq_trainers.middlename
              AS t_middlename,
              rfq_trainers.surname
              AS t_surname,
              rfq_trainers.email
              AS t_email,
              rfq_trainers.telephone
              AS t_telephone,
              rfq_trainers.left_at
              AS t_left_at,
              rfq_requests.link_clicked
              AS rfq_link_clicked,
              rfq_requests.NAME AS rfq_name,
              rfq_requests.mode AS rfq_mode,
              rfq_requests.lost_at as rfq_lost_at,
              rfq_responses.late_booking_threshold AS response_late_booking_threshold,
              acc_bookings.parking_required AS booking_parking_required,
              rfq_responses.parking_charge AS response_parking_charge,
              rfq_requests.late_bookings_enabled AS rfq_late_bookings_enabled,
              COALESCE(rfq_training_centres.centre_name,
              rfq_locations.centre_name)         AS
              centre_name,
              rfq_locations.subsistence_provider
                   AS subsistence_provider,
              COALESCE(rfq_training_centres.centre_postcode,
              rfq_locations.centre_postcode) AS
              centre_postcode,
              acc_bookings.payment_method
              AS book_payment_method,
              acc_bookings.account_number
              AS book_account_number,
              acc_bookings.complimentary
              AS book_comp,
              acc_bookings.link_clicked_at,
              acc_bookings.late_booking_threshold,
              joining_instructions.title AS joining_instructions_title,
              rfq_responses.commission AS commission,
              organisations.NAME AS client_name,
              organisations.id AS client_id,
              rfq_proposed_hotels.id AS rfq_proposed_hotel_id,
              acc_bookings.hotel_declined_by_id AS hotel_declined_by_id,
              acc_bookings.hotel_declined_at,
              rfq_bu_accounts.payment_method AS credit_account_payment_method,
              acc_bookings.reservation_number AS reservation_number     
FROM   acc_bookings
       INNER JOIN acc_booking_stays
               ON acc_booking_stays.acc_booking_id = acc_bookings.id
               AND acc_booking_stays.deleted_at IS NULL
       INNER JOIN acc_booking_people
               ON acc_booking_people.id = acc_bookings.acc_booking_person_id
			   AND acc_booking_people.deleted_at IS NULL
       INNER JOIN acc_booking_headers
               ON acc_booking_headers.id = acc_bookings.acc_booking_header_id
			   AND acc_booking_headers.deleted_at IS NULL
       INNER JOIN contacts
               ON contacts.deleted_at IS NULL
               AND contacts.id = acc_booking_headers.booker_id
       INNER JOIN rfq_locations
               ON rfq_locations.id = acc_booking_headers.rfq_location_id
       INNER JOIN rfq_requests
               ON rfq_requests.id = rfq_locations.rfq_request_id
       INNER JOIN rfq_programmes
               ON rfq_programmes.deleted_at IS NULL
                  AND rfq_programmes.id = rfq_requests.rfq_programme_id
       INNER JOIN organisations
               ON organisations.deleted_at IS NULL
                  AND organisations.id = rfq_programmes.client_id
       LEFT JOIN rfq_training_centres
              ON rfq_training_centres.id =
                 acc_booking_headers.rfq_training_centre_id
       LEFT JOIN rfq_responses
              ON rfq_responses.id = rfq_locations.accepted_response_id
       LEFT JOIN joining_instructions
              ON acc_booking_headers.joining_instruction_id =
                 joining_instructions.id
                 AND joining_instructions.deleted_at IS NULL
       LEFT JOIN rfq_learners
              ON rfq_learners.id = acc_booking_people.rfq_learner_id
       LEFT JOIN rfq_trainers
              ON rfq_trainers.id = acc_booking_people.rfq_trainer_id
       LEFT JOIN rfq_business_units
              ON rfq_learners.rfq_business_unit_id = rfq_business_units.id
       LEFT JOIN hotels
              ON hotels.id = acc_bookings.hotel_id
       LEFT JOIN rfq_response_rooms
              ON rfq_response_rooms.id = acc_bookings.rfq_response_room_id
       LEFT JOIN locations
              ON hotels.primary_location_id = locations.id 
       LEFT JOIN rfq_proposed_hotels 
              ON rfq_proposed_hotels.id = rfq_responses.rfq_proposed_hotel_id
       LEFT JOIN rfq_bu_accounts 
              ON rfq_bu_accounts.rfq_business_unit_id = rfq_business_units.id AND rfq_bu_accounts.rfq_response_id = rfq_responses.id
                     AND rfq_bu_accounts.deleted_at IS NULL
              
        where acc_bookings.first_checkin >= (current_date - interval '2' year)